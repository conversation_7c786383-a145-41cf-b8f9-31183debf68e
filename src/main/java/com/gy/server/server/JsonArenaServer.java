package com.gy.server.server;

import com.gy.server.game.server.GameArea;

public class JsonArenaServer {
    public String name;
    public int number;
    public int status;
    public int fluencyStatus;
    public transient String keyword;
    public transient int regist;

    public JsonArenaServer() {
    }

    public JsonArenaServer(GameArea area) {
        this.number = area.getAreaNumber();
        this.name = area.getName();
        this.status = area.getStatus().ordinal();
        this.fluencyStatus = area.getFluencyStatus().ordinal();
        this.keyword = area.getKeyword();
        this.regist = area.getRegistCount();
    }
}
