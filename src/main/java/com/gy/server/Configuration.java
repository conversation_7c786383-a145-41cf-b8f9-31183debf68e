package com.gy.server;

import java.io.File;
import java.io.FileNotFoundException;

import com.gy.server.utils.EmbeddedLogger;
import com.ttlike.server.tl.baselib.CommonsConfiguration;
import org.dom4j.Element;

import com.gy.server.message.MessageLanguage;
import com.gy.server.utils.XmlUtil;

import java.net.URL;
import java.util.Optional;
import java.util.Properties;

/* 
 * <AUTHOR> 
 * @date 2019年9月7日 下午2:29:40 
 */
public class Configuration {

    public final static String CONFIG_FILE_NAME = "config.xml";
    public static Properties hibernateSessionProperties = new Properties();
    public static final String DEFAULT_GAME_ID = "default";

    public static MessageLanguage language;
    
    public static String gameId = DEFAULT_GAME_ID;

    public static boolean openActiveCode = false;


    public static boolean isTest() {
        return CommonsConfiguration.runMode.isTest();
    }

    public static boolean isOpenActiveCode() {
        return openActiveCode;
    }

    public static void init() throws Exception {
        if(CommonsConfiguration.isDocker()){
            initEvn();
        }else{
            initXml();
        }

        printConfig();
    }

    public static void printConfig() {
        EmbeddedLogger.info("Configuration hibernate.connection.url " + Configuration.hibernateSessionProperties.getProperty("hibernate.connection.url"));
        EmbeddedLogger.info("Configuration hibernate.connection.username " + Configuration.hibernateSessionProperties.getProperty("hibernate.connection.username"));
    }
    public static void initEvn() throws Exception {
        Configuration.hibernateSessionProperties.put("hibernate.connection.url", System.getenv("DB_URL"));
        Configuration.hibernateSessionProperties.put("hibernate.connection.username", System.getenv("DB_USERNAME"));
        Configuration.hibernateSessionProperties.put("hibernate.connection.password", System.getenv("DB_PASSWORD"));
    }
    public static void initXml() throws Exception {
        URL url = Configuration.class.getClassLoader().getResource(CONFIG_FILE_NAME);
        if (url == null || url.getPath() == null) {
            throw new FileNotFoundException("file not found -> " + CONFIG_FILE_NAME);
        }
    	try {
            File file = new File(url.getPath());
            Element rootElement = XmlUtil.loadDocumentElement(file);
            Element sessionFactoryElement = XmlUtil.getChild(rootElement, "session-factory");
            Element propertiesElement = XmlUtil.getChild(sessionFactoryElement, "propertys");
            for (Element propertyElement : XmlUtil.getChildren(propertiesElement, "property")) {
                if (propertiesElement.hasContent()) {
                    Configuration.hibernateSessionProperties.put(XmlUtil.getAttribute(propertyElement, "name"), XmlUtil.getText(propertyElement));
                }
            }
		} catch (Exception e) {
			e.printStackTrace();
			throw new FileNotFoundException("file not found -> " + url);
		}
    }
    public static MessageLanguage getLanguage() {
        return Optional.ofNullable(language).orElse(MessageLanguage.zh_CN);
    }
    public static String getGameId() {
        return gameId;
    }
}
