package com.gy.server.gate;

import java.util.Collection;
import java.util.Iterator;
import java.util.concurrent.ConcurrentHashMap;

import com.gy.server.util.ServerConstants;
import com.gy.server.annotation.MessageMethod;
import com.gy.server.annotation.MessageServiceBean;
import com.gy.server.core.MessageServerType;
import com.gy.server.core.MethodInvokeType;
import com.gy.server.core.command.CommandRequestParams;
import com.gy.server.core.command.ServerCommandRequest;
import com.gy.server.utils.EmbeddedLogger;
import com.gy.server.utils.function.Ticker;

/* 网管服务器
 * <AUTHOR> 
 * @date 2019年9月9日 上午10:37:01 
 */
@MessageServiceBean(description = "网关服务器", messageServerType = MessageServerType.billing)
public class GateManagerCommandService{
	@MessageMethod(description = "来自Gate的消息", invokeType = MethodInvokeType.async)
	private static void receiveFromGate(ServerCommandRequest request, CommandRequestParams params) {
		int nodeId = params.getParam(0);
		String ip = params.getParam(1);
		int port = params.getParam(2);
		int online = params.getParam(3);
		Gate gate = GateManager.getInstance().getGate(nodeId);
		if(gate == null) {
			EmbeddedLogger.warn("Gate not registered, nodeId = " + nodeId + ", ip = " + ip + ", port = " + port);
			return;
		}
		//更新最后一次活跃时间
		gate.setLastActiveTime(ServerConstants.getCurrentTimeMillis());
		gate.setOnline(online);
		gate.setActive(true);
	}
}
