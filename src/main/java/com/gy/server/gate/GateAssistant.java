package com.gy.server.gate;

import com.gy.server.db.relation.hibernate.HibernateUtil;
import com.gy.server.game.server.GameArea;

import java.util.ArrayList;
import java.util.List;

/**
 * GameArea数据库访问工具类
 */
public class GateAssistant {

    /**
     * 查询服务器信息
     */
    public static List<Gate> getServerList() {
        String hql = "from Gate gate";
        return HibernateUtil.query(Gate.class, hql);
    }

    public static List<Gate> getServerList(int[] ids){
        List<Gate> areas = new ArrayList<>();
        if(ids.length <= 5){
            for(int serverId : ids){
                Gate area = find(serverId);
                if(area != null){
                    areas.add(area);
                }
            }
        }else{
            List<Gate> all = GateAssistant.getServerList();
            for(int serverId : ids) {
                for (Gate area : all) {
                    if (area.getId() == serverId) {
                        areas.add(area);
                    }
                }
            }
        }
        return areas;
    }

    /**
     * 增加服务器信息
     */
    public static void addServer(Gate server) {
        HibernateUtil.save(server);
    }

    public static void updateServer(Gate server) {
        HibernateUtil.update(server);
    }

    public static void saveOrUpdate(Gate server) {
        HibernateUtil.update(server);
    }

    public static void deleteArea(Gate server) {
        HibernateUtil.delete(server);
    }

    /**
     * 分页查询
     */
    public static List<Gate> getServerByPage(int pageIndex, int pageSize) {
        String hql = "from Gate area";
        return HibernateUtil.queryPage(Gate.class, hql, pageSize, pageIndex);
    }

    public static Gate find(int id) {
        return HibernateUtil.find(Gate.class, id);
    }

    public static void addBatch(List<Gate> dataList) {
        HibernateUtil.saveBatch(dataList);
    }

    public static void updateBatch(List<Gate> dataList) {
        HibernateUtil.updateBatch(dataList);
    }

}
