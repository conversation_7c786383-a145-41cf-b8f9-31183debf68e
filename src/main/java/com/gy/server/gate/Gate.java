package com.gy.server.gate;

import com.ttlike.server.tl.baselib.serialize.gm.GmtGate;

import javax.persistence.*;
import java.io.Serializable;

/* 网关
 * <AUTHOR> 
 * @date 2019年9月9日 上午10:32:57 
 */
@Entity
@Table(name = "gate")
public class Gate {

	@Id
	@Column(name = "server_id")
	private int id;

	@Column(name = "ip")
	private String ip;

	@Column(name = "port")
	private int port;

	// 上次心跳时间
	@Transient
	private long lastActiveTime;

	@Transient
	private int online;

	@Transient
	private boolean isActive;

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public String getIp() {
		return ip;
	}

	public void setIp(String ip) {
		this.ip = ip;
	}

	public int getPort() {
		return port;
	}

	public void setPort(int port) {
		this.port = port;
	}

	public long getLastActiveTime() {
		return lastActiveTime;
	}

	public boolean isActive() {
		return isActive;
	}

	public void setActive(boolean active) {
		isActive = active;
	}

	public int getOnline() {
		return online;
	}

	public void setOnline(int online) {
		this.online = online;
	}

	public void setLastActiveTime(long lastActiveTime) {
		this.lastActiveTime = lastActiveTime;
	}

	public JsonGate toJsonGate(){
		JsonGate gate = new JsonGate();
		gate.setId(id);
		gate.setIp(ip);
		gate.setPort(port);
		gate.setOnline(this.online);
		return gate;
	}

	public GmtGate toGmtGate(){
		GmtGate gate = new GmtGate();
		gate.setId(id);
		gate.setIp(ip);
		gate.setPort(port);
		gate.setOnline(this.online);
		return gate;
	}


	public class JsonGate implements Serializable {
		private int id;
		private String ip;
		private int port;
		private transient int online;

		public int getId() {
			return id;
		}

		public void setId(int id) {
			this.id = id;
		}

		public String getIp() {
			return ip;
		}

		public void setIp(String ip) {
			this.ip = ip;
		}

		public int getPort() {
			return port;
		}

		public void setPort(int port) {
			this.port = port;
		}

		public int getOnline() {
			return online;
		}

		public void setOnline(int online) {
			this.online = online;
		}
	}

}
