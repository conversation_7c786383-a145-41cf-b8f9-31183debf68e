package com.gy.server.gate;

import com.gy.server.game.server.GameArea;
import com.gy.server.game.server.GameAreaAssistant;
import com.gy.server.game.server.GameAreaManager;
import com.gy.server.util.ServerConstants;
import com.gy.server.utils.function.Ticker;
import com.gy.server.utils.runner.Runner;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * @program: tl-billing
 * @description: gate管理器
 * @author: <PERSON>.<PERSON>
 * @create: 2025/7/10
 **/
public class GateManager  implements Ticker, Runner {

    private static Logger logger = LogManager.getLogger(GateManager.class);

    private final static GateManager instance = new GateManager();
    private final static long DISCONNECT_TIME = 10 * 1000L;// 断开连接时间
    private final static long CHECK_CONNECT_INTERVAL = 10 * 1000L;

    private static Map<Integer, Gate> gates = new ConcurrentHashMap<>();

    private long lastCheckConnectTime;

    public static GateManager getInstance() {
        return instance;
    }

    @Override
    public void tick() {
        checkGateConnect();
    }

    /**
     * 检查那些未建立的连接，重新连接，有时间间隔限制
     */
    private void checkGateConnect() {
        long now = ServerConstants.getCurrentTimeMillis();
        if (now - lastCheckConnectTime < CHECK_CONNECT_INTERVAL) {
            return;
        }
        lastCheckConnectTime = now;
        Iterator<Gate> itr = gates.values().iterator();
        while (itr.hasNext()) {
            Gate gate = itr.next();
            if (gate.getLastActiveTime() + DISCONNECT_TIME <= now) {
                gate.setActive(false);
            }
        }
    }

    //当前活跃的网关服务器列表
    public Collection<Gate> getGates() {
        return gates.values().stream().filter(gate -> gate.isActive()).collect(Collectors.toList());
    }
    //根据节点id取到Gate，如果不存在或者该gate已经不活跃，则会返回null
    public Gate getGate(int id) {
        return gates.get(id);
    }

    @Override
    public void runnerExecute() throws Exception {
        updateFromDb();
    }

    @Override
    public long getRunnerInterval() {
        return 5000L;
    }

    public static void updateFromDb() {
        try {
            List<Gate> serverList = GateAssistant.getServerList();
            Set<Integer> serverNumSet = new HashSet<>();
            for (Gate server : serverList) {
                serverNumSet.add(server.getId());
                Gate oldServer = gates.get(server.getId());
                if (oldServer == null) {
                    gates.put(server.getId(), server);
                } else {
                    oldServer.setIp(server.getIp());
                    oldServer.setPort(server.getPort());
                }
            }

            gates.keySet().stream()
                    .filter(integer -> !serverNumSet.contains(integer))
                    .forEach(integer -> gates.remove(integer));
        } catch (Exception e) {
            logger.catching(e);
        }
    }
}
