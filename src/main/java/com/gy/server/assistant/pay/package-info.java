/**
 * <pre>
 *     充值模块，包括订单Receipt、支付渠道PayChannel、支付方法助手PayAssistant，支付拉取数据PayPullData等
 *
 *     1.Receipt表要求各字段均不能为Null，这么设计的原因是为了强制要求接入SDK时能够想办法获取到对应的值，保证支付逻辑的正常运行
 *
 *          orderId：订单号，必须赋值正确，gs在获得拉取信息后会通过该字段找其对应的Receipt对象
 *
 *          coOrderId：第三方订单号，必须赋值正确，其与orderId一起建立了我们的一条订单与第三方一条订单的关联关系，用作后续查询用
 *
 *          playerId：玩家id，必须赋值正确，影响gs给具体玩家发货
 *
 *          channel：充值渠道，必须赋值正确，涉及到进行订单校验时取不同马甲包的参数等，建议通过透传参数传入
 *
 *          price：价格，是真实价格x100后的结果，必须赋值正确，用于gs发货时匹配价格
 *
 *          serverNum：区服id，必须赋值正确，用于充值信息拉取时获得指定区服的所有待发货订单，建议通过透传参数传入
 *
 *
 *
 *          accountId：玩家账号，尽量赋值正确，如实在无法获取可赋值一个有意义的字符串，主要用于方便在billing查询某个账号的订单情况
 *
 *          goodsId：商品id，尽量赋值正确，用于在billing查询指定商品的购买信息，建议通过透传参数传入
 *
 *          coGoodsId：第三方商品id，尽量赋值正确，用于在billing查询指定商品的购买信息，建议通过透传参数传入
 *
 *
 *
 *          status：状态，相关操作后注意修改其值
 *
 *          goldCount：平台指定加钻石数量，主要用于有特殊需求的渠道
 *
 *          createTime：订单创建时间，默认创建时赋值，无需修改
 *
 *          updateTime：订单修改时间，涉及订单修改时尽量修改该值为当前时间
 *
 *          extendText：扩展文本，用于特殊需求
 *
 * </pre>
 */
package com.gy.server.assistant.pay;