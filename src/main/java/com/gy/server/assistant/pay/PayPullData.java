package com.gy.server.assistant.pay;

/**
 * 支付拉取数据
 *
 * <AUTHOR> - [Created on 2018/6/1 11:34]
 */
public class PayPullData {

    public final String orderId;
    public final String coOrderId;
    public final long playerId;
    public final String channel;
    public final int goodsId;
    public final String coGoodsId;
    public final int price;
    public final int goldCount;
    public final int serverNum;

    public PayPullData(Receipt receipt) {
        this.orderId = receipt.getOrderId();
        this.coOrderId = receipt.getCoOrderId();
        this.playerId = receipt.getPlayerId();
        this.channel = receipt.getChannel();
        this.goodsId = receipt.getGoodsId();
        this.coGoodsId = receipt.getCoGoodsId();
        this.price = receipt.getPrice();
        this.goldCount = receipt.getGoldCount();
        this.serverNum = receipt.getServerNum();
    }
}
