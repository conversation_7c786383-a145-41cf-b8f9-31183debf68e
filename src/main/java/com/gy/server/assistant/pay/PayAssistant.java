package com.gy.server.assistant.pay;

import com.gy.server.db.relation.hibernate.HibernateUtil;
import com.gy.server.utils.CollectionUtil;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> - [Created on 2018/6/1 10:23]
 */
public class PayAssistant {

    /**
     * 获取指定服务器所有已支付待发货的订单
     */
    public static List<Receipt> getReceiptsByServerNum(Collection<Integer> serverNums) {
        String hql = "from Receipt receipt where receipt.status = (:status) and receipt.serverNum in (:serverNums)";
        Map<String, Object> map = new HashMap<>();
        map.put("status", Receipt.Status.paid);
        map.put("serverNums", serverNums);
        return HibernateUtil.query(Receipt.class, hql, map);
    }

    /**
     * 根据订单号获得订单
     */
    public static Receipt getReceiptByOrderId(String orderId) {
        String hql = "from Receipt receipt where receipt.orderId = ?";
        List<Receipt> list = HibernateUtil.query(Receipt.class, hql, orderId);
        if (CollectionUtil.isNotEmpty(list)) {
            return list.get(0);
        }
        return null;
    }

    /**
     * 根据第三方订单号和充值渠道获得订单
     */
    public static Receipt getReceiptByCoOrderIdAndPayChannel(String coOrderId, String payChannel) {
        String hql = "from Receipt receipt where receipt.coOrderId = ? and receipt.channel = ?";
        List<Receipt> list = HibernateUtil.query(Receipt.class, hql, coOrderId, payChannel);
        if (CollectionUtil.isNotEmpty(list)) {
            return list.get(0);
        }
        return null;
    }

}
