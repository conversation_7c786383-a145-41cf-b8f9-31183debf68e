package com.gy.server.assistant.pay;

import com.gy.server.utils.JsonUtil;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.servlet.http.HttpServletRequest;
import java.util.Enumeration;

/**
 * <AUTHOR> - [Created on 2018/6/8 14:05]
 */
public class PayLogger {

    private static final String delimiter = "|";

    private static Logger logger = LogManager.getLogger("Pay");

    public static void logCallbackInfo(HttpServletRequest request, String callbackName) {
        StringBuilder sb = new StringBuilder("callback")
                .append(delimiter).append(callbackName)
                .append(delimiter);
        Enumeration<String> itx = request.getParameterNames();
        while (itx.hasMoreElements()) {
            String key = itx.nextElement();
            sb.append(key).append(delimiter).append(request.getParameter(key)).append(delimiter);
        }
        String text = sb.toString();
        logger.info(text.substring(0, text.length() - 1));
    }


    public static void logUpdateReceipt(String callbackName, Receipt receipt) {
        logger.info("receipt" + delimiter + callbackName + delimiter + JsonUtil.obj2Json(receipt));
    }

    public static void logCallbackResult(String callbackName, String receiptStr, String result) {
        logger.info("receipt" + delimiter + callbackName + delimiter + receiptStr + delimiter + "result:" + result);
    }
}
