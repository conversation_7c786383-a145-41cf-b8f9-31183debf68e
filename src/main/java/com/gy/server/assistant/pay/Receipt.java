package com.gy.server.assistant.pay;

import javax.persistence.*;
import java.util.Date;

/**
 * 订单对象
 *
 * <AUTHOR> - [Created on 2018/5/31 15:21]
 */
@Entity
@Table(name = "receipt")
public class Receipt {

    public enum Status {
        /**
         * 已支付，待发货
         */
        paid,

        /**
         * 已完成
         */
        finish,

        /**
         * 等待校验支付结果
         */
        waitCheck,

        /**
         * 错误
         */
        error,
    }

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "id")
    private int id;

    /**
     * 订单号
     */
    @Column(name = "order_id")
    private String orderId;

    /**
     * 第三方订单号
     */
    @Column(name = "co_order_id")
    private String coOrderId;

    /**
     * 玩家id
     */
    @Column(name = "player_id")
    private long playerId;

    /**
     * 玩家账号
     */
    @Column(name = "account_id")
    private String accountId;

    /**
     * 充值渠道
     */
    @Column(name = "channel")
    private String channel;

    /**
     * 商品id
     */
    @Column(name = "goods_id")
    private int goodsId;

    /**
     * 第三方商品id
     */
    @Column(name = "co_goods_id")
    private String coGoodsId;

    /**
     * 价格，是真实价格x100后的结果
     */
    @Column(name = "price")
    private int price;

    /**
     * 订单状态，默认订单已支付完成，待发货
     */
    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    private Status status = Status.paid;

    /**
     * 平台指定加钻石数量
     */
    @Column(name = "gold_count")
    private int goldCount;

    /**
     * 区服id
     */
    @Column(name = "server_num")
    private int serverNum;

    /**
     * 订单创建时间
     */
    @Column(name = "create_time")
    private Date createTime = new Date();

    /**
     * 订单修改时间
     */
    @Column(name = "update_time")
    private Date updateTime = new Date();

    /**
     * 扩展文本
     */
    @Column(name = "extend_text")
    private String extendText;

    public Receipt() {

    }

    public Receipt(String orderId, String coOrderId, long playerId, String accountId,
                   String channel, int goodsId, String coGoodsId, int price, int serverNum) {
        this.orderId = orderId;
        this.coOrderId = coOrderId;
        this.playerId = playerId;
        this.accountId = accountId;
        this.channel = channel;
        this.goodsId = goodsId;
        this.coGoodsId = coGoodsId;
        this.price = price;
        this.serverNum = serverNum;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getCoOrderId() {
        return coOrderId;
    }

    public void setCoOrderId(String coOrderId) {
        this.coOrderId = coOrderId;
    }

    public long getPlayerId() {
        return playerId;
    }

    public void setPlayerId(long playerId) {
        this.playerId = playerId;
    }

    public String getAccountId() {
        return accountId;
    }

    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public int getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(int goodsId) {
        this.goodsId = goodsId;
    }

    public String getCoGoodsId() {
        return coGoodsId;
    }

    public void setCoGoodsId(String coGoodsId) {
        this.coGoodsId = coGoodsId;
    }

    public int getPrice() {
        return price;
    }

    public void setPrice(int price) {
        this.price = price;
    }

    public Status getStatus() {
        return status;
    }

    public void setStatus(Status status) {
        this.status = status;
    }

    public int getGoldCount() {
        return goldCount;
    }

    public void setGoldCount(int goldCount) {
        this.goldCount = goldCount;
    }

    public int getServerNum() {
        return serverNum;
    }

    public void setServerNum(int serverNum) {
        this.serverNum = serverNum;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getExtendText() {
        return extendText;
    }

    public void setExtendText(String extendText) {
        this.extendText = extendText;
    }
}
