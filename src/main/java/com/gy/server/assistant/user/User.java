package com.gy.server.assistant.user;

import java.util.HashSet;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

import com.gy.server.utils.CollectionUtil;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR> - [Created on 2018/5/3 20:05]
 */
@Entity
@Table(name = "user")
public class User {

    /**
     * 超级管理员用户名
     */
    public static final String ADMIN = "admin";

    @Id
    @Column(name = "user_name")
    private String username;

    @Column(name = "password")
    private String password;

    @Column(name = "authority")
    private String authority;

    @Column(name = "authority_ids")
    private String authorityIds;

    public User() {

    }

    public User(String username, String password, String authority, String authorityIds) {
        this.username = username;
        this.password = password;
        this.authority = authority;
        this.authorityIds = authorityIds;
    }

    /**
     * 获取权限列表
     */
    public Set<String> getAuthoritys() {
        if (!StringUtils.equals(authorityIds, "")) {
            String[] split = this.authorityIds.split(",");
            return CollectionUtil.toCollection(split, HashSet::new);
        }
        return new HashSet<>();
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getAuthority() {
        return authority;
    }

    public void setAuthority(String authority) {
        this.authority = authority;
    }
}
