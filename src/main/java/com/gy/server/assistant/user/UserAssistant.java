package com.gy.server.assistant.user;

import java.io.IOException;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.gy.server.servlet.user.login.UserLoginServlet;
import com.gy.server.db.relation.hibernate.HibernateUtil;
import com.gy.server.utils.CollectionUtil;

/**
 * <AUTHOR> - [Created on 2018/5/3 20:04]
 */
public class UserAssistant {

    /**
     * 根据用户名称获取用户信息
     */
    public static User getUserByName(String username) {
        String hql = "from User user where user.username = ?";
        List<User> list = HibernateUtil.query(User.class, hql, username);
        if (CollectionUtil.isNotEmpty(list)) {
            return list.get(0);
        }
        return null;
    }

    /**
     * 查询所有用户信息
     */
    public static List<User> getUserByPage(int pageIndex, int pageSize) {
        String hql = "from User user";
        return HibernateUtil.queryPage(User.class, hql, pageSize, pageIndex);
    }

    /**
     * 删除用户信息
     */
    public static void deleteUser(String username) {
        String hql = "delete User user where user.username = ?";
        HibernateUtil.executeHql(hql, username);
    }

    /**
     * 检查用户权限
     */
    public static boolean checkUserAuthority(HttpServletRequest request, HttpServletResponse response) throws IOException {
        User user = ((User) request.getSession().getAttribute(UserLoginServlet.SESSION_IDENTIFY_KEY));
        if (user == null) {
            response.sendRedirect(request.getContextPath() + "/web/user_login.jsp");
            return false;
        }

        return true;
    }

}
