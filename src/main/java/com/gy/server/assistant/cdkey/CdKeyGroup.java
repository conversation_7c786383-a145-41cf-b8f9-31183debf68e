package com.gy.server.assistant.cdkey;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

import com.gy.server.utils.time.DateTimeFormatterType;
import com.gy.server.utils.time.DateTimeUtil;

/**
 * <AUTHOR> - [Created on 2018/4/26 14:01]
 */
@Entity
@Table(name = "cdkey_group")
public class CdKeyGroup {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "id")
    private int id;

    @Column(name = "key_desc")
    private String desc;

    @Column(name = "rewards")
    private String rewards;

    @Column(name = "reward_info")
    private String rewardInfo;

    @Column(name = "key_type")
    private int type;

    @Column(name = "start_time")
    private Date startTime;

    @Column(name = "end_time")
    private Date endTime;

    @Column(name = "channel")
    private String channel;

    @Column(name = "channel_name")
    private String channelName;

    @Column(name = "register_start_time")
    private Date registerStartTime;

    @Column(name = "register_end_time")
    private Date registerEndTime;

    @Column(name = "level")
    private int level;

    @Transient
    private String startTimeStr;

    @Transient
    private String endTimeStr;

    @Transient
    private String registerStartTimeStr;

    @Transient
    private String registerEndTimeStr;

    @Transient
    private int keyCount;

    public CdKeyGroup() {
    }

    public CdKeyGroup(int type, String rewards, String rewardInfo, String channel, String channelName,
                      Date startDate, Date endDate, Date registerStartDate, Date registerEndDate,
                      int level, String desc) {
        this.type = type;
        this.rewards = rewards;
        this.rewardInfo = rewardInfo;
        this.channel = channel;
        this.channelName = channelName;
        this.startTime = startDate;
        this.endTime = endDate;
        this.registerStartTime = registerStartDate;
        this.registerEndTime = registerEndDate;
        this.level = level;
        this.desc = desc;
    }

    public void init() {
        this.startTimeStr = DateTimeUtil.toString(DateTimeUtil.toLocalDateTime(this.startTime),
                DateTimeFormatterType.date_time);
        this.endTimeStr = DateTimeUtil.toString(DateTimeUtil.toLocalDateTime(this.endTime),
                DateTimeFormatterType.date_time);
        if (this.registerStartTime != null) {
            this.registerStartTimeStr = DateTimeUtil.toString(DateTimeUtil.toLocalDateTime(this.registerStartTime),
                    DateTimeFormatterType.date_time);
        }
        if (this.registerEndTime != null) {
            this.registerEndTimeStr = DateTimeUtil.toString(DateTimeUtil.toLocalDateTime(this.registerEndTime),
                    DateTimeFormatterType.date_time);
        }
        this.keyCount = CdKeyAssistant.getCdKeysByGroupId(this.id).size();
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getRewards() {
        return rewards;
    }

    public void setRewards(String rewards) {
        this.rewards = rewards;
    }

    public String getRewardInfo() {
        return rewardInfo;
    }

    public void setRewardInfo(String rewardInfo) {
        this.rewardInfo = rewardInfo;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getChannelName() {
        return channelName;
    }

    public void setChannelName(String channelName) {
        this.channelName = channelName;
    }

    public Date getRegisterStartTime() {
        return registerStartTime;
    }

    public void setRegisterStartTime(Date registerStartTime) {
        this.registerStartTime = registerStartTime;
    }

    public Date getRegisterEndTime() {
        return registerEndTime;
    }

    public void setRegisterEndTime(Date registerEndTime) {
        this.registerEndTime = registerEndTime;
    }

    public String getStartTimeStr() {
        return startTimeStr;
    }

    public void setStartTimeStr(String startTimeStr) {
        this.startTimeStr = startTimeStr;
    }

    public String getEndTimeStr() {
        return endTimeStr;
    }

    public void setEndTimeStr(String endTimeStr) {
        this.endTimeStr = endTimeStr;
    }

    public String getRegisterStartTimeStr() {
        return registerStartTimeStr;
    }

    public void setRegisterStartTimeStr(String registerStartTimeStr) {
        this.registerStartTimeStr = registerStartTimeStr;
    }

    public String getRegisterEndTimeStr() {
        return registerEndTimeStr;
    }

    public void setRegisterEndTimeStr(String registerEndTimeStr) {
        this.registerEndTimeStr = registerEndTimeStr;
    }

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    public int getKeyCount() {
        return keyCount;
    }

    public void setKeyCount(int keyCount) {
        this.keyCount = keyCount;
    }

    @Override
    public String toString() {
        return "CdKeyGroup{" +
                "id=" + id +
                ", desc='" + desc + '\'' +
                ", rewards='" + rewards + '\'' +
                ", rewardInfo='" + rewardInfo + '\'' +
                ", type=" + type +
                ", startTime=" + startTime +
                ", endTime=" + endTime +
                ", channel='" + channel + '\'' +
                ", channelName='" + channelName + '\'' +
                ", registerStartTime=" + registerStartTime +
                ", registerEndTime=" + registerEndTime +
                ", level=" + level +
                ", startTimeStr='" + startTimeStr + '\'' +
                ", endTimeStr='" + endTimeStr + '\'' +
                ", registerStartTimeStr='" + registerStartTimeStr + '\'' +
                ", registerEndTimeStr='" + registerEndTimeStr + '\'' +
                ", keyCount=" + keyCount +
                '}';
    }
}
