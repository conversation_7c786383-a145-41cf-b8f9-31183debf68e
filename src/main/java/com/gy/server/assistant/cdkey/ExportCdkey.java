package com.gy.server.assistant.cdkey;

/**
 * <AUTHOR> - [Created on 2018/5/2 13:36]
 */
public class ExportCdkey {

    private String id;
    private int maxUseCount;
    private int usedCount;

    public ExportCdkey(CdKey cdKey) {
        this.id = cdKey.getId();
        this.maxUseCount = cdKey.getMaxUseCount();
        this.usedCount = cdKey.getUsedCount();
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public int getMaxUseCount() {
        return maxUseCount;
    }

    public void setMaxUseCount(int maxUseCount) {
        this.maxUseCount = maxUseCount;
    }

    public int getUsedCount() {
        return usedCount;
    }

    public void setUsedCount(int usedCount) {
        this.usedCount = usedCount;
    }
}
