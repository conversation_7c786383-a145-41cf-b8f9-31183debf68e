package com.gy.server.assistant.cdkey;

import java.util.List;

import com.google.common.collect.Lists;
import com.gy.server.db.relation.hibernate.HibernateUtil;
import com.gy.server.utils.key.KeyType;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR> - [Created on 2018/4/26 14:44]
 */
public class CdKeyAssistant {

    private static Logger logger = LogManager.getLogger(CdKeyAssistant.class);

    /**
     * cdkey长度
     */
    private static final int CDKEY_LENGTH = 8;

    /**
     * 查询所有Cdkey组信息
     */
    public static List<CdKeyGroup> getCdkeyGroupByPage(int pageIndex, int pageSize) {
        String hql = "from CdKeyGroup cdKeyGroup";
        return HibernateUtil.queryPage(CdKeyGroup.class, hql, pageSize, pageIndex);
    }

    /**
     * 查询所有Cdkey信息
     */
    public static List<CdKey> getCdKeysByGroupId(int groupId) {
        String hql = "from CdKey cdKey where cdKey.groupId = ?";
        return HibernateUtil.query(CdKey.class, hql, groupId);
    }

    /**
     * 查找CdKey
     */
    public static CdKey getCdKeyByKey(String key) {
        return HibernateUtil.find(CdKey.class, key);
    }

    /**
     * 查找CdKeyGroup
     */
    public static CdKeyGroup getCdKeyGroupById(int groupId) {
        return HibernateUtil.find(CdKeyGroup.class, groupId);
    }

    /**
     * 删除Cdkey组
     */
    public static void deleteCdkeyGroup(String groupIds) {
        String hql = "delete CdKeyGroup cdkeyGroup where cdkeyGroup.id in (" + groupIds + ")";
        HibernateUtil.executeHql(hql);
        //删除该组下的所有Cdkey
        deleteCdkeyByGroupId(groupIds);
    }

    /**
     * 删除指定组下的所有Cdkey
     */
    private static void deleteCdkeyByGroupId(String groupIds) {
        String hql = "delete CdKey cdkey where cdkey.groupId in (" + groupIds + ")";
        HibernateUtil.executeHql(hql);
    }

    /**
     * 分页查找cdkey数据
     */
    public static List<CdKey> getCdkeyByPage(int groupId, int pageIndex, int pageSize) {
        String hql = "from CdKey cdkey where cdkey.groupId = ?";
        return HibernateUtil.queryPage(CdKey.class, hql, pageSize, pageIndex, groupId);
    }

    /**
     * 创建cdkey
     */
    public static void createCdkey(CdKeyGroup cdKeyGroup, int count, int maxUseTime) {
        List<Object> saves = Lists.newArrayList();
        for (int i = 0; i < count; i++) {
            CdKey cdKey = new CdKey(com.gy.server.utils.key.KeyUtil.createKey(KeyType.num_upperWord, CDKEY_LENGTH), maxUseTime, cdKeyGroup.getId());
            try {
                saves.add(cdKey);
                if (i % 100 == 0) {
                    HibernateUtil.saveBatch(saves);
                    saves.clear();
                }
            } catch (Exception e) {
                logger.error(e.getMessage());
            }
        }
        HibernateUtil.saveBatch(saves);
    }

}
