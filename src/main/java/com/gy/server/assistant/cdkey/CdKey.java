package com.gy.server.assistant.cdkey;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * <AUTHOR> - [Created on 2018/4/26 14:00]
 */
@Entity
@Table(name = "cdkey")
public class CdKey {

    @Id
    @Column(name = "key_id")
    private String id;

    @Column(name = "max_use_count")
    private int maxUseCount;

    @Column(name = "used_count")
    private int usedCount;

    @Column(name = "group_id")
    private int groupId;

    public CdKey() {
    }

    public CdKey(String key, int maxUseCount, int groupId) {
        this.id = key;
        this.maxUseCount = maxUseCount;
        this.groupId = groupId;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public int getMaxUseCount() {
        return maxUseCount;
    }

    public void setMaxUseCount(int maxUseCount) {
        this.maxUseCount = maxUseCount;
    }

    public int getUsedCount() {
        return usedCount;
    }

    public void setUsedCount(int usedCount) {
        this.usedCount = usedCount;
    }

    public int getGroupId() {
        return groupId;
    }

    public void setGroupId(int groupId) {
        this.groupId = groupId;
    }
}
