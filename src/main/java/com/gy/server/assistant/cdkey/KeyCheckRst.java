package com.gy.server.assistant.cdkey;

public class KeyCheckRst {

    /**
     * CdKey错误
     */
    public enum ErrorCode {
        codeInvalid,                //无效的cdKey
        channelError,               //渠道错误
        levelError,                 //等级不满足
        formatError,                //cdKey格式错误
        codeMax,                    //达到最大使用次数
        timeError,                  //时间不满足
        serverBusy,;                //服务器异常
    }

    private boolean result;
    private String errorInfo;
    private ErrorCode errorCode;
    private String rewards;


    public boolean isResult() {
        return result;
    }

    public void setResult(boolean result) {
        this.result = result;
    }

    public String getErrorInfo() {
        return errorInfo;
    }

    public void setErrorInfo(String errorInfo) {
        this.errorInfo = errorInfo;
    }

    public ErrorCode getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(ErrorCode errorCode) {
        this.errorCode = errorCode;
    }

    public String getRewards() {
        return rewards;
    }

    public void setRewards(String rewards) {
        this.rewards = rewards;
    }
}
