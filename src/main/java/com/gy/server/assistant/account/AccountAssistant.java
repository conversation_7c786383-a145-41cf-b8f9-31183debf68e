package com.gy.server.assistant.account;

import java.security.NoSuchAlgorithmException;
import java.util.Map;
import java.util.TreeMap;
import java.util.regex.Pattern;

import com.gy.server.utils.MathUtil;
import com.gy.server.utils.Md5Util;
import com.gy.server.utils.key.KeyType;
import com.gy.server.utils.key.KeyUtil;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR> - [Created on 2018/3/28 19:50]
 */
public class AccountAssistant {

    private static final String ACCOUNT_REGEX = "[a-zA-Z0-9]{6,50}";
    private static final String MAIL_REGEX = "^\\s*\\w+(?:\\.{0,1}[\\w-]+)*@[a-zA-Z0-9]+(?:[-.][a-zA-Z0-9]+)*\\.[a-zA-Z]+\\s*$";

    /**
     * 创建一个账号的唯一id
     */
    public static String createId() {
        return String.valueOf(System.currentTimeMillis()) + MathUtil.randomInt(1000);
    }

    /**
     * 创建一个随机字符串
     */
    public static String createRandomText() {
        int length = MathUtil.randomInt(5) + 6;//长度6-10
        return KeyUtil.createKey(KeyType.num_lowerWord, length, "G");
    }


    /**
     * 生成一个密码
     */
    public static String createPassword(String text) throws NoSuchAlgorithmException {
        return Md5Util.MD5(text + "password");
    }

    /**
     * 是否是一个合法的账号
     */
    public static boolean isValidAccountId(String text) {
        return Pattern.matches(ACCOUNT_REGEX, text);
    }

    /**
     * 校验签名
     */
    public static boolean checkSign(Map<String, String> keyMap, String sign) throws NoSuchAlgorithmException {
        return StringUtils.equals(sign, createSign(keyMap));
    }

    /**
     * 构造签名
     */
    public static String createSign(Map<String, String> keyMap) throws NoSuchAlgorithmException {
        String _sign = Md5Util.MD5(generateSignKeySortText(keyMap));
        _sign = Md5Util.MD5(_sign + AccountManager.GAME_KEY);
        return _sign;
    }

    private static String generateSignKeySortText(Map<String, String> map) {
        TreeMap<String, String> tree = new TreeMap<>(map);
        StringBuilder sb = new StringBuilder();
        for (String key : tree.keySet()) {
            String value = tree.get(key);
            sb.append(key).append("=").append(value).append("&");
        }
        sb.setLength(sb.length() - 1);
        return sb.toString();
    }

    /**
     * 是否是一个合法的邮箱
     */
    public static boolean isValidMail(String text) {
        return Pattern.matches(MAIL_REGEX, text);
    }
}
