package com.gy.server.assistant.account;

import java.security.NoSuchAlgorithmException;
import java.util.Date;
import java.util.List;

import com.gy.server.db.relation.hibernate.HibernateUtil;
import com.gy.server.utils.CollectionUtil;
import com.gy.server.utils.Md5Util;

/**
 * 账号管理器
 *
 * <AUTHOR> - [Created on 2018/3/28 14:50]
 */
public class AccountManager {

    public final static String GAME_KEY = "2a5fa67825a9bb8305be74f599ac1b5e";
    public final static String TOKEN_KEY = "28471e8d79c32f62f2a5b5c9b7ddea12";


    /**
     * 创建一个token，如果原token存在且未过期，则返回原token，否则创建新的token
     */
    public static Token createToken(String accountId) throws NoSuchAlgorithmException {
        Token token = getTokenByAccountId(accountId);
        if (token == null || token.isOverdue()) {
            boolean save = false;
            if (token == null) {
                token = new Token();
                token.setAccountId(accountId);
                save = true;
            }
            token.setCreateTime(new Date());
            token.setToken(createTokenText(accountId));
            if (save) {
                HibernateUtil.save(token);
            } else {
                HibernateUtil.update(token);
            }
        }
        return token;
    }

    /**
     * 获得账号对应token，如果token存在且未过期则返回，否则返回null
     */
    public static Token getToken(String accountId) {
        Token token = getTokenByAccountId(accountId);
        if (token != null && !token.isOverdue()) {
            return token;
        }
        return null;
    }

    /**
     * 获得指定id对应账号对象
     */
    public static Account getAccount(String id) {
        return HibernateUtil.find(Account.class, id);
    }

    /**
     * 根据玩家账号获得账号对象
     */
    public static Account getAccountByAccountId(String accountId) {
        String hql = "from Account account where account.accountId = ?";
        List<Account> list = HibernateUtil.query(Account.class, hql, accountId);
        if (CollectionUtil.isNotEmpty(list)) {
            return list.get(0);
        }
        return null;
    }


    /**
     * 根据玩家账号获得其token对象
     */
    private static Token getTokenByAccountId(String accountId) {
        String hql = "from Token token where token.accountId = ?";
        List<Token> list = HibernateUtil.query(Token.class, hql, accountId);
        if (CollectionUtil.isNotEmpty(list)) {
            return list.get(0);
        }
        return null;
    }

    /**
     * 创建一个token文本
     */
    private static String createTokenText(String accountId) throws NoSuchAlgorithmException {
        String key = TOKEN_KEY + String.valueOf(System.currentTimeMillis());
        return Md5Util.MD5(key);
    }

}
