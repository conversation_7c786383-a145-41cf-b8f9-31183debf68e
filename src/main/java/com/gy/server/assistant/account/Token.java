package com.gy.server.assistant.account;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

import com.gy.server.utils.time.DateTimeUtil;

/**
 * <AUTHOR> - [Created on 2018/3/28 14:44]
 */
@Entity
@Table(name = "token")
public class Token {

    private static final long VALID_TIME = 5 * DateTimeUtil.MillisOfMinute;

    @Id
    @Column(name = "account_id")
    private String accountId;

    @Column(name = "token")
    private String token;

    @Column(name = "create_time")
    private Date createTime;


    /**
     * 返回token有效剩余时间秒数
     */
    public long getSurplusSeconds() {
        return (createTime.getTime() + VALID_TIME - System.currentTimeMillis()) / 1000;
    }

    /**
     * 判断token是否过期
     */
    public boolean isOverdue() {
        return getSurplusSeconds() <= 0;
    }


    public String getAccountId() {
        return accountId;
    }

    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
