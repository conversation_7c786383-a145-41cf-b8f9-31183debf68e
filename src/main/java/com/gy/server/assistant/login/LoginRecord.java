package com.gy.server.assistant.login;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * <AUTHOR> - [Created on 2018/3/29 10:29]
 */
@Entity
@Table(name = "login")
public class LoginRecord {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "id")
    private int id;

    @Column(name = "account_type")
    private int accountType;

    @Column(name = "account_id")
    private String accountId;

    @Column(name = "login_token")
    private String loginToken;

    @Column(name = "update_time")
    private Date updateTime = new Date();

    @Override
    public String toString() {
        return "LoginRecord{" +
                "id=" + id +
                ", accountType=" + accountType +
                ", accountId='" + accountId + '\'' +
                ", loginToken='" + loginToken + '\'' +
                ", updateTime=" + updateTime +
                '}';
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getAccountType() {
        return accountType;
    }

    public void setAccountType(int accountType) {
        this.accountType = accountType;
    }

    public String getAccountId() {
        return accountId;
    }

    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    public String getLoginToken() {
        return loginToken;
    }

    public void setLoginToken(String loginToken) {
        this.loginToken = loginToken;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

}
