package com.gy.server.assistant.login;

/**
 * <AUTHOR> - [Created on 2018/3/28 21:30]
 */
public class LoginCheckType {

    /**
     * 压测机器人用
     */
    public static final int ACCOUNT_TYPE_GUEST = 1;
    public static final int ACCOUNT_TYPE_GY = 2;
    public static final int ACCOUNT_TYPE_OASIS = 3;
    public static final int ACCOUNT_TYPE_JG = 4;
    public static final int ACCOUNT_TYPE_YIJIE = 5;
    public static final int ACCOUNT_TYPE_SHOUQU = 6;
    public static final int ACCOUNT_TYPE_MAOER = 7;
    public static final int ACCOUNT_TYPE_6KW = 8;
    public static final int ACCOUNT_TYPE_OPPO = 9;
    public static final int ACCOUNT_TYPE_HUAWEI = 10;
    public static final int ACCOUNT_TYPE_XIONGMAOWAN = 11;
    public static final int ACCOUNT_TYPE_SHOUQU_IOS = 12;
    public static final int ACCOUNT_TYPE_PINRUI_IOS = 13;
    public static final int ACCOUNT_TYPE_DOUYOU = 14;
    public static final int ACCOUNT_TYPE_SHOUQU2_IOS = 15;
    public static final int ACCOUNT_TYPE_6KW_IOS = 16;
    public static final int ACCOUNT_TYPE_CHANGYOU = 17;
    public static final int ACCOUNT_TYPE_PINRUI2_IOS = 18;
    public static final int ACCOUNT_TYPE_DOUYOU_IOS = 19;
    public static final int ACCOUNT_TYPE_PINRUI = 20;
    public static final int ACCOUNT_TYPE_DOUYOU_AND = 21;
    public static final int ACCOUNT_TYPE_DOUYOU_IOS2 = 22;
    public static final int ACCOUNT_TYPE_OASIS_52x = 23;
    public static final int ACCOUNT_TYPE_CHANGYOU_ABROAD = 27;

}
