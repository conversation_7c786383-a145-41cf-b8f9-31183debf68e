package com.gy.server.assistant.login;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.gy.server.db.relation.hibernate.HibernateUtil;
import com.gy.server.utils.CollectionUtil;

/**
 * <AUTHOR> - [Created on 2018/3/29 10:48]
 */
public class LoginAssistant {

    public static LoginRecord getLoginRecordByAccountTypeAndAccountId(int accountType, String accountId) {
        String hql = "from LoginRecord login where login.accountType = ? and login.accountId = ?";
        List<LoginRecord> list = HibernateUtil.query(LoginRecord.class, hql, accountType, accountId);
        if (CollectionUtil.isNotEmpty(list)) {
            return list.get(0);
        }
        return null;
    }

    public static List<LoginRecord> getLoginRecordsByAccountTypeAndAccountIds(int accountType, Collection<String> accountIdList) {
        String hql = "from LoginRecord login where login.accountType = (:accountType) and login.accountId in (:accountIdList)";
        Map<String, Object> map = new HashMap<>();
        map.put("accountType", accountType);
        map.put("accountIdList", accountIdList);
        return HibernateUtil.query(LoginRecord.class, hql, map);
    }

    /**
     * 根据账号类型计算登录校验类型，如果账号类型错误，则返回-1
     */
    public static int calculateLoginCheckTypeByAccountType(int accountType) {
        int dividend = 1000000;
        if (accountType < dividend) {
            return -1;
        }
        return accountType / dividend;
    }
}
