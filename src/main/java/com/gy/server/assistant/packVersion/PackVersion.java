package com.gy.server.assistant.packVersion;

import com.baidu.bjf.remoting.protobuf.annotation.Protobuf;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;


public class PackVersion {
    @Protobuf(order = 1)
    private String version;
    @Protobuf(order = 2)
    private String platform;
    @Protobuf(order = 3)
    private String url;


    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }
}
