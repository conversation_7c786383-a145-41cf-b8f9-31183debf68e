package com.gy.server.assistant.notice;


import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.gy.server.db.relation.hibernate.HibernateUtil;
import com.gy.server.utils.CollectionUtil;

import org.apache.commons.lang3.StringUtils;

@Entity
@Table(name = "notice")
public class Notice {

    public static final String CONTENT_SEPARATOR = "%#@";

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "notice_id")
    private int noticeId;

    @Column(name = "order_id")
    private int orderId;

    /**
     * 账号类型
     */
    @Column(name = "channels")
    private String channels;

    @Column(name = "languages")
    private String languages;

    @Column(name = "titles")
    private String titles;

    @Column(name = "contents")
    private String contents;

    @Column(name = "default_language")
    private String defaultLanguage;

    /**
     * 开始时间
     */
    @Column(name = "start_time")
    private Date startTime;

    /**
     * 结束时间
     */
    @Column(name = "end_time")
    private Date endTime;

    /**
     * 是否使用时间
     */
    @Column(name = "is_set")
    private Boolean usedTime;
    /**
     * 公告类型
     */
    @Column(name = "type")
    private String type;

    /**
     * 子渠道文本
     */
    @Column(name = "platform_channel")
    private String platformChannel = "";

    private transient Set<String> accountTypeIdSet = Sets.newHashSet();
    
    private transient List<String> languageIdList = Lists.newArrayList();

    private transient List<String> titleList = Lists.newArrayList();

    private transient List<String> contentList = Lists.newArrayList();

    private transient Set<String> subChannelIdSet = Sets.newHashSet();


    public void init() {
        if (StringUtils.isNotEmpty(channels)) {
            String[] arrays = channels.split(",");
            accountTypeIdSet = CollectionUtil.toCollection(arrays, HashSet::new);
        }

        if (StringUtils.isNotEmpty(languages)) {
            String[] arrays = languages.split(",");
            languageIdList = CollectionUtil.toCollection(arrays, ArrayList::new);
        }

        if (StringUtils.isNotEmpty(platformChannel)) {
            String[] arrays = platformChannel.split(",");
            subChannelIdSet = CollectionUtil.toCollection(arrays, HashSet::new);
        }

        String[] titleArr = titles.split(CONTENT_SEPARATOR);
        titleList = CollectionUtil.toCollection(titleArr, ArrayList::new);

        String[] contentArr = contents.split(CONTENT_SEPARATOR);
        contentList = CollectionUtil.toCollection(contentArr, ArrayList::new);
    }

    public void save() {
        channels = StringUtils.join(accountTypeIdSet, ",");
        languages = StringUtils.join(languageIdList, ",");
        titles = StringUtils.join(titleList, CONTENT_SEPARATOR);
        contents = StringUtils.join(contentList, CONTENT_SEPARATOR);
        platformChannel = StringUtils.join(subChannelIdSet, ",");

        HibernateUtil.save(this);
    }

    public void update() {
        channels = StringUtils.join(accountTypeIdSet, ",");
        languages = StringUtils.join(languageIdList, ",");
        titles = StringUtils.join(titleList, CONTENT_SEPARATOR);
        contents = StringUtils.join(contentList, CONTENT_SEPARATOR);
        platformChannel = StringUtils.join(subChannelIdSet, ",");

        HibernateUtil.update(this);
    }

    public void delete() {
        HibernateUtil.delete(this);
    }

    public void updateData(Notice noticeNew) {
        orderId = noticeNew.orderId;
        defaultLanguage = noticeNew.defaultLanguage;

        accountTypeIdSet = new HashSet<>(noticeNew.accountTypeIdSet);
        languageIdList = new ArrayList<>(noticeNew.languageIdList);
        titleList = new ArrayList<>(noticeNew.titleList);
        contentList = new ArrayList<>(noticeNew.contentList);
        subChannelIdSet = new HashSet<>(noticeNew.subChannelIdSet);

        channels = StringUtils.join(accountTypeIdSet, ",");
        languages = StringUtils.join(languageIdList, ",");
        titles = StringUtils.join(noticeNew.titleList, CONTENT_SEPARATOR);
        contents = StringUtils.join(noticeNew.contentList, CONTENT_SEPARATOR);
        platformChannel = StringUtils.join(subChannelIdSet, ",");
        type = noticeNew.type;
    }

    private String getTitle(String language) {
        return titleList.get(getLanguageIndex(language));
    }

    private String getContent(String language) {
        return contentList.get(getLanguageIndex(language));
    }

    private int getLanguageIndex(String language) {
        return languageIdList.indexOf(language);
    }

    public Notice.JsonNotice toJsonNotice(String language) {
        if (!languageIdList.contains(language)) {
            language = defaultLanguage;
        }
        return new Notice.JsonNotice(noticeId, language, endTime.getTime(), type);
    }


    public int getNoticeId() {
        return noticeId;
    }

    public int getOrderId() {
        return orderId;
    }

    public String getChannels() {
        return channels;
    }

    public String getLanguages() {
        return languages;
    }

    public String getTitles() {
        return titles;
    }

    public String getContents() {
        return contents;
    }

    public String getDefaultLanguage() {
        return defaultLanguage;
    }

    public Date getStartTime() {
        return startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public Boolean getUsedTime() {
        return usedTime;
    }

    public List<String> getLanguageIdList() {
        return languageIdList;
    }

    public List<String> getTitleList() {
        return titleList;
    }

    public List<String> getContentList() {
        return contentList;
    }

    public Set<String> getAccountTypeIdSet() {
        return accountTypeIdSet;
    }

    public void setNoticeId(int noticeId) {
        this.noticeId = noticeId;
    }

    public void setOrderId(int orderId) {
        this.orderId = orderId;
    }

    public void setChannels(String channels) {
        this.channels = channels;
    }

    public void setLanguages(String languages) {
        this.languages = languages;
    }

    public void setTitles(String titles) {
        this.titles = titles;
    }

    public void setContents(String contents) {
        this.contents = contents;
    }

    public void setDefaultLanguage(String defaultLanguage) {
        this.defaultLanguage = defaultLanguage;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public void setUsedTime(Boolean usedTime) {
        this.usedTime = usedTime;
    }

    public void setAccountTypeIdSet(Set<String> accountTypeIdSet) {
        this.accountTypeIdSet = accountTypeIdSet;
    }

    public void setLanguageIdList(List<String> languageIdList) {
        this.languageIdList = languageIdList;
    }

    public void setTitleList(List<String> titleList) {
        this.titleList = titleList;
    }

    public void setContentList(List<String> contentList) {
        this.contentList = contentList;
    }

    public String getType() {
        return type;
    }

    public String getPlatformChannel() {
        return platformChannel;
    }

    public Set<String> getSubChannelIdSet() {
        return subChannelIdSet;
    }

    public void setSubChannelIdSet(Set<String> subChannelIdSet) {
        this.subChannelIdSet = subChannelIdSet;
    }

    public void setPlatformChannel(String platformChannel) {
        this.platformChannel = platformChannel;
    }

    public class JsonNotice {
        final int noticeId;
        final String title;
        final String content;
        final long time;
        final String type;

        JsonNotice(int noticeId, String language, long time, String type) {
            this.noticeId = noticeId;
            this.title = getTitle(language);
            this.content = getContent(language);
            this.time = time;
            this.type = type;
        }
    }
}
