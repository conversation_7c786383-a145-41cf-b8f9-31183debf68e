package com.gy.server.assistant.notice;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

import com.google.common.collect.Lists;
import com.gy.server.servlet.notice.NoticeType;
import com.gy.server.utils.CollectionUtil;
import com.gy.server.utils.runner.Runner;
import com.gy.server.utils.time.DateTimeUtil;

import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class NoticeManager implements Runner {

    private final static NoticeManager instance = new NoticeManager();

    private static final long UPDATE_INTERVAL = 10 * DateTimeUtil.MillisOfSecond;

    private static Logger logger = LogManager.getLogger(NoticeManager.class);

    private static Map<Integer, Notice> notices = new ConcurrentHashMap<>();

    private NoticeManager() {
    }

    public static NoticeManager getInstance() {
        return instance;
    }

    public static void init() throws IOException {
        List<Notice> noticeList = NoticeAssistant.getAllNotice();
        for (Notice notice : noticeList) {
            notice.init();
            notices.put(notice.getNoticeId(), notice);
        }
    }

    public static void shutdown() throws IOException {
        saveAll();
    }


    public static List<Notice> getAllNotices() {
        return new ArrayList<>(notices.values());
    }

    public static Notice getNoticeById(int noticeId) {
        return notices.get(noticeId);
    }

    public static List<Notice.JsonNotice> getNoticeByChannelAndLanguage(String channel, String language) {
        List<Notice.JsonNotice> list = Lists.newArrayList();
        List<Notice> rstList = Lists.newArrayList();

        if (StringUtils.isNotEmpty(channel) && StringUtils.isNotEmpty(language)) {

            for (Notice notice : notices.values()) {
                if (CollectionUtil.isEmpty(notice.getAccountTypeIdSet())
                        || notice.getAccountTypeIdSet().contains(channel)) {
                    rstList.add(notice);
                }
            }

            rstList = filterNotice(rstList);

            rstList.sort((o1, o2) -> {

                if (o1.getNoticeId() == o2.getOrderId()) {
                    return o1.getNoticeId() - o2.getNoticeId();
                } else {
                    return o1.getOrderId() - o2.getOrderId();
                }
            });

            rstList.forEach(notice -> list.add(notice.toJsonNotice(language)));
        }

        return list;
    }

    public static List<Notice.JsonNotice> getNoticeByChannelAndLanguage(String accountType, String language, Set<String> noticeTypeSet, String subChannel) {
        List<Notice.JsonNotice> list = Lists.newArrayList();
        List<Notice> rstList = Lists.newArrayList();

        if (StringUtils.isNotEmpty(accountType) && StringUtils.isNotEmpty(language)) {
            boolean flag = true;
            for (Notice notice : notices.values()) {
                if (CollectionUtil.isEmpty(notice.getAccountTypeIdSet())//未设置渠道
                        || notice.getAccountTypeIdSet().contains(accountType)//渠道匹配
                        ) {
                    if (noticeTypeSet.contains(notice.getType())
                            && (CollectionUtil.isEmpty(notice.getSubChannelIdSet()) || notice.getSubChannelIdSet().contains(subChannel))
                            && flag ) {
                        rstList.add(notice);
                        if (notice.getType().equals(NoticeType.maintenance.getId())) flag = false;
                    }
                    // 运维公告只能有一个
                }
            }

            rstList = filterNotice(rstList);

            rstList.sort((o1, o2) -> {

                if (o1.getOrderId() == o2.getOrderId()) {
                    return o1.getNoticeId() - o2.getNoticeId();
                } else {
                    return o1.getOrderId() - o2.getOrderId();
                }
            });

            rstList.forEach(notice -> list.add(notice.toJsonNotice(language)));
        }

        return list;
    }


    private static List<Notice> filterNotice(List<Notice> noticeList){
        List<Notice> result = new ArrayList<>();
        noticeList.forEach(notice -> {
            if(notice.getUsedTime() != null && notice.getStartTime() != null
                    && notice.getEndTime() != null &&notice.getUsedTime()){
                long now = System.currentTimeMillis();
                if(notice.getStartTime().getTime() < now && now < notice.getEndTime().getTime()){
                    result.add(notice);
                }
            }else{
                result.add(notice);
            }
        });
        return result;
    }


    public static void removeNotice(int noticeId) {
        Notice notice = notices.remove(noticeId);
        if (notice != null) {
            notice.delete();
        }
    }


    private static void saveAll() {
        notices.values().forEach(Notice::update);
    }

    public static void updateFromDb() {
        try {
            notices.clear();
            init();
//            List<Notice> noticeList = NoticeAssistant.getAllNotice();
//            Set<Integer> idSet = new HashSet<>();
//            for (Notice notice : noticeList) {
//                idSet.add(notice.getNoticeId());
//                notice.init();
//                Notice oldNotice = notices.get(notice.getNoticeId());
//                if (oldNotice == null) {
//                    notices.put(notice.getNoticeId(), notice);
//                } else {
//                    oldNotice.updateData(notice);
//                }
//            }

//            notices.keySet().stream()
//                    .filter(integer -> !idSet.contains(integer))
//                    .forEach(integer -> notices.remove(integer));
        } catch (Exception e) {
            logger.catching(e);
        }
    }

    @Override
    public void runnerExecute() throws Exception {
        updateFromDb();
    }

    @Override
    public long getRunnerInterval() {
        return UPDATE_INTERVAL;
    }
}
