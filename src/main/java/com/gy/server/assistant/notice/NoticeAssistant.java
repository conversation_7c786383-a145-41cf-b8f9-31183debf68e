package com.gy.server.assistant.notice;

import java.util.List;

import com.gy.server.db.relation.hibernate.HibernateUtil;
import com.gy.server.utils.cipher.DesCipher;

import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class NoticeAssistant {

    private static final Logger logger = LogManager.getLogger(NoticeAssistant.class);

    private static final String SECRET_KEY = "NhP9Kk4AFS5QCg17jLQz9qJRhDoiAfFY";

    public static List<Notice> getAllNotice() {
        String hql = "from Notice";
        return HibernateUtil.query(Notice.class, hql);
    }

    public static boolean checkAuthValidity(String data) {
        try {
            if (StringUtils.isNotEmpty(data)) {
                return StringUtils.equals("gyGateNoticeManager", decrypt(data));
            }
        } catch (Exception e) {
            logger.catching(e);
        }
        return false;
    }

    private static String decrypt(String data) throws Exception {
        return DesCipher.decrypt(SECRET_KEY, data);
    }
}
