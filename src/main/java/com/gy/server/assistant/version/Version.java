package com.gy.server.assistant.version;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

import com.gy.server.db.relation.hibernate.HibernateUtil;
import com.gy.server.utils.CollectionUtil;
import com.gy.server.utils.MathUtil;
import com.gy.server.utils.StringUtil;

import org.apache.commons.lang3.StringUtils;

@Entity
@Table(name = "version")
public class Version {

    @Id
    @Column(name = "id")
    private int id;
    /**
     * 更新类型：
     * 1 更新
     * 2 预加载：服务器不会卡版本
     * 3 整包(现在没用到)
     */
    @Column(name = "type")
    private int type;

    @Column(name = "version")
    private String version;

    @Column(name = "url")
    private String url;

    @Column(name = "md5")
    private String md5;

    @Column(name = "size")
    private int size;

    @Column(name = "decompress_size")
    private int decompressSize;

    @Column(name = "platform")
    private String platform;

    @Column(name = "limit_ips")
    private String limitIps;

    @Column(name = "force_update")
    private boolean forceUpdate;

    @Column(name = "time")
    private Date time = new Date();

    public transient Set<String> limitIpSet = new HashSet<>();

    @Column(name = "init_version_include")
    private String initVersionInclude;

    public transient Set<String> initVersionIncludeSet = new HashSet<>();

    @Column(name = "init_version_exclude")
    private String initVersionExclude;

    public transient Set<String> initVersionExcludeSet = new HashSet<>();


    public static int versionCompare(String version1, String version2) {
        int[] version1Array = StringUtil.splitToIntArray(version1, ".");
        int[] version2Array = StringUtil.splitToIntArray(version2, ".");
        int minLength = MathUtil.min(version1Array.length, version2Array.length);
        for (int i = 0; i < minLength; i++) {
            if (version1Array[i] < version2Array[i]) {
                return -1;
            } else if (version1Array[i] > version2Array[i]) {
                return 1;
            }
        }

        if (version1Array.length > version2Array.length) {
            return 1;
        } else if (version1Array.length < version2Array.length) {
            return -1;
        }
        return 0;
    }

    public void init() {
        if (StringUtils.isNotEmpty(limitIps)) {
            String[] arrays = limitIps.split(",");
            limitIpSet = CollectionUtil.toCollection(arrays, HashSet::new);
        }
        if (StringUtils.isNotEmpty(initVersionInclude)) {
            String[] arrays = initVersionInclude.split(",");
            initVersionIncludeSet = CollectionUtil.toCollection(arrays, HashSet::new);
        }
        if (StringUtils.isNotEmpty(initVersionExclude)) {
            String[] arrays = initVersionExclude.split(",");
            initVersionExcludeSet = CollectionUtil.toCollection(arrays, HashSet::new);
        }
    }

    public void save() {
        limitIps = StringUtils.join(limitIpSet, ",");
        initVersionInclude = StringUtils.join(initVersionIncludeSet, ",");
        initVersionExclude = StringUtils.join(initVersionExcludeSet, ",");
        HibernateUtil.save(this);
    }

    public void update() {
        limitIps = StringUtils.join(limitIpSet, ",");
        initVersionInclude = StringUtils.join(initVersionIncludeSet, ",");
        initVersionExclude = StringUtils.join(initVersionExcludeSet, ",");
        HibernateUtil.update(this);
    }

    public void updateData(Version version) {
        this.type = version.type;
        this.version = version.version;
        this.url = version.url;
        this.md5 = version.md5;
        this.size = version.size;
        this.decompressSize = version.decompressSize;
        this.platform = version.platform;
        this.limitIpSet = new HashSet<>(version.limitIpSet);
        this.limitIps = StringUtils.join(limitIpSet, ",");
        this.forceUpdate = version.forceUpdate;
        this.time = version.time;
        this.initVersionIncludeSet = new HashSet<>(version.initVersionIncludeSet);
        this.initVersionInclude = StringUtils.join(initVersionIncludeSet, ",");
        this.initVersionExcludeSet = new HashSet<>(version.initVersionExcludeSet);
        this.initVersionExclude = StringUtils.join(initVersionExcludeSet, ",");
    }

    public boolean checkIpLimit(String ip) {
        return limitIpSet.isEmpty() || limitIpSet.contains(ip);
    }

    public boolean isSame(Version version) {
        if (version != null) {
            return StringUtils.equals(this.version, version.version) && StringUtils.equals(this.platform, version.platform);
        }
        return false;
    }

    /**
     * 检查初始版本限制
     */
    public boolean checkClientInitVersionLimit(String clientInitVersion) {

        if (!initVersionIncludeSet.isEmpty()) {
            return initVersionIncludeSet.contains(clientInitVersion);
        }
        if (!initVersionExcludeSet.isEmpty()) {
            return !initVersionExcludeSet.contains(clientInitVersion);
        }
        return true;
    }

    public JsonVersion toJsonVersion() {
        return new JsonVersion(id, type, version, url, md5, size, decompressSize,time);
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getMd5() {
        return md5;
    }

    public void setMd5(String md5) {
        this.md5 = md5;
    }

    public int getSize() {
        return size;
    }

    public void setSize(int size) {
        this.size = size;
    }

    public int getDecompressSize() {
        return decompressSize;
    }

    public void setDecompressSize(int decompressSize) {
        this.decompressSize = decompressSize;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public String getLimitIps() {
        return limitIps;
    }

    public void setLimitIps(String limitIps) {
        this.limitIps = limitIps;
    }

    public Set<String> getLimitIpSet() {
        return limitIpSet;
    }

    public boolean isForceUpdate() {
        return forceUpdate;
    }

    public void setForceUpdate(boolean forceUpdate) {
        this.forceUpdate = forceUpdate;
    }

    public Date getTime() {
        return time;
    }

    public void setTime(Date time) {
        this.time = time;
    }

    public void setLimitIpSet(Set<String> limitIpSet) {
        this.limitIpSet = limitIpSet;
    }

    public String getInitVersionInclude() {
        return initVersionInclude;
    }

    public void setInitVersionInclude(String initVersionInclude) {
        this.initVersionInclude = initVersionInclude;
    }

    public Set<String> getInitVersionIncludeSet() {
        return initVersionIncludeSet;
    }

    public void setInitVersionIncludeSet(Set<String> initVersionIncludeSet) {
        this.initVersionIncludeSet = initVersionIncludeSet;
    }

    public String getInitVersionExclude() {
        return initVersionExclude;
    }

    public void setInitVersionExclude(String initVersionExclude) {
        this.initVersionExclude = initVersionExclude;
    }

    public Set<String> getInitVersionExcludeSet() {
        return initVersionExcludeSet;
    }

    public void setInitVersionExcludeSet(Set<String> initVersionExcludeSet) {
        this.initVersionExcludeSet = initVersionExcludeSet;
    }

    public class JsonVersion {

        private int id;

        private int type;

        private String version;

        private String url;

        private String md5;

        private int size;

        private int decompressSize;

        private Date time;

        public JsonVersion(int id, int type, String version, String url, String md5, int size, int decompressSize,Date time) {
            this.id = id;
            this.type = type;
            this.version = version;
            this.url = url;
            this.md5 = md5;
            this.size = size;
            this.decompressSize = decompressSize;
            this.time = time;
        }
    }
}
