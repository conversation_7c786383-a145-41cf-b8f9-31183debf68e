package com.gy.server.assistant.version;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

import com.gy.server.core.command.ServerCommandRequest;
import com.gy.server.db.relation.hibernate.HibernateUtil;
import com.gy.server.utils.runner.Runner;
import com.gy.server.utils.time.DateTimeUtil;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.rpc.CommandRequests;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class VersionManager implements Runner {

    private static final long UPDATE_INTERVAL = 10 * DateTimeUtil.MillisOfSecond;

    private static final VersionManager instance = new VersionManager();

    private static Logger logger = LogManager.getLogger(VersionManager.class);

    private static ConcurrentHashMap<Integer, Version> versions = new ConcurrentHashMap<>();

    public static String maxVersion;
    public static Map<String, String> maxVersions = new HashMap<>();
    // 需要强制更新的版本号
    public static Map<String, String> maxVersionsForce = new HashMap<>();

    private VersionManager() {

    }

    public static VersionManager getInstance() {
        return instance;
    }

    public static void init() throws IOException {
        List<Version> versionList = VersionAssistant.getAllVersion();
        for (Version version : versionList) {
            version.init();
            versions.put(version.getId(), version);
        }
    }

    public static void shutdown() throws IOException {
        save();
    }


    private static void save() {
        versions.values().forEach(Version::update);
    }

    public static List<Version> getAllVersions() {
        return new ArrayList<>(versions.values());
    }

    public static void removeVersion(int id) {
        Version version = versions.remove(id);
        if (version != null) {
            HibernateUtil.delete(version);
        }
    }

    public static Version getVersionById(int id) {
        return versions.get(id);
    }

    /**
     * 获取玩家可见的最大版本，按类型排序，若有多个类型，返回每个类型各自最大版本
     */
    public static List<Version.JsonVersion> getTopJsonVersions(String ip, String platform) {
        List<Version.JsonVersion> list = new ArrayList<>();
        List<Version> vs = new ArrayList<>();
        for (Version version : versions.values()) {
            try {
                if (version.getPlatform().equals(platform)
                        && version.checkIpLimit(ip)) {
                    vs.add(version);
                }
            } catch (Exception e) {
                logger.catching(e);
            }
        }
        Collections.sort(vs, new Comparator<Version>() {
            @Override
            public int compare(Version o1, Version o2) {
                return Version.versionCompare(o2.getVersion(),o1.getVersion());
            }
        });

        Set<Integer> types = new HashSet<>();
        for(Version v : vs){
            if(!types.contains(v.getType())){
                list.add(v.toJsonVersion());
                types.add(v.getType());
            }
        }

        return list;
    }

    public static List<Version.JsonVersion> getJsonVersionsByClientVersion(String clientVersion, String ip, String platform) {
        List<Version.JsonVersion> list = new ArrayList<>();
        for (Version version : versions.values()) {
            try {
                if (version.getPlatform().equals(platform)
                        && version.checkIpLimit(ip)
                        && Version.versionCompare(version.getVersion(), clientVersion) > 0) {
                    list.add(version.toJsonVersion());
                }
            } catch (Exception e) {
                logger.catching(e);
            }
        }

        return list;
    }

    public static void updateFromDb() {
        try {
            List<Version> versionList = VersionAssistant.getAllVersion();
            Set<Integer> idSet = new HashSet<>();
            for (Version version : versionList) {
                idSet.add(version.getId());
                version.init();
                Version oldVersion = versions.get(version.getId());
                if (oldVersion == null) {
                    versions.put(version.getId(), version);
                } else {
                    oldVersion.updateData(version);
                }
            }

            versions.keySet().stream()
                    .filter(integer -> !idSet.contains(integer))
                    .forEach(integer -> versions.remove(integer));

            updateMaxVersion();
        } catch (Exception e) {
            logger.catching(e);
        }
    }

    public static void updateMaxVersion() {
        String maxVersion = "0.0.0";
        for (Version version : versions.values()) {
            if (maxVersion == null
                    || (version.limitIpSet.isEmpty() && Version.versionCompare(version.getVersion(), maxVersion) > 0)) {
                maxVersion = version.getVersion();
            }
        }
        VersionManager.maxVersion = maxVersion;

        Map<String, String> tmpMaxVersions = new HashMap<>();
        Map<String, String> tmpMaxVersionsForce = new HashMap<>();
        for (Version version : versions.values()) {
            String curVersion = tmpMaxVersions.getOrDefault(version.getPlatform(), "0.0.0");
            String curVersionForce = tmpMaxVersionsForce.getOrDefault(version.getPlatform(), "0.0.0");

            if (version.limitIpSet.isEmpty() && version.getType() != 2) {
                if (Version.versionCompare(version.getVersion(), curVersion) > 0) {
                    tmpMaxVersions.put(version.getPlatform(), version.getVersion());
                }
                if (Version.versionCompare(version.getVersion(), curVersionForce) > 0 && version.isForceUpdate()) {
                    tmpMaxVersionsForce.put(version.getPlatform(), version.getVersion());
                }
            }
        }
        maxVersions = tmpMaxVersions;
        maxVersionsForce = tmpMaxVersionsForce;
    }

    public static boolean hasLowerPreDownloadVersion(String checkVersion, String platform) {
        for (Version version : versions.values()) {
            try {
                if (version.getPlatform().equals(platform)
                        && version.getType() == 2
                        && Version.versionCompare(version.getVersion(), checkVersion) < 0) {
                    return true;
                }
            } catch (Exception e) {
                logger.catching(e);
            }
        }

        return false;
    }

    /**
     * 根据客户端版本和客户端初始版本获得需要更新的版本列表
     */
    public static List<Version.JsonVersion> getJsonVersionsByClientVersionAndInitVersion(
            String clientVersion, String clientInitVersion, String ip, String platform) {
        List<Version.JsonVersion> list = new ArrayList<>();
        for (Version version : versions.values()) {
            try {
                if (version.getPlatform().equals(platform)
                        && version.checkIpLimit(ip)
                        && Version.versionCompare(version.getVersion(), clientVersion) > 0
                        && version.checkClientInitVersionLimit(clientInitVersion)) {
                    list.add(version.toJsonVersion());
                }
            } catch (Exception e) {
                logger.catching(e);
            }
        }

        return list;
    }



    @Override
    public void runnerExecute() throws Exception {
        updateFromDb();
        //同步gs节点，需要强制更新的客户端最大版本号
        syncMaxVersion();
    }

    @Override
    public long getRunnerInterval() {
        return UPDATE_INTERVAL;
    }

    public static void syncMaxVersion() {
        TLBase.getInstance().getLockUtil().executeWithLock(()->{
        ServerCommandRequest serverCommandRequest = CommandRequests.newServerCommandRequest("BillingCommandService.syncMaxVersion");
        TLBase.getInstance().getRpcUtil().sendToAll(ServerType.GAME, serverCommandRequest, maxVersionsForce, maxVersions);
        },"syncVersionToAllServer");
    }
}
