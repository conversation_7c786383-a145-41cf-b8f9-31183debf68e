package com.gy.server.assistant.channel;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * <AUTHOR> - [Created on 2018/4/27 10:23]
 */
@Entity
@Table(name = "channel")
public class Channel {

    @Id
    @Column(name = "channel_id")
    private String id;

    @Column(name = "channel_name")
    private String name;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
