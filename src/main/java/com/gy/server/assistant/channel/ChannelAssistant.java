package com.gy.server.assistant.channel;

import java.util.List;

import com.gy.server.db.relation.hibernate.HibernateUtil;

/**
 * 这里的渠道，代表子渠道
 * <AUTHOR> - [Created on 2018/4/27 10:23]
 */
public class ChannelAssistant {

    /**
     * 查询渠道信息
     */
    public static List<Channel> getChannelByPage(int pageIndex, int pageSize) {
        String hql = "from Channel channel";
        return HibernateUtil.queryPage(Channel.class, hql, pageSize, pageIndex);
    }

    /**
     * 查询渠道信息
     */
    public static List<Channel> getChannels() {
        String hql = "from Channel channel";
        return HibernateUtil.query(Channel.class, hql);
    }
}
