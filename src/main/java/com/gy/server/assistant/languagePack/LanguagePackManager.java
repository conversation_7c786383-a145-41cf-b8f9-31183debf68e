package com.gy.server.assistant.languagePack;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

import com.gy.server.db.relation.hibernate.HibernateUtil;
import com.gy.server.utils.runner.Runner;
import com.gy.server.utils.time.DateTimeUtil;
import com.gy.server.assistant.version.Version;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class LanguagePackManager implements Runner {

    private static final long UPDATE_INTERVAL = 10 * DateTimeUtil.MillisOfSecond;

    private static final LanguagePackManager instance = new LanguagePackManager();

    private static Logger logger = LogManager.getLogger(LanguagePackManager.class);

    private static ConcurrentHashMap<Integer, LanguagePack> languagePacks = new ConcurrentHashMap<>();

    private LanguagePackManager() {

    }

    public static LanguagePackManager getInstance() {
        return instance;
    }

    public static void init() throws IOException {
        List<LanguagePack> packList = LanguagePackAssistant.getAllLanguagePack();
        for (LanguagePack languagePack : packList) {
            languagePack.init();
            languagePacks.put(languagePack.getId(), languagePack);
        }
    }

    public static void shutdown() throws IOException {
        save();
    }


    private static void save() {
        languagePacks.values().forEach(LanguagePack::update);
    }

    public static List<LanguagePack> getAllLanguagePacks() {
        return new ArrayList<>(languagePacks.values());
    }

    public static void removeLanguagePack(int id) {
        LanguagePack languagePack = languagePacks.remove(id);
        if (languagePack != null) {
            HibernateUtil.delete(languagePack);
        }
    }

    public static LanguagePack getLanguagePackById(int id) {
        return languagePacks.get(id);
    }

    public static List<LanguagePack.JsonLanguagePack> getJsonLanguagePackByClientVersion(int language, String clientVersion, String ip, String platform) {
        List<LanguagePack.JsonLanguagePack> list = new ArrayList<>();
        for (LanguagePack languagePack : languagePacks.values()) {
            try {
                if (languagePack.getPlatform().equals(platform)
                        && language == languagePack.getLanguage()
                        && languagePack.checkIpLimit(ip)
                        && Version.versionCompare(languagePack.getVersion(), clientVersion) > 0) {
                    list.add(languagePack.toJsonLanguagePack());
                }
            } catch (Exception e) {
                logger.catching(e);
            }
        }

        return list;
    }

    public static void updateFromDb() {
        try {
            List<LanguagePack> packList = LanguagePackAssistant.getAllLanguagePack();
            Set<Integer> idSet = new HashSet<>();
            for (LanguagePack languagePack : packList) {
                languagePack.init();
                idSet.add(languagePack.getId());
                LanguagePack oldPack = languagePacks.get(languagePack.getId());
                if (oldPack == null) {
                    languagePacks.put(languagePack.getId(), languagePack);
                } else {
                    oldPack.updateData(languagePack);
                }
            }

            languagePacks.keySet().stream()
                    .filter(integer -> !idSet.contains(integer))
                    .forEach(integer -> languagePacks.remove(integer));
        } catch (Exception e) {
            logger.catching(e);
        }
    }

    @Override
    public void runnerExecute() throws Exception {
        updateFromDb();
    }

    @Override
    public long getRunnerInterval() {
        return UPDATE_INTERVAL;
    }
}
