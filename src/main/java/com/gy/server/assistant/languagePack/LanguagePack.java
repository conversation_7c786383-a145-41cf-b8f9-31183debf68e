package com.gy.server.assistant.languagePack;

import java.util.HashSet;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

import com.gy.server.db.relation.hibernate.HibernateUtil;
import com.gy.server.utils.CollectionUtil;

import org.apache.commons.lang3.StringUtils;

@Entity
@Table(name = "language_pack")
public class LanguagePack {

    @Id
    @Column(name = "id")
    private int id;

    @Column(name = "language")
    private int language;

    @Column(name = "version")
    private String version;

    @Column(name = "url")
    private String url;

    @Column(name = "md5")
    private String md5;

    @Column(name = "size")
    private int size;

    @Column(name = "decompress_size")
    private int decompressSize;

    @Column(name = "platform")
    private String platform;

    @Column(name = "limit_ips")
    private String limitIps;

    public transient Set<String> limitIpSet = new HashSet<>();

    public void init() {
        if (StringUtils.isNotEmpty(limitIps)) {
            String[] arrays = limitIps.split(",");
            limitIpSet = CollectionUtil.toCollection(arrays, HashSet::new);
        }
    }

    public void save() {
        limitIps = StringUtils.join(limitIpSet, ",");
        HibernateUtil.save(this);
    }

    public void update() {
        limitIps = StringUtils.join(limitIpSet, ",");
        HibernateUtil.update(this);
    }

    public void updateData(LanguagePack pack) {
        this.language = pack.language;
        this.version = pack.version;
        this.url = pack.url;
        this.md5 = pack.md5;
        this.size = pack.size;
        this.decompressSize = pack.decompressSize;
        this.platform = pack.platform;
        this.limitIpSet = new HashSet<>(pack.limitIpSet);
        this.limitIps = StringUtils.join(limitIpSet, ",");
    }

    public boolean checkIpLimit(String ip) {
        return limitIpSet.isEmpty() || limitIpSet.contains(ip);
    }

    public boolean isSame(LanguagePack pack) {
        if (pack != null) {
            return this.language == pack.getLanguage()
                    && StringUtils.equals(this.version, pack.version)
                    && StringUtils.equals(this.platform, pack.platform);
        }
        return false;
    }

    public JsonLanguagePack toJsonLanguagePack() {
        return new JsonLanguagePack(id, language, version, url, md5, size, decompressSize);
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getLanguage() {
        return language;
    }

    public void setLanguage(int language) {
        this.language = language;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getMd5() {
        return md5;
    }

    public void setMd5(String md5) {
        this.md5 = md5;
    }

    public int getSize() {
        return size;
    }

    public void setSize(int size) {
        this.size = size;
    }

    public int getDecompressSize() {
        return decompressSize;
    }

    public void setDecompressSize(int decompressSize) {
        this.decompressSize = decompressSize;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public String getLimitIps() {
        return limitIps;
    }

    public void setLimitIps(String limitIps) {
        this.limitIps = limitIps;
    }

    public Set<String> getLimitIpSet() {
        return limitIpSet;
    }

    public class JsonLanguagePack {
        private int id;

        private int language;

        private String version;

        private String url;

        private String md5;

        private int size;

        private int decompressSize;

        public JsonLanguagePack(int id, int language, String version, String url, String md5, int size, int decompressSize) {
            this.id = id;
            this.language = language;
            this.version = version;
            this.url = url;
            this.md5 = md5;
            this.size = size;
            this.decompressSize = decompressSize;
        }
    }
}
