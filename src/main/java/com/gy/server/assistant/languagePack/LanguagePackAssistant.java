package com.gy.server.assistant.languagePack;

import java.util.List;

import com.gy.server.db.relation.hibernate.HibernateUtil;
import com.gy.server.utils.cipher.DesCipher;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class LanguagePackAssistant {

    private static final Logger logger = LogManager.getLogger(LanguagePackAssistant.class);

    private static final String SECRET_KEY = "NhP9Kk4AFS5QCg17jLQz9qJRhDoiAfFY";

    public static List<LanguagePack> getAllLanguagePack() {
        String hql = "from LanguagePack";
        return HibernateUtil.query(LanguagePack.class, hql);
    }

    public static boolean checkAuthValidity(String data) {
        try {
            if (StringUtils.isNotEmpty(data)) {
                return StringUtils.equals("gyGateLanguagePackManager", decrypt(data));
            }
        } catch (Exception e) {
            logger.catching(e);
        }
        return false;
    }

    private static String decrypt(String data) throws Exception {
        return DesCipher.decrypt(SECRET_KEY, data);
    }

    @SuppressWarnings("Duplicates")
    public static boolean isValidVersionText(String text) {
        if (StringUtils.isEmpty(text)
                || !text.contains(".")
                || text.startsWith(".")
                || text.endsWith(".")){
        	return false;
        }

        for (String str : StringUtils.split(text, ".")) {
            if (!NumberUtils.isDigits(str)) {
                return false;
            }
        }

        return true;
    }
}
