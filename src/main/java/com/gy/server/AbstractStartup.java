package com.gy.server;

import java.io.FileNotFoundException;
import java.io.InputStream;
import java.util.Collection;
import java.util.HashSet;
import java.util.Set;

import com.gy.server.sdk.SDKParam;
import com.gy.server.message.DefaultMessage;
import com.gy.server.message.MessageLanguage;
import com.gy.server.utils.XmlUtil;
import com.gy.server.utils.module.MainThread;
import com.gy.server.utils.runner.RunnerManager;
import com.gy.server.web.db.HibernateMappingClass;
import com.gy.server.web.startup.StartupDbListener;

import org.dom4j.Element;
public abstract class AbstractStartup extends StartupDbListener implements HibernateMappingClass {


    @Override
    public Collection<Class> getHibernateMappingClasses() throws Exception {
        String mappingsFileName = "mappings.xml";
        InputStream inputStream = this.getClass().getClassLoader().getResourceAsStream(mappingsFileName);
        if (inputStream == null) {
            throw new FileNotFoundException("file not found -> " + mappingsFileName);
        }
        Set<Class> set = new HashSet<>();
        Element rootElement = XmlUtil.loadDocumentElement(inputStream);
        Element element = XmlUtil.getChild(rootElement, "mappings");
        String[] classTexts = XmlUtil.getChildrenText(element, "mapping");
        for (String classText : classTexts) {
            set.add(Class.forName(classText));
        }
        return set;
    }

    @Override
    protected String getStartupFinishText() {
        return " _      _  _  _  _                       _                 _              _   _ \n" +
                "| |    (_)| || |(_)                     | |               | |            | | | |\n" +
                "| |__   _ | || | _  _ __    __ _    ___ | |_   __ _  _ __ | |_   ___   __| | | |\n" +
                "| '_ \\ | || || || || '_ \\  / _` |  / __|| __| / _` || '__|| __| / _ \\ / _` | | |\n" +
                "| |_) || || || || || | | || (_| |  \\__ \\| |_ | (_| || |   | |_ |  __/| (_| | |_|\n" +
                "|_.__/ |_||_||_||_||_| |_| \\__, |  |___/ \\__| \\__,_||_|    \\__| \\___| \\__,_| (_)\n" +
                "                            __/ |                                               \n" +
                "                           |___/                                                ";
    }

    @Override
    protected String getShutdownFinishText() {
        return " _      _  _  _  _                       _                            _   _ \n" +
                "| |    (_)| || |(_)                     | |                          | | | |\n" +
                "| |__   _ | || | _  _ __    __ _    ___ | |_   ___   _ __    ___   __| | | |\n" +
                "| '_ \\ | || || || || '_ \\  / _` |  / __|| __| / _ \\ | '_ \\  / _ \\ / _` | | |\n" +
                "| |_) || || || || || | | || (_| |  \\__ \\| |_ | (_) || |_) ||  __/| (_| | |_|\n" +
                "|_.__/ |_||_||_||_||_| |_| \\__, |  |___/ \\__| \\___/ | .__/  \\___| \\__,_| (_)\n" +
                "                            __/ |                   | |                     \n" +
                "                           |___/                    |_|                     ";
    }

    @Override
    protected void systemInit() throws Exception {

        //初始化系统语言
        Configuration.language = this.getSystemMessageLanguage();

        //初始化游戏id
        Configuration.gameId = this.getCurrentGameId();

        //初始化测试模式
        Configuration.openActiveCode = this.isOpenActiveCode();

        //文本
        DefaultMessage.init();

        //SDK参数
        SDKParam.initParams();

        //独立线程...

        //ticker
        MainThread.getInstance().addTicker(RunnerManager.getInstance());

        //子类初始化
        this.init();
    }

    @Override
    protected void systemStartup() throws Exception {

        //...

        //子类启动
        this.startup();
    }

    @Override
    protected void systemShutdown() throws Exception {

        //子类关闭
        this.shutdown();


        //...
    }


    /**
     * 获得系统语言
     */
    protected abstract MessageLanguage getSystemMessageLanguage() throws Exception;

    /**
     * 获得当前游戏id
     */
    protected abstract String getCurrentGameId();

    /**
     * 是否测试模式
     */
    protected abstract boolean isTest();

    /**
     * 是否开启登录激活码
     */
    protected abstract boolean isOpenActiveCode();


    protected abstract void init() throws Exception;

    protected abstract void startup() throws Exception;

    protected abstract void shutdown() throws Exception;

}
 
