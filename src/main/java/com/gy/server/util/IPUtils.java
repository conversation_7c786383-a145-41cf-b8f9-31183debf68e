package com.gy.server.util;

import java.io.File;
import java.io.FileNotFoundException;
import java.net.InetAddress;
import java.net.URL;

import com.gy.server.Configuration;
import com.gy.server.web.log.LoggerManager;
import com.maxmind.geoip2.DatabaseReader;
import com.maxmind.geoip2.record.Country;

/**
 * @program: bl-gate
 * @description: ip处理工具类
 * @author: <PERSON><PERSON>
 * @create: 2020-03-09 17:15
 **/
public class IPUtils {
    public static String GEO_COUNTRY_DB = "GeoLite2-City.mmdb";


    private static String ipCheckChar = "((25[0-5]|2[0-4]\\d|[0-1]\\d{2}|[1-9]?\\d).){3}(25[0-5]|2[0-4]\\d|[0-1]\\d{2}|[1-9]?\\d)";

    public static DatabaseReader reader = null;

    //TODO 暂时没调用先不初始化，二进制文件需要拷贝
    public static void init(){
        try {
            URL url = Configuration.class.getClassLoader().getResource(GEO_COUNTRY_DB);
            if (url == null || url.getPath() == null) {
                throw new FileNotFoundException("file not found -> " + GEO_COUNTRY_DB);
            }
            File file = new File(url.getPath());

            System.setProperty("java.net.preferIPv4Stack", "true");
            reader = new DatabaseReader.Builder(file).build();

        }catch (Exception e){
            LoggerManager.error("Init geo db error");
            LoggerManager.error(e);
        }
    }

    public static String getCountry(String ip){
        try {
            InetAddress ipAddress = InetAddress.getByName(ip);
            Country country = reader.city(ipAddress).getCountry();
            return country.getIsoCode();
        }catch (Exception e){
            return null;
        }
    }

    public static boolean checkIp(String ip){
        return ip.matches(ipCheckChar);
    }
}
