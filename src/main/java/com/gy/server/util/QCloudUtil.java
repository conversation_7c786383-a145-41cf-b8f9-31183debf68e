package com.gy.server.util;

import java.io.ByteArrayInputStream;
import java.io.InputStream;

import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.auth.BasicCOSCredentials;
import com.qcloud.cos.auth.COSCredentials;
import com.qcloud.cos.model.ObjectMetadata;
import com.qcloud.cos.model.PutObjectRequest;
import com.qcloud.cos.region.Region;

/**
 * @program: xhx-billing
 * @description: 腾讯云工具类
 * @author: <PERSON>.<PERSON>
 * @create: 2021-10-09 14:18
 **/
public class QCloudUtil {

    public static String SecretId = "AKIDKWhgUHifz3rujJRkzzsZrBH7hMyQufdW";
    public static String SecretKey = "pOp03sU0HL8lRzHMB7i5EZuukNEzc1f5";
    public static String AppId = "1251125656";
    public static String bucketName = "xhxbjz-" + AppId;
    /** 储存地区 **/
    public static String saveRegionStr = "ap-shanghai";

    /** 审核地区---支持区域不多，除非跨国否则不要轻易改 **/
    public static String checkRegionStr = "ap-shanghai";


    /**
     * 获取cosClient
     */
    public static COSClient cosClient() {
        // 1 初始化用户身份信息（secretId, secretKey）。
        COSCredentials cred = new BasicCOSCredentials(SecretId, SecretKey);
        // 2 设置 bucket 的区域, COS 地域的简称请参照
        // https://cloud.tencent.com/document/product/436/6224
        // clientConfig 中包含了设置 region, https(默认 http), 超时, 代理等 set 方法,
        // 使用可参见源码或者常见问题 Java SDK 部分。
        Region region = new Region(saveRegionStr);
        ClientConfig clientConfig = new ClientConfig(region);
        // 3 生成 cos 客户端。
        COSClient cosClient = new COSClient(cred, clientConfig);
        return cosClient;
    }

    /**
     * 上传
     *
     * @param cosClient
     * @param fileName
     * @param dir
     * @param data
     */
    public static void updateToCOS(COSClient cosClient, String fileName, String dir, byte[] data) {
        InputStream bis = new ByteArrayInputStream(data);
        ObjectMetadata meta = new ObjectMetadata();
        meta.setContentLength(data.length);
        PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, dir + fileName, bis, meta);
        cosClient.putObject(putObjectRequest);
    }
}
