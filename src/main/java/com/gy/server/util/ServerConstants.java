package com.gy.server.util;

import java.time.LocalDateTime;
import java.util.Date;

import com.gy.server.utils.time.DateTimeUtil;


/* 
 * <AUTHOR> 
 * @date 2019年9月7日 下午2:30:29 
 */
public class ServerConstants {

    public static final long PERIOD_FAST_MILLIS = 5L;//快速tick每帧时间,处理网络消息

    public static final long PERIOD_SLOW_MILLIS = 10L;//慢速tick每帧时间，处理逻辑



    /**
     * 系统时间偏移量（毫秒）
     */
    public static long systemTimeOffset = 0;
    /**
     * 缓存协议包数量
     */
    public static int cachePacketCount = 5;


    public static void init() throws Exception {

    }


    /**
     * 获取当前系统时间-毫秒数
     */
    public static long getCurrentTimeMillis() {
        return System.currentTimeMillis() + systemTimeOffset;
    }

    /**
     * 获取当前系统时间-Date，推荐使用{@link LocalDateTime}
     */
    public static Date getCurrentTimeDate() {
        return new Date(getCurrentTimeMillis());
    }

    /**
     * 获取当前系统时间-LocalDateTime
     */
    public static LocalDateTime getCurrentTimeLocalDateTime() {
        return DateTimeUtil.toLocalDateTime(getCurrentTimeMillis());
    }

}
