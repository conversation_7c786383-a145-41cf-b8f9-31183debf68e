package com.gy.server;

import java.util.Collection;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import com.gy.server.utils.structure.ElapsedTimeRecorder;

/* 时间统计器
 * <AUTHOR> 
 * @date 2019年9月7日 下午2:29:49 
 */
public class ElapsedTimeStatistics {

    /**
     * 累积耗时
     */
    private static Map<String, ElapsedTimeRecorder> recordMap = new ConcurrentHashMap<>();

    public static void addElapsedNanoTime(String name, long elapsedNanoTime) {
        addElapsedNanoTime(recordMap, name, elapsedNanoTime);
    }

    private static void addElapsedNanoTime(Map<String, ElapsedTimeRecorder> RecorderMap, String name, long elapsedNanoTime) {
        ElapsedTimeRecorder elapsedRecorder = RecorderMap.computeIfAbsent(name, ElapsedTimeRecorder::new);
        elapsedRecorder.addElapsedNanoTime(elapsedNanoTime);
    }

    public static ElapsedTimeRecorder getElapsedTimeRecorder(String name) {
        return recordMap.get(name);
    }

    public static Collection<ElapsedTimeRecorder> getElapsedTimeRecorders() {
        return recordMap.values();
    }

}
