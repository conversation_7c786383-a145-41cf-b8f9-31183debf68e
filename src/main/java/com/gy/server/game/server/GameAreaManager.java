package com.gy.server.game.server;


import com.gy.server.assistant.version.VersionManager;
import com.gy.server.core.command.ServerCommandRequest;
import com.gy.server.packet.PbCommons;
import com.gy.server.server.JsonArenaServer;
import com.gy.server.utils.runner.Runner;
import com.gy.server.utils.time.DateTimeUtil;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.rpc.CommandRequests;
import com.ttlike.server.tl.baselib.serialize.AreaServer;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @program: tl-billing
 * @description: 区服管理器
 * @author: <PERSON>.<PERSON>
 * @create: 2025/5/13
 **/
public class GameAreaManager implements Runner {

    private static Logger logger = LogManager.getLogger(GameAreaManager.class);
    private static final long UPDATE_INTERVAL = 10 * DateTimeUtil.MillisOfSecond;
    public static final long OFFLINE_HEART_INTERVAL = 10 * DateTimeUtil.MillisOfSecond;
    private final static GameAreaManager instance = new GameAreaManager();

    private static long recommendInterval = 3 * 1000;
    private long lastRecommendTime = 0;

    /**
     * 自动推荐的服务器
     * -1 表示无有效推荐，否则为具体推荐区号
     */
    private volatile int recommendAreaId = -1;

    /**
     * 最后一次更新入数据库的时间
     */
    private long lastUpdateTime;

    /**
     * server集合（key：serverNum，value：Server对象）
     */
    private ConcurrentHashMap<Integer, GameArea> servers = new ConcurrentHashMap<>();

    /**
     * center集合，Key是serverId Values:最后一次心跳的时间戳
     */
    private ConcurrentHashMap<Integer, Long> centerServer = new ConcurrentHashMap<>();

    private GameAreaManager() {

    }

    public static GameAreaManager getInstance() {
        return instance;
    }

    public int getRecommendAreaId() {
        return recommendAreaId;
    }

    public ConcurrentHashMap<Integer, GameArea> getServers() {
        return servers;
    }

    private static Comparator<GameArea> recommendComparator = (area1, area2) -> {
        if(area1.getRegistCount() == area2.getRegistCount()){
            return area1.getAreaNumber() - area2.getAreaNumber();
        }

        return area1.getRegistCount() - area2.getRegistCount();
    };

    public void calcRecommend() {
        List<GameArea> recommendAreas = new ArrayList<>();
        for (GameArea server : servers.values()) {
            if (server.getStatus() == AreaServer.Status.recommend
                    && server.isOnline()) {
                recommendAreas.add(server);
            }
        }

        if (recommendAreas.size() > 1) {
            recommendAreas.sort(recommendComparator);
        }

        if (recommendAreas.size() > 0) {
            List<Integer> tmp = new ArrayList<>();
            int size = recommendAreas.size() / 3;
            if(size < 1) {
                size = 1;
            }
            for(int i = 0; i < size; i++) {
                Integer areaId = recommendAreas.get(i).getAreaNumber();
                tmp.add(areaId);
            }
            Collections.shuffle(tmp);
            this.recommendAreaId = tmp.get(0);
        } else {
            this.recommendAreaId = -1;
        }
    }

    @Override
    public void runnerExecute() {
        if (this.lastRecommendTime + recommendInterval < System.currentTimeMillis()) {
            this.lastRecommendTime = System.currentTimeMillis();
            calcRecommend();
        } else if (this.lastRecommendTime > System.currentTimeMillis()) {
            //防止时间回退带来的刷新停滞
            this.lastRecommendTime = System.currentTimeMillis();
        }

        disconnectCheck();

        if (System.currentTimeMillis() - lastUpdateTime >= UPDATE_INTERVAL) {
            updateFromDb();
            lastUpdateTime = System.currentTimeMillis();
        }else if (lastUpdateTime > System.currentTimeMillis()) {
            //防止时间回退带来的刷新停滞
            lastUpdateTime = System.currentTimeMillis();
        }
    }

    public static void updateFromDb() {
        try {
            List<GameArea> serverList = GameAreaAssistant.getServerList();
            Set<Integer> serverNumSet = new HashSet<>();
            for (GameArea server : serverList) {
                serverNumSet.add(server.getAreaNumber());
                server.init();
                GameArea oldServer = getInstance().servers.get(server.getAreaNumber());
                if (oldServer == null) {
                    getInstance().servers.put(server.getAreaNumber(), server);
                } else {
                    oldServer.update(server);
                }
            }

            getInstance().servers.keySet().stream()
                    .filter(integer -> !serverNumSet.contains(integer))
                    .forEach(integer -> getInstance().servers.remove(integer));
        } catch (Exception e) {
            logger.catching(e);
        }
    }

    public static void disconnectCheck() {
        for (GameArea server : getInstance().servers.values()) {
            if (server.isOnline()
                    && System.currentTimeMillis() - server.getLastActiveTime() > OFFLINE_HEART_INTERVAL) {
                server.setOnline(false);
                logger.info(String.format("gate disconnect -> num:%s, id:%s, name:%s",
                        server.getAreaNumber(), server.getServerId(), server.getName()));
            }
        }
    }

    @Override
    public long getRunnerInterval() {
        return DateTimeUtil.MillisOfSecond;
    }

    public List<GameArea> getServersById(int serverId){
        List<GameArea> rst = new ArrayList<>();
        for(GameArea server : servers.values()){
            if(server.getServerId() == serverId){
                rst.add(server);
            }
        }
        return rst;
    }

    public Collection<JsonArenaServer> getServers(String ip){
        List<JsonArenaServer> rst = new ArrayList<>(servers.size());
        servers.values().forEach(area -> {
            JsonArenaServer jsonServer = new JsonArenaServer(area);
            switch (area.getStatus()){
                case maintain:{
                    if(area.getLimitIps().contains(ip)){
                        //维护白名单内，可登陆
                        jsonServer.status = AreaServer.Status.newOpen.ordinal();
                    }
                    break;
                }
                case limit:{
                    if(area.getLimitIps().contains(ip)){
                        //白名单内，可见可登陆
                        jsonServer.status = AreaServer.Status.recommend.ordinal();
                    }else{
                        //否则看不见
                        return;
                    }
                    break;
                }
                case toOpenForTest:
                case toOpenForPublic: {
                    //否则看不见
                    return;
                }
                case close:{
                    jsonServer.status = AreaServer.Status.maintain.ordinal();
                }
                default:{
                }
            }

            if(!area.isOnline()){
                jsonServer.status = AreaServer.Status.maintain.ordinal();
            }
            rst.add(jsonServer);
        });
        return rst;
    }
}
