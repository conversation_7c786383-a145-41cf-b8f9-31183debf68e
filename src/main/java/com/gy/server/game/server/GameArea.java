package com.gy.server.game.server;

import com.gy.server.utils.CollectionUtil;
import com.gy.server.utils.time.DateTimeFormatterType;
import com.gy.server.utils.time.DateTimeUtil;
import com.ttlike.server.tl.baselib.serialize.AreaServer;
import com.ttlike.server.tl.baselib.serialize.gm.GmtGameArea;
import org.apache.commons.lang3.StringUtils;

import javax.persistence.*;
import java.util.HashSet;
import java.util.Set;

/**
 * @program: xhx-billing
 * @description: 逻辑服实例
 * @author: Huang.<PERSON>a
 **/
@Entity
@Table(name = "server")
public class GameArea {
    @Id
    @Column(name = "server_num")
    private int areaNumber;

    @Column(name = "server_id")
    private int serverId;
    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    private AreaServer.Status status;
    @Column(name = "fluency_status")
    @Enumerated(EnumType.STRING)
    private AreaServer.FluencyStatus fluencyStatus;
    @Column(name = "open_time")
    private String openTime;
    @Column(name = "name")
    private String name;
    @Column(name = "limit_ips")
    private String limitIps;
    @Column(name = "keyword")
    private String keyword;
    @Transient
    private int registCount;
    @Transient
    private int onlineCount;
    // 服务器是否在线
    @Transient
    private volatile boolean isOnline;
    @Transient
    private volatile long lastActiveTime = System.currentTimeMillis();
    @Transient
    private Set<String> limitIpSet = new HashSet<>();

    public void init() {
        if (StringUtils.isNotEmpty(limitIps)) {
            String[] arrays = limitIps.split(",");
            limitIpSet = CollectionUtil.toCollection(arrays, HashSet::new);
        }
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public long getLastActiveTime() {
        return lastActiveTime;
    }

    public void setLastActiveTime(long lastActiveTime) {
        this.lastActiveTime = lastActiveTime;
    }

    public int getAreaNumber() {
        return areaNumber;
    }

    public void setAreaNumber(int areaNumber) {
        this.areaNumber = areaNumber;
    }

    public AreaServer.Status getStatus() {
        return status;
    }

    public void setStatus(AreaServer.Status status) {
        this.status = status;
    }

    public AreaServer.FluencyStatus getFluencyStatus() {
        return fluencyStatus;
    }

    public void setFluencyStatus(AreaServer.FluencyStatus fluencyStatus) {
        this.fluencyStatus = fluencyStatus;
    }

    public String getOpenTime() {
        return openTime;
    }

    public long getOpenTimeLong(){
        if(this.openTime == null || this.openTime.length() < 4){
            return -1;
        }
        return DateTimeUtil.toMillis(DateTimeFormatterType.date_time.parseString(this.openTime));
    }

    public void setOpenTime(String openTime) {
        this.openTime = openTime;
    }

    public String getLimitIps() {
        return limitIps;
    }

    public void setLimitIps(String limitIps) {
        this.limitIps = limitIps;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public int getRegistCount() {
        return registCount;
    }

    public void setRegistCount(int registCount) {
        this.registCount = registCount;
    }

    public int getOnlineCount() {
        return onlineCount;
    }

    public void setOnlineCount(int onlineCount) {
        this.onlineCount = onlineCount;
    }

    public boolean isOnline() {
        return isOnline;
    }

    public void setOnline(boolean online) {
        isOnline = online;
    }

    public Set<String> getLimitIpSet() {
        return limitIpSet;
    }

    public void setLimitIpSet(Set<String> limitIpSet) {
        this.limitIpSet = limitIpSet;
    }

    public int getServerId() {
        return serverId;
    }

    public void setServerId(int serverId) {
        this.serverId = serverId;
    }

    public void update(GameArea server){
        this.serverId = server.serverId;
        this.name = server.name;
        this.limitIpSet = new HashSet<>(server.limitIpSet);
        this.limitIps = StringUtils.join(this.limitIpSet, ",");
        this.status = server.status;
        this.fluencyStatus = server.fluencyStatus;
        this.openTime = server.openTime;
        this.keyword = server.keyword;
    }

    public void update(int registCount, int onlineCount){
        this.registCount = registCount;
        this.onlineCount = onlineCount;
        this.isOnline = true;
        this.lastActiveTime = System.currentTimeMillis();
    }

    public void updateLimitIps(){
        StringBuilder ipStr = new StringBuilder();
        for(String ip : getLimitIpSet()){
            if(ipStr.length() > 0){
                ipStr.append(",");
            }
            ipStr.append(ip);
        }

        this.limitIps = ipStr.toString();
    }

    public GmtGameArea toGmtGameArea(){
        GmtGameArea gmtGameArea = new GmtGameArea();
        gmtGameArea.setServerId(this.serverId);
        gmtGameArea.setAreaNumber(this.areaNumber);
        gmtGameArea.setServerId(this.serverId);
        gmtGameArea.setStats(this.status.name());
        gmtGameArea.setFluencyStatus(this.fluencyStatus.name());
        gmtGameArea.setOpenTime(this.openTime);
        gmtGameArea.setName(this.name);
        gmtGameArea.setLimitIps(this.limitIps);
        gmtGameArea.setKeyword(this.keyword);

        GameArea memArea = GameAreaManager.getInstance().getServers().get(this.areaNumber);
        if(memArea != null) {
            //动态数据
            gmtGameArea.setRegistCount(memArea.registCount);
            gmtGameArea.setOnlineCount(memArea.onlineCount);
            gmtGameArea.setIsOnline(memArea.isOnline() ? 1 : 0);
        }

        return gmtGameArea;
    }
}
