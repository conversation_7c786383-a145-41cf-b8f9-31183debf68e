package com.gy.server.game.server;

import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.serialize.AreaServer;

/**
 * @program: tl-billing
 * @description: 区服状态修改器
 * @author: <PERSON><PERSON>
 * @create: 2025/5/26
 **/
public class GameAreaStateAutoChangerAsync implements Runnable{
    private int areaId;

    public GameAreaStateAutoChangerAsync(int areaId) {
        this.areaId = areaId;
    }

    @Override
    public void run() {
        //使用分布式锁来防止同时修改
        TLBase.getInstance().getLockUtil().executeWithLock(() -> {
            GameArea area = GameAreaAssistant.find(areaId);
            if (area != null && area.getStatus() == AreaServer.Status.recommend) {
                area.setStatus(AreaServer.Status.newOpen);
                area.setFluencyStatus(AreaServer.FluencyStatus.hot);
                GameAreaAssistant.updateServer(area);
            }
        },"area_state_auto_changer_async_" + areaId);

    }
}
