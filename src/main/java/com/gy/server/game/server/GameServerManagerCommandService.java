package com.gy.server.game.server;

import com.gy.server.annotation.MessageMethod;
import com.gy.server.annotation.MessageServiceBean;
import com.gy.server.core.MessageServerType;
import com.gy.server.core.MethodInvokeType;
import com.gy.server.core.command.CommandRequestParams;
import com.gy.server.core.command.ServerCommandRequest;
import com.ttlike.server.tl.baselib.serialize.AreaServer;
import com.ttlike.server.tl.baselib.thread.ThreadPool;

import java.util.List;

@MessageServiceBean(description = "游戏服务器", messageServerType = MessageServerType.billing)
public class GameServerManagerCommandService{

	private final static GameServerManagerCommandService instance = new GameServerManagerCommandService();

	private GameServerManagerCommandService() {
	}

	public static GameServerManagerCommandService getInstance() {
		return instance;
	}

	@MessageMethod(description = "来自游戏的消息", invokeType = MethodInvokeType.async)
	private static void heart(ServerCommandRequest request, CommandRequestParams params) {
		int nodeId = params.getParam(0);
		List<GameArea> nodes = GameAreaManager.getInstance().getServersById(nodeId);
		int onlineCount = params.getParam(1);
		int registerCount = params.getParam(2);

		for (GameArea node : nodes) {
			node.update(registerCount, onlineCount);
		}

		//推荐状态如果注册满，改为火爆
		if(params.getParams().length > 3) {
			boolean isFull = params.getParam(3);
			if(isFull){
				for (GameArea node : nodes) {
					if (node.getStatus() == AreaServer.Status.recommend) {
						ThreadPool.execute(new GameAreaStateAutoChangerAsync(node.getAreaNumber()));
					}
				}
			}
		}
	}
}
