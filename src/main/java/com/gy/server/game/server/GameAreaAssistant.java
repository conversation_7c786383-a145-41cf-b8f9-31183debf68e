package com.gy.server.game.server;

import com.gy.server.db.relation.hibernate.HibernateUtil;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * GameArea数据库访问工具类
 */
public class GameAreaAssistant {

    /**
     * 查询服务器信息
     */
    public static List<GameArea> getServerList() {
        String hql = "from GameArea area";
        return HibernateUtil.query(GameArea.class, hql);
    }

    public static List<GameArea> getServerList(int[] serverNumbers){
        List<GameArea> areas = new ArrayList<>();
        if(serverNumbers.length <= 5){
            for(int serverId : serverNumbers){
                GameArea area = GameAreaAssistant.find(serverId);
                if(area != null){
                    areas.add(area);
                }
            }
        }else{
            List<GameArea> all = GameAreaAssistant.getServerList();
            for(int serverId : serverNumbers) {
                for (GameArea area : all) {
                    if (area.getAreaNumber()== serverId) {
                        areas.add(area);
                    }
                }
            }
        }
        return areas;
    }

    /**
     * 增加服务器信息
     */
    public static void addServer(GameArea server) {
        HibernateUtil.save(server);
    }

    public static void updateServer(GameArea server) {
        HibernateUtil.update(server);
    }

    public static void saveOrUpdate(GameArea server) {
        HibernateUtil.update(server);
    }

    public static void deleteArea(GameArea server) {
        HibernateUtil.delete(server);
    }

    /**
     * 分页查询道具信息
     */
    public static List<GameArea> getServerByPage(int pageIndex, int pageSize) {
        String hql = "from GameArea area";
        return HibernateUtil.queryPage(GameArea.class, hql, pageSize, pageIndex);
    }

    public static GameArea find(int id) {
        return HibernateUtil.find(GameArea.class, id);
    }

    public static void addBatch(List<GameArea> dataList) {
        HibernateUtil.saveBatch(dataList);
    }

    public static void updateBatch(List<GameArea> dataList) {
        HibernateUtil.updateBatch(dataList);
    }

}
