/* 
* <AUTHOR> 
* @date 2019年9月16日 下午2:37:27 
* @version 1.0 
*/ 
package com.gy.server.message;
import java.io.FileNotFoundException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;

import com.gy.server.utils.XmlUtil;

import org.dom4j.Element;
import com.gy.server.Configuration;
/* 
 * <AUTHOR> 
 * @date 2019年9月16日 下午2:37:27 
 */
public enum DefaultMessage {
	params_error,
    server_error,
    account_register_fail,
    account_register_success,
    account_account_not_match_password,
    account_login_fail,
    account_login_success,
    account_exist,
    account_sign_error,
    login_check_token_error,
    login_check_token_overdue,
    login_check_success,
    cdkey_list_empty,
    cdkey_reward_empty,
    count_is_over_5000_failure,;

    public static void init() throws Exception {
        String messageFileName = "default_message.xml";
        InputStream inputStream = DefaultMessage.class.getClassLoader().getResourceAsStream(messageFileName);
        if (inputStream == null) {
            throw new FileNotFoundException("file not found -> " + messageFileName);
        }
        Element rootElement = XmlUtil.loadDocumentElement(inputStream);
        Element[] elements = XmlUtil.getChildren(rootElement, "message");
        for (Element element : elements) {
            DefaultMessage message = DefaultMessage.valueOf(XmlUtil.getChildText(element, "id"));
            for (MessageLanguage messageLanguage : MessageLanguage.values()) {
                message.updateText(messageLanguage, XmlUtil.getChildText(element, messageLanguage.name()));
            }
        }
    }

    private Map<MessageLanguage, String> texts = new HashMap<>();

    private void updateText(MessageLanguage language, String text) {
        texts.put(language, text);
    }

    public String getText(MessageLanguage language) {
        return texts.getOrDefault(language, "null");
    }

    public String getText() {
        return getText(Configuration.getLanguage());
    }
}
