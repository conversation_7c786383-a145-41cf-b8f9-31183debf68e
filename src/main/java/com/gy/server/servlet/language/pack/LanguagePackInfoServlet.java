package com.gy.server.servlet.language.pack;

import java.io.IOException;
import java.util.Comparator;
import java.util.List;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.gy.server.assistant.languagePack.LanguagePack;
import com.gy.server.assistant.languagePack.LanguagePackManager;
import com.gy.server.utils.CharsetEncoding;
import com.gy.server.utils.JsonUtil;
import com.gy.server.assistant.version.Version;

@WebServlet(name = "LanguagePackInfoServlet", urlPatterns = "/language_pack_info")
public class LanguagePackInfoServlet extends HttpServlet {

    private static Comparator<LanguagePack> comparator = (o1, o2) -> {
        int result = o1.getPlatform().compareTo(o2.getPlatform());
        if (result == 0) {
            result = Version.versionCompare(o1.getVersion(), o2.getVersion());
            if (result == 0) {
                result = o1.getId() - o2.getId();
            }
        }
        return result;
    };

    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        this.doPost(request, response);
    }

    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        request.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);
        response.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);

        List<LanguagePack> packs = LanguagePackManager.getAllLanguagePacks();
        packs.sort(comparator);

        response.getWriter().print(JsonUtil.list2Json(packs));
    }
}
