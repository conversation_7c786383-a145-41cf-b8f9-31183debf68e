package com.gy.server.servlet.language.pack;

import java.io.IOException;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.gy.server.assistant.languagePack.LanguagePackAssistant;
import com.gy.server.assistant.languagePack.LanguagePackManager;
import com.gy.server.utils.CharsetEncoding;
import com.gy.server.utils.StringUtil;

@WebServlet(name = "LanguagePackDeleteServlet", urlPatterns = "/language_pack_delete")
public class LanguagePackDeleteServlet extends HttpServlet {

    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        this.doGet(request, response);
    }

    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        request.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);
        response.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);

        if (!LanguagePackAssistant.checkAuthValidity(request.getParameter("auth"))) {
            response.getWriter().write("auth fail");
            return;
        }

        int[] ids = StringUtil.splitToIntArray(request.getParameter("ids"), ",");

        for (int id : ids) {
            LanguagePackManager.removeLanguagePack(id);
        }

        response.getWriter().write("success");
    }
}
