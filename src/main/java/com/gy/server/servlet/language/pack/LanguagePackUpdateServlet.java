package com.gy.server.servlet.language.pack;

import java.io.IOException;
import java.util.List;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.google.gson.reflect.TypeToken;
import com.gy.server.assistant.languagePack.LanguagePack;
import com.gy.server.assistant.languagePack.LanguagePackAssistant;
import com.gy.server.assistant.languagePack.LanguagePackManager;
import com.gy.server.utils.CharsetEncoding;
import com.gy.server.utils.CollectionUtil;
import com.gy.server.utils.JsonUtil;

/**
 * <AUTHOR> - [Created on 2018/6/23 11:20]
 */
@WebServlet(name = "LanguagePackUpdateServlet", urlPatterns = "/language_pack_update")
public class LanguagePackUpdateServlet extends HttpServlet {

    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        this.doGet(request, response);
    }

    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        request.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);
        response.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);

        String result = "success";

        if (!LanguagePackAssistant.checkAuthValidity(request.getParameter("auth"))
                && !request.getRemoteHost().startsWith("192.168.1")) {
            //客户端会用到该接口自动提交更新记录，仅限于内网
            result = "auth fail";
        } else {
            String data = request.getParameter("data");
            List<LanguagePack> list = JsonUtil.json2Collection(data, new TypeToken<List<LanguagePack>>() {
            }.getType());

            if (CollectionUtil.isNotEmpty(list)) {
                for (LanguagePack languagePack : list) {
                    if (!LanguagePackAssistant.isValidVersionText(languagePack.getVersion())) {
                        result = "version is invalid";
                    } else if (LanguagePackManager.getAllLanguagePacks().stream().anyMatch(pack -> pack.getId() != languagePack.getId() && pack.isSame(languagePack))) {
                        result = "already exist";
                    } else {
                        languagePack.init();
                        if (LanguagePackManager.getLanguagePackById(languagePack.getId()) != null) {
                            languagePack.update();
                        } else {
                            languagePack.save();
                        }
                    }
                }
            }

            LanguagePackManager.updateFromDb();
        }

        response.getWriter().write(result);
    }
}
