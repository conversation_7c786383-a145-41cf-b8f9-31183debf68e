package com.gy.server.servlet.language.pack;

import java.io.IOException;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.gy.server.assistant.languagePack.LanguagePack;
import com.gy.server.assistant.languagePack.LanguagePackAssistant;
import com.gy.server.assistant.languagePack.LanguagePackManager;
import com.gy.server.utils.CharsetEncoding;
import com.gy.server.utils.StringUtil;

/**
 * <AUTHOR> - [Created on 2019/2/16 11:13]
 */
@WebServlet(name = "LanguagePackLimitIpClearServlet", urlPatterns = "/language_pack_limit_ip_clear")
public class LanguagePackLimitIpClearServlet extends HttpServlet {

    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        this.doGet(request, response);
    }

    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        request.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);
        response.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);

        String result = "success";

        if (!LanguagePackAssistant.checkAuthValidity(request.getParameter("auth"))) {
            result = "auth fail";
        } else {
            int[] languagePackIds = StringUtil.splitToIntArray(request.getParameter("languagePackIds"), ",");
            for (int languagePackId : languagePackIds) {
                LanguagePack languagePack = LanguagePackManager.getLanguagePackById(languagePackId);
                if (languagePack != null) {
                    languagePack.getLimitIpSet().clear();
                    languagePack.update();
                }
            }

            LanguagePackManager.updateFromDb();
        }

        response.getWriter().write(result);
    }
}
