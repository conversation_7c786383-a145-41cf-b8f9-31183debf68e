package com.gy.server.servlet.channel;

import java.io.IOException;
import java.util.List;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.gy.server.assistant.channel.Channel;
import com.gy.server.assistant.channel.ChannelAssistant;
import com.gy.server.utils.CharsetEncoding;
import com.gy.server.utils.JsonUtil;

/**
 * <AUTHOR> - [Created on 2018/4/27 10:09]
 */
@WebServlet(name = "ChannelListServlet", urlPatterns = "/channel_list")
public class ChannelListServlet extends HttpServlet {

    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        this.doGet(request, response);
    }

    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        request.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);
        response.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);

        int pageIndex = Integer.parseInt(request.getParameter("pageIndex"));
        int pageSize = Integer.parseInt(request.getParameter("pageSize"));

        List<Channel> channels = ChannelAssistant.getChannels();

        response.getWriter().write(JsonUtil.list2Json(channels));
    }
}
