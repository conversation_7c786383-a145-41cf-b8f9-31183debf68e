package com.gy.server.servlet.prerecruit;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

import javax.servlet.AsyncContext;
import javax.servlet.ServletException;
import javax.servlet.ServletResponse;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.gy.server.core.MessageServerType;
import com.gy.server.core.callback.response.CallbackResponse;
import com.gy.server.core.command.CommandUtil;
import com.gy.server.core.command.ServerCommandRequest;
import com.gy.server.utils.JsonUtil;
import com.gy.server.utils.Md5Util;
import com.ttlike.server.tl.baselib.CommonsConfiguration;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.rpc.TLMessageCallbackWebTask;
import com.ttlike.server.tl.baselib.util.BaseCommonUtil;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * @作者 FanHaojie
 * @描述 预抽卡奖励发放
 */
@WebServlet(name = "advanceLotteryExchangeServlet", urlPatterns = "/advance_lottery_exchange", asyncSupported = true)
public class AdvanceLotteryExchangeServlet extends HttpServlet {
    private static Logger logger = LogManager.getLogger(AdvanceLotteryExchangeServlet.class);
    /** 签名私钥 */
    public static final String SIGN_KEY_TEST = "j1Sq!OS3huWoEoNQ";
    public static final String SIGN_KEY = "ImAi4QZJW7aVTgWkV3guf1b0FSN6rymo";
    /** 成功 */
    public static final int RESULT_SUCCESS = 10000;
    /** 服务器异常 */
    private static final int RESULT_FAIL = 10001;
    /** 签名失败 */
    private static final int RESULT_FAIL_SIGN = 10002;
    /** 角色唯一标识非法 */
    private static final int RESULT_FAIL_ROLE_ID = 10003;

    @Override
    protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {

        Map<String, Object> results = new HashMap<>();
        try {
            //http://localhost:8080/xhx-billing/advance_lottery_exchange?roleId=3000228&items=MnwxMDAxM3wx&uid=1611899327241907&sign=55AA6C95C7EE1FBAC56A507F8F8F1B4C
            //            new String(Base64.getUrlEncoder().encode("2|10013|1".getBytes()));
			logger.info("AdvanceLotteryExchangeServlet, Parameter = roleId : {}, items : {}, uid : {}, sign : {}",
					req.getParameter("roleId"), req.getParameter("items"), req.getParameter("uid"),
					req.getParameter("sign"));
            String items = new String(Base64.getUrlDecoder().decode(req.getParameter("items")));
            String stringForSign = "item=" + items + "&roleId=" + req.getParameter("roleId") + "&uid="
                    + req.getParameter("uid") + "&"
					+ (CommonsConfiguration.runMode.isLive() ? SIGN_KEY : SIGN_KEY_TEST);
            String sSign = Md5Util.MD5(stringForSign);//计算签名
            logger.info("AdvanceLotteryUserRoleData MD5 Sign, sign = {}", sSign);

            if (sSign.equalsIgnoreCase(req.getParameter("sign"))) {//校验签名
                long roleId = Long.parseLong(req.getParameter("roleId"));
                if (roleId <= 0) {
                    results.put("code", RESULT_FAIL_ROLE_ID);
                    String rst = JsonUtil.map2Json(results);
                    resp.getOutputStream().write(rst.getBytes(StandardCharsets.UTF_8));
                    return;
                }

                int serverId = BaseCommonUtil.getServerId(roleId);
                ServerCommandRequest commandRequest = CommandUtil.newServerCommandRequest("BillingCommandService.advanceLotteryExchange", MessageServerType.billing, serverId);
                TLBase.getInstance().getRpcUtil().sendToNodeWithCallBack(new TLMessageCallbackWebTask(req) {

                    @Override
                    public void webComplete(AsyncContext asyncContext, CallbackResponse callbackResponse) {
                        ServletResponse response = asyncContext.getResponse();
                        Map<String, Object> results = new HashMap<>();
                        results.put("code", AdvanceLotteryExchangeServlet.RESULT_SUCCESS);
                        results.put("data", "成功");
                        String rst = JsonUtil.map2Json(results);
                        try {
                            response.getOutputStream().write(rst.getBytes(StandardCharsets.UTF_8));
                            response.getOutputStream().flush();
                            response.flushBuffer();
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    }

                    @Override
                    public void webTimeout(AsyncContext asyncContext) {
                        ServletResponse response = asyncContext.getResponse();
                        try {
                            response.getWriter().write("Fail");
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    }
                }, ServerType.GAME, serverId, commandRequest, roleId, items);

                return;
            } else {
                results.put("code", RESULT_FAIL_SIGN);//签名不符
            }
        } catch (Exception e) {
            e.printStackTrace();
            results.put("code", RESULT_FAIL);
        }
        String rst = JsonUtil.map2Json(results);
        resp.getOutputStream().write(rst.getBytes(StandardCharsets.UTF_8));
    }

    @Override
    protected void doPost(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        doGet(req, resp);
    }

}
