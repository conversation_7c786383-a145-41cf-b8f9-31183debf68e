package com.gy.server.servlet.prerecruit;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.*;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.gy.server.assistant.login.LoginAccountType;
import com.gy.server.utils.JsonUtil;
import com.gy.server.utils.Md5Util;
import com.ttlike.server.tl.baselib.CommonsConfiguration;
import com.ttlike.server.tl.baselib.serialize.player.RoleInfo;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * @作者 FanHaojie
 * @描述 预抽卡玩家账号数据查询
 */
@WebServlet(name = "AdvanceLotteryUserRoleDataServlet", urlPatterns = "/advance_lottery_user_role_data")
public class AdvanceLotteryUserRoleDataServlet extends HttpServlet {

	private static Logger logger = LogManager.getLogger(AdvanceLotteryUserRoleDataServlet.class);

	/**
	 * 全部畅游账号渠道
	 */
	private static final Set<Integer> ACCOUNT_TYPE_SET = new HashSet<Integer>() {
		{
			add(LoginAccountType.GuangYao.id);//测试用例：151测试服渠道
			add(LoginAccountType.ChangYou.id);
		}
	};
	/**
	 * 成功
	 */
	private static final int RESULT_SUCCESS = 10000;
	/**
	 * 服务器异常
	 */
	private static final int RESULT_FAIL = 10001;
	/**
	 * 签名失败
	 */
	private static final int RESULT_FAIL_SIGN = 10002;
	/**
	 * 账号唯一标识非法
	 */
	private static final int RESULT_FAIL_ACCOUNT = 10003;

	@Override
	protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {

		Map<String, Object> results = new HashMap<>();
		try {
			//http://localhost:8080/xhx-billing/advance_lottery_user_role_data?uid=****************&sign=3774CEC6EB741CEE8C98802700318F0F
			logger.info("AdvanceLotteryUserRoleDataServlet, Parameter = uid : {}, sign : {}", req.getParameter("uid"),
					req.getParameter("sign"));
			String stringForSign = "uid=" + req.getParameter("uid") + "&"
					+ (CommonsConfiguration.runMode.isLive() ? AdvanceLotteryExchangeServlet.SIGN_KEY
							: AdvanceLotteryExchangeServlet.SIGN_KEY_TEST);
			String sSign = Md5Util.MD5(stringForSign);//计算签名
			logger.info("AdvanceLotteryUserRoleData MD5 Sign, sign = {}", sSign);

			if (sSign.equalsIgnoreCase(req.getParameter("sign"))) {//校验签名
				String account = req.getParameter("uid");
				if (account.length() <= 0) {
					results.put("code", RESULT_FAIL_ACCOUNT);
					String rst = JsonUtil.map2Json(results);
					resp.getOutputStream().write(rst.getBytes(StandardCharsets.UTF_8));
					return;
				}
				results.put("code", RESULT_SUCCESS);
				List<RoleInfo> roleInfos = new ArrayList<RoleInfo>();
				for (Integer i : ACCOUNT_TYPE_SET) {
					//TODO 需要关联账号id和玩家信息，并且加缓存
//					roleInfos.addAll(DataCenter.getInstance().getPlayerUtils().getRoleInfos(account, i));
				}
				List<MiniData> datas = new ArrayList<>();
				for (RoleInfo ri : roleInfos) {
					datas.add(MiniData.create(ri));
				}
				results.put("data", datas);
			} else {
				results.put("code", RESULT_FAIL_SIGN);//签名不符
			}
		} catch (Exception e) {
			e.printStackTrace();
			results.put("code", RESULT_FAIL);
		}
		String rst = JsonUtil.map2Json(results);
		resp.getOutputStream().write(rst.getBytes(StandardCharsets.UTF_8));
	}

	@Override
	protected void doPost(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
		doGet(req, resp);
	}

}

class MiniData {

	private String roleId;
	private String serverNumber;
	private String roleName;

	public String getRoleId() {
		return roleId;
	}

	public void setRoleId(String roleId) {
		this.roleId = roleId;
	}

	public String getServerNumber() {
		return serverNumber;
	}

	public void setServerNumber(String serverNumber) {
		this.serverNumber = serverNumber;
	}

	public String getRoleName() {
		return roleName;
	}

	public void setRoleName(String roleName) {
		this.roleName = roleName;
	}

	public static MiniData create(RoleInfo role) {
		MiniData m = new MiniData();
		m.roleId = String.valueOf(role.playerId);
		m.roleName = role.name;
		m.serverNumber = String.valueOf(role.serverNum);
		return m;
	}

}
