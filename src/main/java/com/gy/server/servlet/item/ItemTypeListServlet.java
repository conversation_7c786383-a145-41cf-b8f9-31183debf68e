package com.gy.server.servlet.item;

import com.gy.server.core.callback.response.CallbackResponse;
import com.gy.server.core.command.ServerCommandRequest;
import com.gy.server.utils.CharsetEncoding;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.rpc.CommandRequests;
import com.ttlike.server.tl.baselib.rpc.TLMessageCallbackWebTask;

import javax.servlet.AsyncContext;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR> - [Created on 2018/4/3 10:33]
 */
@WebServlet(name = "ItemTypeListServlet", urlPatterns = "/item_type_list", asyncSupported = true)
public class ItemTypeListServlet extends HttpServlet {

    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        this.doGet(request, response);
    }

    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        request.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);
        response.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);

        String dropType = request.getParameter("dropType");
        String itemType = request.getParameter("itemType");

        ServerCommandRequest serverCommandRequest = CommandRequests.newServerCommandRequest("BillingCommandService.getItemTypeInfo");
        TLBase.getInstance().getRpcUtil().sendToAnyNodeWithCallBack(new TLMessageCallbackWebTask(request) {
            @Override
            public void webComplete(AsyncContext asyncContext, CallbackResponse callbackResponse) {

                try {
                    String jsonArray = callbackResponse.getParam(0);
                    asyncContext.getResponse().getWriter().write(jsonArray);
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void webTimeout(AsyncContext asyncContext) {
                try {
                    asyncContext.getResponse().getWriter().write("timeout");
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }, ServerType.GM, serverCommandRequest, dropType == null ? "" : dropType, itemType == null ? "" : itemType);


    }

}
