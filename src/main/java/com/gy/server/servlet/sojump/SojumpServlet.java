package com.gy.server.servlet.sojump;

import com.google.gson.JsonObject;
import com.gy.server.core.command.ServerCommandRequest;
import com.gy.server.utils.CharsetEncoding;
import com.gy.server.utils.JsonUtil;
import com.ttlike.server.tl.baselib.CommonsConfiguration;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.rpc.CommandRequests;
import com.ttlike.server.tl.baselib.util.BaseCommonUtil;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import sun.misc.BASE64Decoder;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLDecoder;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;

@WebServlet(name = "SojumpServlet", urlPatterns = "/sojump")
public class SojumpServlet extends HttpServlet {

    private static Logger logger = LogManager.getLogger(SojumpServlet.class);

    private static final String SECURITY_KEY = "9359aa7c4ccf4dcf93a04de421482fe3";
    private static final String ALGO = "AES";
    private static final String ALGO_MODE = "AES/CBC/NoPadding";

    @Override
    protected void doPost(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {

        req.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);
        resp.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);

        try {
            String rawString = IOUtils.toString(req.getInputStream(), Charset.defaultCharset());
            logger.info("sojump rawString: " + rawString);

            if (StringUtils.isNotEmpty(rawString)) {
                String jsonString = StringUtils.EMPTY;

                // 问卷星传过来的格式有两种, 一种有content=, 一种没有
                if (rawString.startsWith("content=")) {
                    String content = rawString.substring(8);
                    content = URLDecoder.decode(content, "UTF-8");
                    if (StringUtils.isNotEmpty(content)) {
                        jsonString = aesDecrypt(content, SECURITY_KEY);
                    } else {
                        logger.info("sojump content is empty.");
                    }
                } else {
                    jsonString = aesDecrypt(rawString, SECURITY_KEY);
                }

                if (StringUtils.isNotBlank(jsonString)) {
                    logger.info("sojump param json:{}", jsonString);
                    JsonObject json = JsonUtil.getJsonObject(jsonString);
                    if (json != null) {
                        JsonObject answer = json.get("answer").getAsJsonObject();
                        long id = answer.get("activity").getAsLong();

                        // 目前的[2]下标的参数是APP_KEY，后续畅游想将这个参数挪到至[0]
                        String params = answer.get("sojumpparm").getAsString();
                        String[] split = params.split(";");
                        long playerId = Long.parseLong(split[0]);
                        int templateId = Integer.parseInt(split[1]);

                        int serverId = BaseCommonUtil.getServerId(playerId);
                        if (serverId > 0) {
                            ServerCommandRequest r1 = CommandRequests.newServerCommandRequest("GameMasterCommandService.questionnaireReward");
                            TLBase.getInstance().getRpcUtil().sendToNode(ServerType.GAME, serverId, r1, playerId, id, templateId);
                        }
                    } else {
                        logger.info("sojump param json is empty.");
                    }
                } else {
                    logger.info("sojump aes decrypt result is empty.");
                }
            } else {
                logger.info("sojump not found content.");
            }
        } catch (Exception e) {
            logger.catching(e);
        }
    }

    private String aesDecrypt(String encryptedData, String securityKey) {
        if(CommonsConfiguration.runMode.isPress()){
            //压测模式数据无需解密
            return encryptedData;
        }

        try {
            byte[] data = (new BASE64Decoder()).decodeBuffer(encryptedData);
            byte[] iv = Arrays.copyOfRange(data, 0, 16);
            Cipher cipher = Cipher.getInstance(ALGO_MODE);
            SecretKeySpec keySpec = new SecretKeySpec(securityKey.getBytes(StandardCharsets.UTF_8), ALGO);
            IvParameterSpec ivSpec = new IvParameterSpec(iv);
            cipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec);
            byte[] countent = Arrays.copyOfRange(data, 16, data.length);
            byte[] original = cipher.doFinal(countent);
            String originalString = new String(original);
            return originalString.trim();
        } catch (Exception e) {
            logger.error(e);
            return null;
        }
    }

}
