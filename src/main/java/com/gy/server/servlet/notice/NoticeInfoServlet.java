package com.gy.server.servlet.notice;

import java.io.IOException;
import java.util.List;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.gy.server.assistant.notice.Notice;
import com.gy.server.assistant.notice.NoticeManager;
import com.gy.server.utils.CharsetEncoding;
import com.gy.server.utils.JsonUtil;

/**
 * <AUTHOR> - [Created on 2018/4/27 18:11]
 */
@WebServlet(name = "NoticeInfoServlet", urlPatterns = "/notice_info")
public class NoticeInfoServlet extends HttpServlet {
    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        this.doGet(request, response);
    }

    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        request.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);
        response.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);

        List<Notice> notices = NoticeManager.getAllNotices();

        response.getWriter().print(JsonUtil.list2Json(notices));
    }
}

