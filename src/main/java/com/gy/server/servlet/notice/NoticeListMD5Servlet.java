package com.gy.server.servlet.notice;

import com.gy.server.assistant.notice.Notice;
import com.gy.server.assistant.notice.NoticeManager;
import com.gy.server.servlet.Md5RequestHttpServlet;
import com.gy.server.utils.CharsetEncoding;
import com.gy.server.utils.JsonUtil;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;

/**
 * <AUTHOR> - [Created on 2018/8/27 5:32]
 */
@WebServlet(name = "NoticeListMD5Servlet", urlPatterns = "/notice_list_md5")
public class NoticeListMD5Servlet extends Md5RequestHttpServlet {

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        this.doPost(request, response);
    }

    @Override
    public Map<Object, Object> doPost0(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        request.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);
        response.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);


        String accountType = request.getParameter("accountType");
        String language = request.getParameter("language");
        /**
         * 子渠道：畅游四位
         */
        String subChannel = request.getParameter("subChannel");
        String noticeType = request.getParameter("noticeType");
        if (subChannel == null) {
            //默认内网环境
            subChannel = "1234";
        }
        Set<String> set = new HashSet<>();
        set.add(noticeType);

        List<Notice.JsonNotice> noticeNewList = NoticeManager.getNoticeByChannelAndLanguage(accountType, language, set, subChannel);

        Map<Object, Object> resultMap = new HashMap<>();
        resultMap.put("noticeList", noticeNewList);
        return resultMap;
    }
}
