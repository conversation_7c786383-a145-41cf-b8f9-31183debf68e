package com.gy.server.servlet.notice;

import java.util.HashMap;
import java.util.Map;

public enum NoticeType {

    activity("activity", "活动公告"),

    maintenance("maintenance", "运维公告"),

    ;


    private String id;

    private String name;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static void setData(Map<String, NoticeType> data) {
        NoticeType.data = data;
    }

    public static Map<String, NoticeType> getData() {
        return data;
    }

    NoticeType(String id, String name) {
        this.id = id;
        this.name = name;
    }

    private static Map<String, NoticeType> data = new HashMap<>();

    static {
        for (NoticeType value : NoticeType.values()) {
            data.put(value.getId(),value);
        }
    }
}
