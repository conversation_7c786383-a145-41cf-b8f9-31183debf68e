package com.gy.server.servlet.notice;

import java.io.IOException;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.gy.server.assistant.notice.Notice;
import com.gy.server.assistant.notice.NoticeAssistant;
import com.gy.server.assistant.notice.NoticeManager;
import com.gy.server.utils.CharsetEncoding;
import com.gy.server.utils.JsonUtil;

/**
 * <AUTHOR> - [Created on 2018/8/27 5:34]
 */
@WebServlet(name = "NoticeUpdateServlet", urlPatterns = "/notice_update")
public class NoticeUpdateServlet extends HttpServlet {

    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        this.doGet(request, response);
    }

    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        request.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);
        response.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);

        if (!NoticeAssistant.checkAuthValidity(request.getParameter("auth"))) {
            response.getWriter().write("auth fail");
            return;
        }
        String data = request.getParameter("data");
        Gson g = new GsonBuilder().setDateFormat("yyyy-MM-dd HH:mm:ss").create();
        Notice notice = g.fromJson(data, Notice.class);
        notice.init();
        if (NoticeManager.getNoticeById(notice.getNoticeId()) == null) {
            notice.save();
        } else {
            notice.update();
        }

        NoticeManager.updateFromDb();

        response.getWriter().write("success");
    }
}
