package com.gy.server.servlet.notice;

import java.io.IOException;
import java.util.Objects;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.gy.server.assistant.notice.NoticeAssistant;
import com.gy.server.assistant.notice.NoticeManager;
import com.gy.server.utils.CharsetEncoding;

/**
 * <AUTHOR> - [Created on 2018/8/28 20:01]
 */
@WebServlet(name = "NoticeDeleteServlet", urlPatterns = "/notice_delete")
public class NoticeDeleteServlet extends HttpServlet {

    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        this.doGet(request, response);
    }

    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws IOException {
        request.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);
        response.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);


        if (!NoticeAssistant.checkAuthValidity(request.getParameter("auth"))) {
            response.getWriter().write("auth fail");
            return;
        }

        String noticeIds = request.getParameter("noticeId");
        if (Objects.isNull(noticeIds)) {
            response.getWriter().write("parameter is null");
            return;
        }

        for (String num : noticeIds.split(",")) {
            NoticeManager.removeNotice(Integer.parseInt(num));
        }

        response.getWriter().write("success");
    }
}