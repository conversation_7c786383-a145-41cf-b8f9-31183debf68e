package com.gy.server.servlet.notice;

import com.gy.server.assistant.notice.Notice;
import com.gy.server.assistant.notice.NoticeManager;
import com.gy.server.utils.CharsetEncoding;
import com.gy.server.utils.JsonUtil;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> - [Created on 2018/8/27 5:32]
 */
@WebServlet(name = "NoticeListServlet", urlPatterns = "/notice_list")
public class NoticeListServlet extends HttpServlet {

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        this.doPost(request, response);
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        request.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);
        response.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);

        String channel = request.getParameter("channel");
        String language = request.getParameter("language");
        String platformChannel = request.getParameter("platformChannel");
        if (platformChannel == null) {
            //默认内网环境
            platformChannel = "1234";
        }
        Set<String> set = new HashSet<>();
        for (NoticeType value : NoticeType.values()) {
            if (value != NoticeType.maintenance) {
                set.add(value.getId());
            }
        }

        List<Notice.JsonNotice> noticeNewList = NoticeManager.getNoticeByChannelAndLanguage(channel, language, set, platformChannel);

        response.getWriter().print(JsonUtil.list2Json(noticeNewList));
    }
}
