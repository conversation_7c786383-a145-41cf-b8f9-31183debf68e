package com.gy.server.servlet.user;

import java.io.IOException;
import java.util.List;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.gy.server.assistant.user.User;
import com.gy.server.assistant.user.UserAssistant;
import com.gy.server.utils.CharsetEncoding;
import com.gy.server.utils.JsonUtil;

/**
 * <AUTHOR> - [Created on 2018/5/4 13:16]
 */
@WebServlet(name = "UserListServlet", urlPatterns = "/user_list")
public class UserListServlet extends HttpServlet {

    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        this.doGet(request, response);
    }

    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        request.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);
        response.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);

        //检查用户是否登录
        if(!UserAssistant.checkUserAuthority(request, response)){
            return;
        }

        int pageIndex = Integer.parseInt(request.getParameter("pageIndex"));
        int pageSize = Integer.parseInt(request.getParameter("pageSize"));

        List<User> users = UserAssistant.getUserByPage(pageIndex, pageSize);

        response.getWriter().write(JsonUtil.list2Json(users));
    }
}