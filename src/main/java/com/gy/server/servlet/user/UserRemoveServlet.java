package com.gy.server.servlet.user;

import java.io.IOException;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.gy.server.servlet.user.login.UserLoginServlet;
import com.gy.server.assistant.user.User;
import com.gy.server.assistant.user.UserAssistant;
import com.gy.server.utils.CharsetEncoding;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR> - [Created on 2018/5/4 15:53]
 */
@WebServlet(name = "UserRemoveServlet", urlPatterns = "/user_remove")
public class UserRemoveServlet extends HttpServlet {

    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        this.doGet(request, response);
    }

    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        request.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);
        response.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);

        //检查用户是否登录
        if(!UserAssistant.checkUserAuthority(request, response)){
            return;
        }

        String username = request.getParameter("username");

        String curUserName = ((User) request.getSession().getAttribute(UserLoginServlet.SESSION_IDENTIFY_KEY)).getUsername();
        if (StringUtils.equals(username, curUserName)) {
            response.getWriter().write("不能删除自己");
        } else if (StringUtils.equalsIgnoreCase(username, User.ADMIN)) {
            response.getWriter().write("不能删除超级管理员");
        } else {
            UserAssistant.deleteUser(username);
            response.getWriter().write("success");
        }
    }
}
