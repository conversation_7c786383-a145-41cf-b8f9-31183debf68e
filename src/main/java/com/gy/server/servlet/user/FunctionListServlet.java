package com.gy.server.servlet.user;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.gy.server.assistant.user.User;
import com.gy.server.servlet.user.login.UserLoginServlet;
import com.gy.server.utils.CharsetEncoding;
import com.gy.server.utils.JsonUtil;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR> - [Created on 2018/3/27 19:30]
 */
@WebServlet(name = "FunctionListServlet", urlPatterns = "/function_list")
public class FunctionListServlet extends HttpServlet {


    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        this.doGet(request, response);
    }

    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        request.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);
        response.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);

        List<Object> list = new ArrayList<>();

        list.add(new FunctionGroup("cdKeyManage", "Cdkey管理"));
        list.add(new Function("cdKey", "cdkey列表", "cdKeyManage", "cdkey/cdkey_list.jsp"));


        list.add(new FunctionGroup("userManage", "用户管理"));
        list.add(new Function("user", "用户信息", "userManage", "user/user_list.jsp"));

        String type = request.getParameter("type");
        if (!StringUtils.equals(type, "authorityTree")) {
            User user = ((User) request.getSession().getAttribute(UserLoginServlet.SESSION_IDENTIFY_KEY));
            Set<String> authorities = user.getAuthoritys();

            list.removeIf(next -> next instanceof Function && !authorities.contains(((Function) next).id));
        }

        Set<String> functions = new HashSet<>();
        for (Object o : list) {
            if (o instanceof Function) {
                functions.add(((Function) o).pid);
            }
        }

        list.removeIf(next -> next instanceof FunctionGroup && !functions.contains(((FunctionGroup) next).id));

        response.getWriter().write(JsonUtil.list2Json(list));
    }

    class FunctionGroup {
        private String id;
        private String text;

        FunctionGroup(String id, String text) {
            this.id = id;
            this.text = text;
        }
    }


    class Function {
        private String id;
        private String text;

        private String pid;
        private String pagePath;

        private String iconCls = "icon-node";


        Function(String id, String text, String pid, String pagePath) {
            this.id = id;
            this.text = text;
            this.pid = pid;
            this.pagePath = pagePath;
        }
    }
}
