package com.gy.server.servlet.user;

import java.io.IOException;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.gy.server.servlet.user.login.UserLoginServlet;
import com.gy.server.assistant.user.User;
import com.gy.server.assistant.user.UserAssistant;
import com.gy.server.db.relation.hibernate.HibernateUtil;
import com.gy.server.utils.CharsetEncoding;
import com.gy.server.utils.JsonUtil;
import com.gy.server.utils.cipher.DesCipher;

import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR> - [Created on 2018/5/4 13:17]
 */
@WebServlet(name = "UserUpdateServlet", urlPatterns = "/user_update")
public class UserUpdateServlet extends HttpServlet {

    private static Logger logger = LogManager.getLogger(UserUpdateServlet.class);

    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        this.doGet(request, response);
    }

    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        request.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);
        response.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);

        //检查用户是否登录
        if(!UserAssistant.checkUserAuthority(request, response)){
            return;
        }

        User user = JsonUtil.json2Obj(request.getParameter("data"), User.class);

        //当前操作用户
        String curUserName = ((User) request.getSession().getAttribute(UserLoginServlet.SESSION_IDENTIFY_KEY)).getUsername();

        //超级管理员操作判断
        if(!StringUtils.equalsIgnoreCase(curUserName, User.ADMIN)
                && StringUtils.endsWithIgnoreCase(user.getUsername(), User.ADMIN)) {
            response.getWriter().write("不能修改超级管理员信息");
            return;
        }

        String passwordChange = request.getParameter("passwordChange");
        if (StringUtils.equals(passwordChange, "true")) {
            try {
                user.setPassword(DesCipher.encrypt(UserLoginServlet.SECRET_KEY, user.getPassword()));
            } catch (Exception e) {
                logger.error(e.getMessage());
            }
        }

        if (!StringUtils.equals(user.getAuthority(), "")) {
            user.setAuthority(user.getAuthority().replace(",", "，"));
        }

        HibernateUtil.update(user);

        response.getWriter().write("success");
    }
}
