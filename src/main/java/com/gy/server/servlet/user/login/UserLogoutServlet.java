package com.gy.server.servlet.user.login;

import java.io.IOException;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.gy.server.utils.CharsetEncoding;

/**
 * <AUTHOR> - [Created on 2018/5/14 13:24]
 */
@WebServlet(name = "UserLogoutServlet", urlPatterns = "/user_logout")
public class UserLogoutServlet extends HttpServlet {

    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        this.doGet(request, response);
    }

    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        request.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);
        response.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);

        request.getSession().removeAttribute(UserLoginServlet.SESSION_IDENTIFY_KEY);

        response.getWriter().write("success");
    }
}
