package com.gy.server.servlet.user.login;

import java.io.IOException;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.gy.server.assistant.user.User;
import com.gy.server.assistant.user.UserAssistant;
import com.gy.server.utils.CharsetEncoding;
import com.gy.server.utils.cipher.DesCipher;

import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR> - [Created on 2018/5/4 11:28]
 */
@WebServlet(name = "UserLoginServlet", urlPatterns = "/user_login")
public class UserLoginServlet extends HttpServlet {

    public static final String SECRET_KEY = "NhP9Kk4AFS5QCg17jLQz9qJRhDoiAfFY";

    public static final String SESSION_IDENTIFY_KEY = "user";

    private static Logger logger = LogManager.getLogger(UserLoginServlet.class);

    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        this.doGet(request, response);
    }

    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        request.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);
        response.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);

        String username = request.getParameter("username");
        String password = request.getParameter("password");

        User user = UserAssistant.getUserByName(username);
        if (user == null) {
            response.getWriter().write("notexist");
            return;
        } else {
            try {
                String encrypt = DesCipher.encrypt(SECRET_KEY, password);
                if (StringUtils.equals(encrypt, user.getPassword())) {
                    request.getSession().setAttribute(SESSION_IDENTIFY_KEY, user);
                    response.getWriter().write("success");
                    return;
                }
            } catch (Exception e) {
                logger.error(e.getMessage());
            }
        }
        response.getWriter().write("invalid");
    }

}
