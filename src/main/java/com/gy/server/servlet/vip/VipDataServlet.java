package com.gy.server.servlet.vip;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.servlet.AsyncContext;
import javax.servlet.ServletException;
import javax.servlet.ServletResponse;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.gy.server.core.MessageServerType;
import com.gy.server.core.callback.response.CallbackResponse;
import com.gy.server.core.command.CommandUtil;
import com.gy.server.core.command.ServerCommandRequest;
import com.gy.server.packet.PbItem;
import com.gy.server.utils.JsonUtil;
import com.gy.server.utils.Md5Util;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.rpc.TLMessageCallbackWebTask;
import com.ttlike.server.tl.baselib.util.BaseCommonUtil;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * @作者 FanHaojie
 * @描述 大客户数据查询
 */
@WebServlet(name = "VipDataServlet", urlPatterns = "/vip_role_data", asyncSupported = true)
public class VipDataServlet extends HttpServlet {
    private static Logger logger = LogManager.getLogger(VipDataServlet.class);
    /** 签名私钥 */
    private static final String SIGN_KEY = "B33hIf0fgPh4aAE8Tbkp5FujCXXDE2";
    /** 成功 */
    public static final String RESULT_SUCCESS = "0";
    /** 服务器异常 */
    private static final String RESULT_FAIL = "1";
    /** 签名失败 */
    private static final String RESULT_FAIL_SIGN = "2";
    /** 角色唯一标识非法 */
    private static final String RESULT_FAIL_ROLE_ID = "3";

    @Override
    protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        Map<String, String> results = new HashMap<>();
        try {
            //http://localhost:8080/xhx-billing/vip_role_data?roleId=2000135&sign=32CDD757312AC71F1935976215A1FFFA
            Map<String, String[]> parameterMap = new HashMap<String, String[]>(req.getParameterMap());
            parameterMap.remove("sign");//签名不参与签名计算
            String stringForSign = parameterMap.entrySet().stream().map(e -> e.getKey() + "=" + e.getValue()[0])
                    .sorted(String::compareTo).collect(Collectors.joining("&"));
            logger.info("VipDataServlet, Parameter = {}", stringForSign + "&sign=" + req.getParameter("sign"));
            stringForSign = stringForSign + "&serviceSecret=" + SIGN_KEY;
            String sSign = Md5Util.MD5(stringForSign);//计算签名
            logger.info("VipData MD5 Sign, sign = {}", sSign);
            if (sSign.equalsIgnoreCase(req.getParameter("sign"))) {//校验签名
                long roleId = Long.parseLong(req.getParameter("roleId"));
                if (roleId <= 0) {
                    results.put("code", RESULT_FAIL_ROLE_ID);
                    String rst = JsonUtil.map2Json(results);
                    resp.getOutputStream().write(rst.getBytes(StandardCharsets.UTF_8));
                    return;
                }
                int serverId = BaseCommonUtil.getServerId(roleId);
                ServerCommandRequest commandRequest = CommandUtil.newServerCommandRequest("BillingCommandService.geiVipRoleData", MessageServerType.billing, serverId);
                TLBase.getInstance().getRpcUtil().sendToNodeWithCallBack(new TLMessageCallbackWebTask(req) {

                    @Override
                    public void webComplete(AsyncContext asyncContext, CallbackResponse callbackResponse) {
                        ServletResponse response = asyncContext.getResponse();
                        List<PbItem.Item> allMiniHero = callbackResponse.getParam(0);

                        Map<String, Object> dataResults = new HashMap<>(allMiniHero.size());
                        dataResults.put("heroData", allMiniHero);

                        Map<String, Object> results = new HashMap<>();
                        results.put("code", VipDataServlet.RESULT_SUCCESS);
                        results.put("data", dataResults);
                        String rst = JsonUtil.map2Json(results);
                        try {
                            response.getOutputStream().write(rst.getBytes(StandardCharsets.UTF_8));
                            response.getOutputStream().flush();
                            response.flushBuffer();
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    }

                    @Override
                    public void webTimeout(AsyncContext asyncContext) {
                        ServletResponse response = asyncContext.getResponse();
                        try {
                            response.getWriter().write("Fail");
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    }
                }, ServerType.GAME, serverId, commandRequest,roleId);


                return;
            } else {
                results.put("code", RESULT_FAIL_SIGN);//签名不符
            }
        } catch (Exception e) {
            e.printStackTrace();
            results.put("code", RESULT_FAIL);
        }
        String rst = JsonUtil.map2Json(results);
        resp.getOutputStream().write(rst.getBytes(StandardCharsets.UTF_8));
    }

    @Override
    protected void doPost(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        doGet(req, resp);
    }
}
