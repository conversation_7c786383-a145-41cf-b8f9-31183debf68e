package com.gy.server.servlet.vip;

import java.io.IOException;
import java.nio.charset.Charset;
import java.util.Enumeration;
import java.util.TreeMap;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.gy.server.utils.net.HttpUtil;

/** @作者 FanHaojie
 ** @描述 大客户数据查询分流 */
@WebServlet(name = "VipDataToServlet", urlPatterns = "/vip_role_data_to")
public class VipDataToServlet extends HttpServlet {
    private static Logger logger = LogManager.getLogger(VipDataToServlet.class);

    @Override
    protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        try {
            TreeMap<String, String> map = new TreeMap<>();
            Enumeration<String> en = req.getParameterNames();
            while (en.hasMoreElements()) {
                String str = en.nextElement();
                map.put(str, req.getParameter(str));
            }
            StringBuilder sb = new StringBuilder();
            for (String key : map.keySet()) {
                sb.append(key).append("=").append(map.get(key)).append("&");
            }
            String data = sb.toString().substring(0, sb.toString().length() - 1);
            logger.info("VipDataToServlet, Parameter = {}", data);
            String url = "http://gy.shangua.com:5180/xhx-billing/vip_role_data";//TDF 大客户 - 决定转发地址
            String rst = HttpUtil.requestHttpWithPostReturnString(url, data.getBytes(Charset.forName("utf-8")));
            logger.info("VipData Redirect, url = {}", url);
            resp.getOutputStream().write(rst.getBytes());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    protected void doPost(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        doGet(req, resp);
    }
}
