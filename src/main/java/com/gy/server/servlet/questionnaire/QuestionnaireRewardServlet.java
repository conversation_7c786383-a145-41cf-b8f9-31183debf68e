package com.gy.server.servlet.questionnaire;

import com.google.gson.reflect.TypeToken;
import com.gy.server.assistant.login.LoginCheckType;
import com.gy.server.core.command.ServerCommandRequest;
import com.gy.server.sdk.SDKParam;
import com.gy.server.servlet.login.check.changyou.LoginChangYou;
import com.gy.server.utils.CharsetEncoding;
import com.gy.server.utils.JsonUtil;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.rpc.CommandRequests;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.servlet.ServletException;
import javax.servlet.ServletResponse;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

@WebServlet(name = "QuestionnaireRewardServlet", urlPatterns = "/questionnaire_reward")
public class QuestionnaireRewardServlet extends HttpServlet {
    private static final Logger LOGGER = LogManager.getLogger(QuestionnaireRewardServlet.class);


    /**
     * 返回值
     */
    public static final int CODE_SUCCESS = 0;                                   // 成功
    public static final int CODE_FOUND_EXCEPTION = 1;                           // 发生异常
    public static final int CODE_NOT_FOUND_CONTENT = 2;                         // 空请求体, 导致无法解析出有效的字段
    public static final int CODE_VERIFY_FAILURE = 3;                            // 验签失败
    public static final int CODE_SERVERID_ILLEGAL = 4;                          // 区服id非法
    public static final int CODE_TIMEOUT = 5;                                   // 超时

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        request.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);
        response.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);

        try {
            String rawString = IOUtils.toString(request.getInputStream(), StandardCharsets.UTF_8);
            LOGGER.info("questionnaire reward rawString: " + rawString);

            if (StringUtils.isNotEmpty(rawString)) {
                String jsonString = URLDecoder.decode(rawString, CharsetEncoding.ENCODING_UTF_8);
                LOGGER.info("questionnaire reward jsonString: " + jsonString);

                String appKey = SDKParam.getAccountParam(LoginCheckType.ACCOUNT_TYPE_CHANGYOU, 0);
                String appSecret = SDKParam.getAccountParam(LoginCheckType.ACCOUNT_TYPE_CHANGYOU, 1);

                Map<String, String> dataMap = JsonUtil.json2Collection(jsonString, new TypeToken<Map<String, String>>() {}.getType());

                int templateId = Integer.parseInt(dataMap.get("remark"));
                long playerId = Long.parseLong(dataMap.get("roleId"));
                int serverId = Integer.parseInt(dataMap.get("serverId"));
                String cyId = dataMap.get("cyId");
                String itemType = dataMap.get("itemType");
                String itemId = dataMap.get("itemId");
                String sign = dataMap.get("sign");

                if(itemType == null){
                    itemType = "";
                }
                if(itemId == null){
                    itemId = "";
                }

                String sb = appKey + appSecret + cyId + playerId + serverId + itemType + itemId + templateId;
                String _sign = LoginChangYou.createSign(sb);

                if (_sign.equalsIgnoreCase(sign)) {

                    if (serverId > 0) {
                        ServerCommandRequest r1 = CommandRequests.newServerCommandRequest("GameMasterCommandService.questionnaireReward");
                        TLBase.getInstance().getRpcUtil().sendToNode(ServerType.GAME, serverId, r1, playerId, 0L, templateId);
                    } else {
                        LOGGER.error("questionnaire reward serverId illegal.");
                        writeTo(response, CODE_SERVERID_ILLEGAL, "serverId illegal");
                    }
                } else {
                    LOGGER.error("questionnaire reward verify failure. " + sign + " != " + _sign + "  " + sb);
                    writeTo(response, CODE_VERIFY_FAILURE, "verify failure");
                }
            } else {
                LOGGER.error("questionnaire reward not found content.");
                writeTo(response, CODE_NOT_FOUND_CONTENT, "not found content");

            }
        } catch (Exception e) {
            LOGGER.catching(e);
            writeTo(response, CODE_FOUND_EXCEPTION, "found exception");
        }
    }

    public static void writeTo(ServletResponse response, int code, String msg) throws IOException {
        Map<String, Object> result = new HashMap<>();
        result.put("code", code);
        result.put("message", msg);
        String jsonStr = JsonUtil.map2Json(result);
        response.getWriter().write(jsonStr);

        // 日志
        LOGGER.info(jsonStr);
    }
}
