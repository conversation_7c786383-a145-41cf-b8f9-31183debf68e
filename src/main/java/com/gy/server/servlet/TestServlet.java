package com.gy.server.servlet;

import java.io.IOException;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR> - [Created on 2018/2/27 21:20]
 */
@WebServlet(name = "TestServlet", urlPatterns = "/test")
public class TestServlet extends HttpServlet {


    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

    }

    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        System.out.println(request.getHeader("x-forwarded-for"));
        System.out.println(request.getHeader("Proxy-Client-IP"));
        System.out.println(request.getHeader("WL-Proxy-Client-IP"));
        System.out.println(request.getHeader("X-Real-IP"));
        System.out.println(request.getRemoteAddr());
    }
}
