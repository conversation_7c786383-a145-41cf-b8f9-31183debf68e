package com.gy.server.servlet.version;

import com.google.gson.reflect.TypeToken;
import com.gy.server.assistant.version.Version;
import com.gy.server.assistant.version.VersionAssistant;
import com.gy.server.assistant.version.VersionManager;
import com.gy.server.utils.CharsetEncoding;
import com.gy.server.utils.CollectionUtil;
import com.gy.server.utils.JsonUtil;
import com.ttlike.server.tl.baselib.CommonsConfiguration;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR> - [Created on 2018/6/21 20:30]
 */
@WebServlet(name = "VersionUpdateServlet", urlPatterns = "/version_update")
public class VersionUpdateServlet extends HttpServlet {

    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        this.doGet(request, response);
    }

    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        request.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);
        response.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);
        String result = "success";


        if (!VersionAssistant.checkAuthValidity(request.getParameter("auth"))
                && CommonsConfiguration.runMode.isLive()) {
            ////客户端会用到该接口自动提交更新记录，仅限于内网
            result = "auth fail";
        } else {
            String data = request.getParameter("data");
            List<Version> list = JsonUtil.json2Collection(data, new TypeToken<List<Version>>() {
            }.getType());

            if (CollectionUtil.isNotEmpty(list)) {
                for (Version version : list) {
                    if (!VersionAssistant.isValidVersionText(version.getVersion())) {
                        result = "version is invalid";
                    } else if (VersionManager.getAllVersions().stream().anyMatch(v -> v.getId() != version.getId() && v.isSame(version))) {
                        result = "already exist";
                    } else {
                        //检测一下 填入的初始版本号是否合法
                        boolean initVersionValid = true;
                        if (StringUtils.isNotEmpty(version.getInitVersionInclude())) {
                            String[] arrays = version.getInitVersionInclude().split(",");
                            for (String initVersion : arrays) {
                                if (!VersionAssistant.isValidVersionText(initVersion)) {
                                    initVersionValid = false;
                                    break;
                                }
                            }
                        }
                        if (initVersionValid) {
                            if (StringUtils.isNotEmpty(version.getInitVersionExclude())) {
                                String[] arrays = version.getInitVersionExclude().split(",");
                                for (String initVersion : arrays) {
                                    if (!VersionAssistant.isValidVersionText(initVersion)) {
                                        initVersionValid = false;
                                        break;
                                    }
                                }
                            }
                        }

                        if (!initVersionValid) {
                            result = "init version is invalid";
                        }

                        if (version.getType() == 1) {
                            //不能有比它低的预下载版本
                            if (VersionManager.hasLowerPreDownloadVersion(version.getVersion(), version.getPlatform())) {
                                initVersionValid = false;
                                result = "不能存在比当前版本低的预下载版本";
                            }
                        }

                        if (initVersionValid) {
                            version.init();
                            if (VersionManager.getVersionById(version.getId()) != null) {
                                version.update();
                            } else {
                                version.save();
                            }

                            VersionManager.updateFromDb();
                        }
                    }
                }
            }
        }
        response.getWriter().write(result);
    }

}
