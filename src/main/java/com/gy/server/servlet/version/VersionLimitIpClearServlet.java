package com.gy.server.servlet.version;

import java.io.IOException;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.gy.server.utils.CharsetEncoding;
import com.gy.server.utils.StringUtil;
import com.gy.server.assistant.version.Version;
import com.gy.server.assistant.version.VersionAssistant;
import com.gy.server.assistant.version.VersionManager;

/**
 * <AUTHOR> - [Created on 2019/2/16 9:50]
 */
@WebServlet(name = "VersionLimitIpClearServlet", urlPatterns = "/version_limit_ip_clear")
public class VersionLimitIpClearServlet extends HttpServlet {

    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        this.doGet(request, response);
    }

    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        request.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);
        response.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);

        String result = "success";

        if (!VersionAssistant.checkAuthValidity(request.getParameter("auth"))) {
            result = "auth fail";
        } else {
            int[] versionIds = StringUtil.splitToIntArray(request.getParameter("versionIds"), ",");
            for (int versionId : versionIds) {
                Version version = VersionManager.getVersionById(versionId);
                if (version != null) {
                    version.getLimitIpSet().clear();
                    version.update();
                }
            }

            VersionManager.updateFromDb();
        }

        response.getWriter().write(result);
    }
}