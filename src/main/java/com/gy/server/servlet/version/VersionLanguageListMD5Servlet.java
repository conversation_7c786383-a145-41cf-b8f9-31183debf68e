package com.gy.server.servlet.version;

import com.gy.server.assistant.languagePack.LanguagePack;
import com.gy.server.assistant.languagePack.LanguagePackManager;
import com.gy.server.assistant.version.Version;
import com.gy.server.assistant.version.VersionManager;
import com.gy.server.servlet.Md5RequestHttpServlet;
import com.gy.server.utils.CharsetEncoding;
import com.gy.server.utils.JsonUtil;
import com.gy.server.utils.net.ServletUtil;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> - [Created on 2018/6/21 19:39]
 */
@WebServlet(name = "VersionLanguageListMD5Servlet", urlPatterns = "/version_language_list_md5")
public class VersionLanguageListMD5Servlet extends Md5RequestHttpServlet {

    @Override
    public Map<Object, Object> doPost0(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        request.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);
        response.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);

        String ip = ServletUtil.getIpByHttpServletRequest(request);
        String platform = request.getParameter("platform");

        List<Version.JsonVersion> versions = new ArrayList<>();
        List<LanguagePack.JsonLanguagePack> languagePacks = new ArrayList<>();

        if (StringUtils.isNotEmpty(platform)) {
            versions.addAll(VersionManager.getTopJsonVersions(ip,platform));

            //不再使用
            //languagePacks.addAll(LanguagePackManager.getJsonLanguagePackByClientVersion(language, languageVersion, ip, platform));
        }


        Map<Object, Object> result = new HashMap<>();
        result.put("version", versions);
//        result.put("language", languagePacks);

        return result;
    }

}
