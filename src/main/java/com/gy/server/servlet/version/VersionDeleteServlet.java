package com.gy.server.servlet.version;

import java.io.IOException;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.gy.server.utils.CharsetEncoding;
import com.gy.server.utils.StringUtil;
import com.gy.server.assistant.version.VersionAssistant;
import com.gy.server.assistant.version.VersionManager;

/**
 * <AUTHOR> - [Created on 2018/6/21 19:39]
 */
@WebServlet(name = "VersionDeleteServlet", urlPatterns = "/version_delete")
public class VersionDeleteServlet extends HttpServlet {


    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        this.doGet(request, response);
    }

    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        request.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);
        response.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);

        if (!VersionAssistant.checkAuthValidity(request.getParameter("auth"))) {
            response.getWriter().write("auth fail");
            return;
        }

        int[] ids = StringUtil.splitToIntArray(request.getParameter("ids"), ",");

        for (int id : ids) {
            VersionManager.removeVersion(id);
        }

        response.getWriter().write("success");
    }
}
