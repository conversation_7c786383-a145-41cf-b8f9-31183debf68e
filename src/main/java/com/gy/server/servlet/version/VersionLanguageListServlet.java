package com.gy.server.servlet.version;

import com.gy.server.assistant.languagePack.LanguagePack;
import com.gy.server.assistant.languagePack.LanguagePackManager;
import com.gy.server.assistant.version.Version;
import com.gy.server.assistant.version.VersionManager;
import com.gy.server.utils.CharsetEncoding;
import com.gy.server.utils.JsonUtil;
import com.gy.server.utils.net.ServletUtil;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> - [Created on 2018/6/21 19:39]
 */
@WebServlet(name = "VersionLanguageListServlet", urlPatterns = "/version_language_list")
public class VersionLanguageListServlet extends HttpServlet {


    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        this.doGet(request, response);
    }

    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        request.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);
        response.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);

        String ip = ServletUtil.getIpByHttpServletRequest(request);
        String clientVersion = request.getParameter("clientVersion");
        int language = Integer.parseInt(request.getParameter("language"));
        String languageVersion = request.getParameter("languageVersion");
        String platform = request.getParameter("platform");
        String clientInitVersion = request.getParameter("clientInitVersion");

        List<Version.JsonVersion> versions = new ArrayList<>();
        List<LanguagePack.JsonLanguagePack> languagePacks = new ArrayList<>();

        if (StringUtils.isNotEmpty(clientVersion)
                && StringUtils.isNotEmpty(languageVersion)
                && StringUtils.isNotEmpty(platform)) {

            if (StringUtils.isNotEmpty(clientInitVersion)) {
                versions.addAll(VersionManager.getJsonVersionsByClientVersionAndInitVersion(clientVersion, clientInitVersion, ip, platform));
            } else {
                versions.addAll(VersionManager.getJsonVersionsByClientVersion(clientVersion, ip, platform));
            }
            languagePacks.addAll(LanguagePackManager.getJsonLanguagePackByClientVersion(language, languageVersion, ip, platform));
        }


        Map<String, Object> result = new HashMap<>();
        result.put("version", versions);
        result.put("language", languagePacks);

        response.getWriter().print(JsonUtil.map2Json(result));
    }
}
