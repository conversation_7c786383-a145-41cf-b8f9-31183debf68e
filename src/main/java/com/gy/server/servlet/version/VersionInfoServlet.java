package com.gy.server.servlet.version;

import java.io.IOException;
import java.util.Comparator;
import java.util.List;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.gy.server.utils.CharsetEncoding;
import com.gy.server.utils.JsonUtil;
import com.gy.server.assistant.version.Version;
import com.gy.server.assistant.version.VersionManager;

/**
 * <AUTHOR> - [Created on 2018/6/21 17:51]
 */
@WebServlet(name = "VersionInfoServlet", urlPatterns = "/version_info")
public class VersionInfoServlet extends HttpServlet {

    private static Comparator<Version> comparator = (o1, o2) -> {
        int result = o1.getPlatform().compareTo(o2.getPlatform());
        if (result == 0) {
            result = Version.versionCompare(o1.getVersion(), o2.getVersion());
            if (result == 0) {
                result = o1.getId() - o2.getId();
            }
        }
        return result;
    };

    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        this.doPost(request, response);
    }

    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        request.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);
        response.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);

        List<Version> versions = VersionManager.getAllVersions();
        versions.sort(comparator);

        Gson g = new GsonBuilder().setDateFormat("yyyy-MM-dd HH:mm:ss").create();
        response.getWriter().print(JsonUtil.list2Json(versions));
    }
}
