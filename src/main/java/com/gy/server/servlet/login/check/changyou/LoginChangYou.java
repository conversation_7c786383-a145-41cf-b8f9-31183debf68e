package com.gy.server.servlet.login.check.changyou;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

import javax.servlet.http.HttpServletRequest;

import com.google.gson.JsonObject;
import com.gy.server.Configuration;
import com.gy.server.assistant.login.LoginCheckType;
import com.gy.server.servlet.login.check.LoginCheck;
import com.gy.server.sdk.SDKParam;
import com.gy.server.util.ServerConstants;
import com.gy.server.utils.JsonUtil;
import com.gy.server.utils.net.HttpUtil;

import org.apache.commons.lang3.tuple.Pair;

/**
 * 畅游登录验证
 *
 * <AUTHOR>
 * @Date Created in 14:41 2019-11-28
 */
public class LoginChangYou extends LoginCheck {

    private static String test_url = "http://tmobilebilling.changyou.com/billing";
    private static String live_url = "http://mobilebilling.changyou.com/billing";

    public static final Random random = new Random();
    private String channelId;
    private String data;

    private String tag = String.valueOf(random.nextInt(10000000));


    public LoginChangYou(List<String> params, String version) {
        super(params, version);
        channelId = params.get(0);
        data = params.get(1);
    }

    public static final String getUrl(){
        return Configuration.isTest() ? test_url : live_url;
    }

    @Override
    protected String getUrl(HttpServletRequest request) {
        return getUrl();
    }

    @Override
    protected boolean isPostRequest() {
        return true;
    }

    @Override
    public void check(HttpServletRequest request) {
        //生成签名
        String url = getUrl(request);
        try {
            String appKey = SDKParam.getAccountParam(LoginCheckType.ACCOUNT_TYPE_CHANGYOU, 0);
            String appSecret = SDKParam.getAccountParam(LoginCheckType.ACCOUNT_TYPE_CHANGYOU, 1);
            List<Pair<String, String>> requestPropertiesParam = new ArrayList<>();
            requestPropertiesParam.add(Pair.of("appkey", appKey));

            //opcode+data+appkey +appsecret+tag+channelId
            String sb = "10001" + data + appKey +
                    appSecret + tag + this.channelId;
            requestPropertiesParam.add(Pair.of("sign", createSign(sb)));
            requestPropertiesParam.add(Pair.of("tag", tag));
            requestPropertiesParam.add(Pair.of("opcode", "10001"));
            requestPropertiesParam.add(Pair.of("channelId", this.channelId));
            String resultText = HttpUtil.requestHttpWithPostReturnString(url, getPostData().getBytes(), requestPropertiesParam);
            this.unPack(resultText);
        } catch (Exception e) {
            logger.catching(e);
        }
    }

    @Override
    protected String getPostData() {
        return "data=" + data;
    }

    @Override
    protected void unPack(String text) {
        JsonObject json = JsonUtil.getJsonObject(text);
        int state = json.get("state").getAsInt();
        if (state == 200) {
            JsonObject data = json.get("data").getAsJsonObject();
            int status = data.get("status").getAsInt();
            if (status == 1) {
                //success
                accountId = data.get("userid").getAsString();
                addResultParams(accountId);
                addResultParams(data.get("oid").getAsString());
                addResultParams(data.has("access_token") ? data.get("access_token").getAsString() : "");
                addResultParams(data.has("extension") ? data.get("extension").getAsString() : "");
                //获取info的年龄
                String age = "18";
                if(data.has("info")){
                    JsonObject info = JsonUtil.getJsonObject(data.get("info").getAsString());
                    if(info.has("age")){
                        age = info.get("age").getAsString();
                    }
                }
                addResultParams(age);
                addResultParams(ServerConstants.getCurrentTimeMillis() + "");
                this.result = LoginCheck.RESULT_SUCCESS;
                logger.info("changYou login Info : " + data);
            } else {
                //未知错误代码
                this.result = LoginCheck.RESULT_OVERDUE;
                logger.info("changYou login error message : " + text);
            }
        } else {
            //畅游服务器返回失败
            this.result = LoginCheck.RESULT_OVERDUE;
            logger.info("changYou login error message : " + text);
        }
    }

    /**
     * 畅游签名
     */
    public static String createSign(String param) {
        StringBuilder result = new StringBuilder();
        MessageDigest md5;
        try {
            md5 = MessageDigest.getInstance("MD5");
            md5.update(param.getBytes(StandardCharsets.UTF_8));
            byte[] b = md5.digest();
            for (byte value : b) {
                int x = value & 0xFF;
                int h = x >>> 4;
                int l = x & 0x0F;
                result.append((char) (h + ((h < 10) ? '0' : 'a' - 10)));
                result.append((char) (l + ((l < 10) ? '0' : 'a' - 10)));
            }
            return result.toString().substring(8, 24);
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }
}
