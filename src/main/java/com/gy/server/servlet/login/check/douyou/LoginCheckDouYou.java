package com.gy.server.servlet.login.check.douyou;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.NoSuchAlgorithmException;
import java.util.*;

import javax.servlet.http.HttpServletRequest;

import com.google.gson.reflect.TypeToken;
import com.gy.server.assistant.login.LoginCheckType;
import com.gy.server.servlet.login.check.LoginCheck;
import com.gy.server.sdk.SDKParam;
import com.gy.server.utils.JsonUtil;
import com.gy.server.utils.Md5Util;
import com.gy.server.utils.net.HttpUtil;

import org.apache.commons.lang3.tuple.Pair;

/**
 * <AUTHOR> - [Created on 2019/9/23 14:55]
 */
public class LoginCheckDouYou extends LoginCheck {

    private String session_token;
    private long timestamp;
    private String from;

    public LoginCheckDouYou(List<String> params, String version) {
        super(params, version);
        accountId = loginCheckParams.get(0);
        session_token = loginCheckParams.get(1);
        if (loginCheckParams.size() > 2 && loginCheckType == LoginCheckType.ACCOUNT_TYPE_DOUYOU_AND) {
            from = loginCheckParams.get(2);
        }
        if (loginCheckType == LoginCheckType.ACCOUNT_TYPE_DOUYOU_IOS2) {
            timestamp = (long) (System.currentTimeMillis() / 1000.0);
        }else{
            timestamp = System.currentTimeMillis();
        }
    }

    @Override
    public void check(HttpServletRequest request) {
        //生成签名
        String url = getUrl(request);
        try {
            List<Pair<String, String>> requestPropertiesParam = new ArrayList<>();
            requestPropertiesParam.add(Pair.of("Content-Type", "application/x-www-form-urlencodeed"));
            String resultText = HttpUtil.requestHttpWithPostReturnString(url, JsonUtil.map2Json(getPostDataMap()).getBytes(StandardCharsets.UTF_8), requestPropertiesParam);
            this.unPack(resultText);
        } catch (Exception e) {
            logger.catching(e);
        }
    }

    @Override
    protected String getUrl(HttpServletRequest request) {
        return "http://open.douyouzhiyu.com/v2/users/check_token";
    }

    @Override
    protected boolean isPostRequest() {
        return true;
    }

    @Override
    protected String getPostData() {
        return "";
    }

    protected Map<String, String> getPostDataMap() throws NoSuchAlgorithmException {
        Map<String, String> signDataMap = getSignDataMap();
        signDataMap.put("sign", Md5Util.MD5(getSignData()).toLowerCase());
        if (loginCheckType == LoginCheckType.ACCOUNT_TYPE_DOUYOU_AND) {
            signDataMap.put("from", from);
        }
        return signDataMap;
    }

    protected String getSignData() {
        //对消息体中查询字符串按字典序排序并且进行URLCode编码
        String baseStr = format(getSignDataMap());
        return baseStr + "|" + SDKParam.getAccountParam(this.loginCheckType, 1);
    }

    private Map<String, String> getSignDataMap() {
        String client_id = SDKParam.getAccountParam(this.loginCheckType, 0);

        Map<String, String> params = new HashMap<>();
        params.put("client_id", client_id);
        params.put("open_id", this.accountId);
        params.put("session_token", session_token);
        params.put("timestamp", String.valueOf(timestamp));

        return params;
    }

    public static String format(Map<String, String> params) {
        StringBuilder base = new StringBuilder();
        Map<String, String> tempMap = new TreeMap<>(params);
        //获取计算nsp_key的基础串
        try {
            for (Map.Entry<String, String> entry : tempMap.entrySet()) {
                String k = entry.getKey();
                String v = entry.getValue();
                base.append(k).append("=").append(URLEncoder.encode(v, "UTF-8")).append("&");
            }
        } catch (UnsupportedEncodingException e) {
            logger.catching(e);
        }
        String body = base.toString().substring(0, base.toString().length() - 1);
        // 空格和星号转义
        body = body.replaceAll("\\+", "%20").replaceAll("\\*", "%2A");
        return body;
    }

    @Override
    protected void unPack(String text) {
        //检测状态
        Map<String, String> result = JsonUtil.json2Collection(text, new TypeToken<Map<String, String>>() {
        }.getType());

        String status = result.get("status");
        if (status.equals("200")) {
            this.result = LoginCheck.RESULT_SUCCESS;
        } else {
            this.result = LoginCheck.RESULT_OVERDUE;
        }
    }

}
