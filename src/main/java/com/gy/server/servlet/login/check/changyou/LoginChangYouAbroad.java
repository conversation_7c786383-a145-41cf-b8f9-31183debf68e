package com.gy.server.servlet.login.check.changyou;

import com.google.gson.JsonObject;
import com.gy.server.Configuration;
import com.gy.server.assistant.login.LoginCheckType;
import com.gy.server.sdk.SDKParam;
import com.gy.server.servlet.login.check.LoginCheck;
import com.gy.server.utils.JsonUtil;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * <AUTHOR> -[Create on 2023/2/15]
 */
public class LoginChangYouAbroad extends LoginCheck {

    private static final Random random = new Random();
    private String channelId;
    private String data;
    private String deviceId;

    private String tag = String.valueOf(random.nextInt(********));

    public LoginChangYouAbroad(List<String> params, String version) {
        super(params, version);
        channelId = params.get(0);
        data = params.get(1);
        deviceId = params.get(2);
    }

    @Override
    protected String getUrl(HttpServletRequest request) {
        //线上测试
        String test_url = "https://tnsdk.gaming.com/account-api/";
        //线上正式
        String live_url = "https://nsdk.gaming.com/account-api/";

        String api = "cyou/orangelo/user/cysidLoginVerifyV3.json";

        return (Configuration.isTest() ? test_url : live_url) + api;
    }

    @Override
    protected boolean isPostRequest() {
        return true;
    }

    @Override
    public void check(HttpServletRequest request) {
        //生成签名
        String url = getUrl(request);
        try {
            String appKey = SDKParam.getAccountParam(LoginCheckType.ACCOUNT_TYPE_CHANGYOU_ABROAD, 0);
            String appSecrest = SDKParam.getAccountParam(LoginCheckType.ACCOUNT_TYPE_CHANGYOU_ABROAD, 1);


            JsonObject json = JsonUtil.getJsonObject(data);
            String cyid = json.get("cyid").getAsString();
            String token = json.get("cysid").getAsString();
            //请求body
            JsonObject body = new JsonObject();
            body.addProperty("device_id", deviceId);
            body.addProperty("token", token);
            body.addProperty("cyid", cyid);


            //请求头
            List<Pair<String, String>> requestPropertiesParam = new ArrayList<>();
            requestPropertiesParam.add(Pair.of("app_key", appKey));
            requestPropertiesParam.add(Pair.of("channel_id", this.channelId));
            requestPropertiesParam.add(Pair.of("tag", tag));

            //协议签名：appKey + appSecret + data
            String sb = appKey + appSecrest + body;
            requestPropertiesParam.add(Pair.of("sign", LoginChangYou.createSign(sb)));

            String resultText = httpPost(url, body, requestPropertiesParam);
            logger.info("=========================================>"+resultText);

            this.unPack(resultText);
        } catch (Exception e) {
            logger.catching(e);
        }
    }

    @Override
    protected String getPostData() {
        return "data=" + data;
    }

    @Override
    protected void unPack(String text) {
        JsonObject json = JsonUtil.getJsonObject(text);
        String result = json.get("result").getAsString();
        //请求成功
        int state = 400;
        if (result.equals("true")){
            state = 200;
        }
        int errorCode = json.get("errorCode").getAsInt();

        System.out.println("=========================================>"+result + ":" + errorCode );
        if (state == 200) {
            //登录成功
            if (errorCode == 0) {
                //success
                accountId = json.get("uid").getAsString();
                addResultParams(accountId);
                addResultParams(json.get("oid").getAsString());
                addResultParams(json.has("token") ? json.get("token").getAsString() : "");
                JsonObject data = json.get("info").getAsJsonObject();
                addResultParams(data.has("email") ? data.get("email").getAsString() : "");
                addResultParams(data.has("facebook") ? data.get("facebook").getAsString() :"");
                addResultParams(data.has("facebookId") ? data.get("facebookId").getAsString() :"");
                addResultParams(data.has("google")? data.get("google").getAsString() : "");
                addResultParams(data.has("googleId") ? data.get("googleId").getAsString() : "");
                this.result = LoginCheck.RESULT_SUCCESS;
            } else if (errorCode == 900) {
                //这么写清楚点
                this.result = LoginCheck.RESULT_OVERDUE;
                logger.info("changYouAbroad login error message : " + text + ",errorCode:" + errorCode);
            }else {
                //未知错误代码
                this.result = LoginCheck.RESULT_OVERDUE;
                logger.info("changYouAbroad login error message : " + text + ",errorCode:" + errorCode);
            }
        } else {
            //畅游服务器返回失败
            this.result = LoginCheck.RESULT_OVERDUE;
            logger.info("changYouAbroad login error message : " + text + ",errorCode:" + errorCode);
        }
    }

    public static String httpPost(String url,JsonObject body,List<Pair<String, String>> header){
        //返回body
        String result = null;

        //获取连接客户端工具
        CloseableHttpClient httpClient = HttpClients.createDefault();
        CloseableHttpResponse httpResponse = null;

        //创建一个httpPost请求
        HttpPost httpPost = new HttpPost(url);

        //设置header信息
        httpPost.setHeader("Accept","*/*");
        httpPost.setHeader("Accept-Encoding","gzip,deflate");
        httpPost.setHeader("Cache-Control","no-cache");
        httpPost.setHeader("Connection","keep-alive");
        httpPost.setHeader("Content-Type","application/json;charset=UTF-8");

        if (header.size() > 0){
            for (Pair<String, String> pair : header) {
                httpPost.setHeader(pair.getKey(),pair.getValue());
            }
        }

        if (body != null){
            try {
                StringEntity stringEntity = new StringEntity(body.toString(), "UTF-8");
                stringEntity.setContentEncoding("UTF-8");
                stringEntity.setContentType("application/json");
                httpPost.setEntity(stringEntity);

                //执行post请求
                httpResponse = httpClient.execute(httpPost);

                //获取结果实体
                HttpEntity entity = httpResponse.getEntity();
                if (entity != null){
                    result = EntityUtils.toString(entity,"UTF-8");
                }

                try {
                    httpResponse.close();
                    httpClient.close();
                }catch (IOException e){
                    logger.error(e);
                }
            }catch (Exception e){
                logger.error(e);
            }
        }
        return result;
    }
}
