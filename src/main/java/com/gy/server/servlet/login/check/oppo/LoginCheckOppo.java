package com.gy.server.servlet.login.check.oppo;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.List;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.tuple.Pair;

import com.google.gson.JsonObject;
import com.gy.server.assistant.login.LoginCheckType;
import com.gy.server.servlet.login.check.LoginCheck;
import com.gy.server.sdk.SDKParam;
import com.gy.server.utils.JsonUtil;
import com.gy.server.utils.net.HttpUtil;

/**
 * oppo登录验证
 * <AUTHOR>
 * @date  2019年9月16日 下午3:18:40
 */
public class LoginCheckOppo extends LoginCheck{
	
	//登录时间戳
	private long timestamp;
	
	private String token;
	
	public LoginCheckOppo(List<String> params, String version) {
		super(params, version);
		try {
			this.token = URLEncoder.encode(loginCheckParams.get(0),"UTF-8").replace("+", "%2B");
		} catch (UnsupportedEncodingException e) {
			this.token = "";
		}
		this.accountId = loginCheckParams.get(1);
		this.timestamp = System.currentTimeMillis();
	}
	
	@Override
	protected String getUrl(HttpServletRequest request) {
		String url = "https://iopen.game.oppomobile.com/sdkopen/user/fileIdInfo?fileId="+accountId+"&token="+token;
		return url;
	}
	
	@Override
	protected boolean isPostRequest() {
		return false;
	}

	@Override
	protected String getPostData() {
		return null;
	}
	
	@Override
	public void check(HttpServletRequest request) {
		try {
			String url = getUrl(request);
			List<Pair<String, String>> requestPropertiesParam = new ArrayList<>();
			//随机数
			String nonce = Math.random() * ((9999999999L-1000000000L)+1000000000L) + "";
			//基串
			String baseStr = generateBaseString(timestamp + "", nonce);
			//签名
			String sign = generateSign(baseStr);
			requestPropertiesParam.add(Pair.of("param", baseStr));
			requestPropertiesParam.add(Pair.of("oauthSignature", sign));
			String resultText = HttpUtil.requestHttpWithGetReturnString(url, requestPropertiesParam);
			
			this.unPack(resultText);
			
		} catch (IOException e) {
			logger.catching(e);
		}
		
		
	}

	@Override
	protected void unPack(String text) {
		JsonObject json = JsonUtil.getJsonObject(text);
		if(json.get("resultCode").getAsString().equals("200") && json.get("ssoid").getAsString().equals(accountId)){
			this.result = LoginCheck.RESULT_SUCCESS;
		}else{
			this.result = LoginCheck.RESULT_OVERDUE;
		}
		
	}
	
	public static final String OAUTH_CONSUMER_KEY = "oauthConsumerKey";
	public static final String OAUTH_TOKEN = "oauthToken";
	public static final String OAUTH_SIGNATURE_METHOD = "oauthSignatureMethod";
	public static final String OAUTH_SIGNATURE = "oauthSignature";
	public static final String OAUTH_TIMESTAMP = "oauthTimestamp";
	public static final String OAUTH_NONCE = "oauthNonce";
	public static final String OAUTH_VERSION = "oauthVersion";
	public static final String CONST_SIGNATURE_METHOD = "HMAC-SHA1";
	public static final String CONST_OAUTH_VERSION = "1.0";
	
	private String generateBaseString(String timestamp,String nonce){
		StringBuilder sb = new StringBuilder();
		try {
			sb.append(OAUTH_CONSUMER_KEY).append("=").
			append(URLEncoder.encode(SDKParam.getAccountParam(LoginCheckType.ACCOUNT_TYPE_OPPO, 0),"UTF-8")).
			append("&").
			append(OAUTH_TOKEN).
			append("=").
			append(token).
			append("&").
			append(OAUTH_SIGNATURE_METHOD).
			append("=").
			append(URLEncoder.encode(CONST_SIGNATURE_METHOD,"UTF-8")).
			append("&").
			append(OAUTH_TIMESTAMP).
			append("=").
			append(URLEncoder.encode(timestamp,"UTF-8")).
			append("&").
			append(OAUTH_NONCE).
			append("=").
			append(URLEncoder.encode(nonce,"UTF-8")).
			append("&").
			append(OAUTH_VERSION).
			append("=").
			append(URLEncoder.encode(CONST_OAUTH_VERSION,"UTF-8")).
			append("&");
		} catch (UnsupportedEncodingException e1) {
			logger.catching(e1);
		}
		return sb.toString();
	}
	
	private String generateSign(String baseStr) throws UnsupportedEncodingException{
		byte[] byteHMAC = null;
		try {
			Mac mac = Mac.getInstance("HmacSHA1");
			SecretKeySpec spec = null;
			String oauthSignatureKey = SDKParam.getAccountParam(LoginCheckType.ACCOUNT_TYPE_OPPO, 1) + "&";
			spec = new SecretKeySpec(oauthSignatureKey.getBytes(),"HmacSHA1");
			mac.init(spec);
			byteHMAC = mac.doFinal(baseStr.getBytes());
		} catch (InvalidKeyException e) {
			logger.catching(e);
		} catch (NoSuchAlgorithmException e) {
			logger.catching(e);
		}
		return URLEncoder.encode(String.valueOf(base64Encode(byteHMAC)) ,"UTF-8");
	}
	
	private char[] base64Encode(byte[] data) {
		final char[] alphabet = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".toCharArray();
		char[] out = new char[((data.length + 2) / 3) * 4];
		for (int i = 0, index = 0; i < data.length; i += 3, index += 4) {
			boolean quad = false;
			boolean trip = false;
			int val = (0xFF & (int) data[i]);
			val <<= 8;
			if ((i + 1) < data.length) {
				val |= (0xFF & (int) data[i + 1]);
				trip = true;
			}
			val <<= 8;
			if ((i + 2) < data.length) {
				val |= (0xFF & (int) data[i + 2]);
				quad = true;
			}
			out[index + 3] = alphabet[(quad ? (val & 0x3F) : 64)];
			val >>= 6;
			out[index + 2] = alphabet[(trip ? (val & 0x3F) : 64)];
			val >>= 6;
			out[index + 1] = alphabet[val & 0x3F];
			val >>= 6;
			out[index + 0] = alphabet[val & 0x3F];
		}
		return out;
	}
	

}
