package com.gy.server.servlet.login.check.yijie;

import java.util.List;

import javax.servlet.http.HttpServletRequest;

import com.gy.server.servlet.login.check.LoginCheck;


/**
 * 易接登录校验 - 初始版本
 *
 * <AUTHOR> - [Created on 2018/3/28 21:57]
 */
public class LoginCheckYiJie extends LoginCheck {

    private String sessionId;
    private String sdk;
    private String app;

    public LoginCheckYiJie(List<String> params, String version) {
        super(params, version);
        this.sdk = loginCheckParams.get(0);
        this.app = loginCheckParams.get(1);
        this.accountId = loginCheckParams.get(2);
        this.sessionId = loginCheckParams.get(3);
    }

    @Override
    protected String getUrl(HttpServletRequest request) {
        return String.format("http://sync.1sdk.cn/login/check.html?sdk=%s&app=%s&uin=%s&sess=%s",
                sdk, app, accountId, sessionId);
    }

    @Override
    protected boolean isPostRequest() {
        return false;
    }

    @Override
    protected String getPostData() {
        return "";
    }

    @Override
    protected void unPack(String text) {
        if (text.equals("0")) {
            //success
            this.result = LoginCheck.RESULT_SUCCESS;
        } else {
            //其他情况
            this.result = LoginCheck.RESULT_BUSY;
        }
    }
}
