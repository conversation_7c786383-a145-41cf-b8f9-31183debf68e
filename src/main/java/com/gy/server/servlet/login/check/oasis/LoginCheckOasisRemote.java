package com.gy.server.servlet.login.check.oasis;

import java.security.NoSuchAlgorithmException;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import com.google.gson.reflect.TypeToken;
import com.gy.server.assistant.login.LoginCheckType;
import com.gy.server.servlet.login.check.LoginCheck;
import com.gy.server.sdk.SDKParam;
import com.gy.server.utils.JsonUtil;
import com.gy.server.utils.Md5Util;

/**
 * 绿洲账号登录校验（远端请求验证） - 初始版本4.9.8
 *
 * <AUTHOR> - [Created on 2018/9/11 17:39]
 */
public class LoginCheckOasisRemote extends LoginCheck {

    private String token;

    public LoginCheckOasisRemote(List<String> params, String version) {
        super(params, version);
        this.token = loginCheckParams.get(0);
    }

    @Override
    protected String getUrl(HttpServletRequest request) {
        String gameCode = SDKParam.getAccountParam(LoginCheckType.ACCOUNT_TYPE_OASIS, 0);
        String sign = createSign();
        return String.format("https://api-mobile.oasgames.com/?a=loginUser&token=%s&game_code=%s&sign=%s", token, gameCode, sign);
    }

    @Override
    protected boolean isPostRequest() {
        return false;
    }

    @Override
    protected String getPostData() {
        return "";
    }

    @Override
    protected void unPack(String text) {
        Map<String, String> result = JsonUtil.json2Collection(text, new TypeToken<Map<String, String>>() {
        }.getType());

        String status = result.get("status");
        if (status.equals("ok")) {
            //success
            accountId = result.get("uid");
            this.result = LoginCheck.RESULT_SUCCESS;
        } else {
            String errorCode = result.get("error");
            switch (errorCode) {
                case "1": {
                    //登录Token无效，重试
                    this.result = LoginCheck.RESULT_OVERDUE;
                    break;
                }
                case "-1": {
                    //sign不正确
                    this.result = LoginCheck.RESULT_BUSY;
                    break;
                }
                default: {
                    //其他情况
                    this.result = LoginCheck.RESULT_BUSY;
                    break;
                }
            }
        }
    }

    private String createSign() {
        try {
            String gameCode = SDKParam.getAccountParam(LoginCheckType.ACCOUNT_TYPE_OASIS, 0);
            String key = SDKParam.getAccountParam(LoginCheckType.ACCOUNT_TYPE_OASIS, 1);
            return Md5Util.MD5(gameCode + key).toLowerCase();
        } catch (NoSuchAlgorithmException e) {
            logger.catching(e);
        }
        return "";
    }
}
