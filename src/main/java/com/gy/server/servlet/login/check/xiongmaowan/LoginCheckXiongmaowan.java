package com.gy.server.servlet.login.check.xiongmaowan;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

import javax.servlet.http.HttpServletRequest;

import com.google.gson.JsonObject;
import com.gy.server.assistant.login.LoginCheckType;
import com.gy.server.servlet.login.check.LoginCheck;
import com.gy.server.sdk.SDKParam;
import com.gy.server.utils.JsonUtil;
import com.gy.server.utils.Md5Util;

/**
 * 熊猫玩登录
 *
 * <AUTHOR>
 * @date 2019年10月11日 下午2:25:45
 */
public class LoginCheckXiongmaowan extends LoginCheck {

    private String data;

    public LoginCheckXiongmaowan(List<String> params, String version) {
        super(params, version);
        data = params.get(0);
        accountId = params.get(1);
    }

    @Override
    protected String getUrl(HttpServletRequest request) {
        switch (this.loginCheckType) {
            case LoginCheckType.ACCOUNT_TYPE_XIONGMAOWAN: {
                return "http://mp.gzjykj.com/index.php";
            }
            case LoginCheckType.ACCOUNT_TYPE_6KW_IOS: {
                return "https://iqd.6kw.com/index.php";
            }
        }
        return "http://mp.gzjykj.com/index.php";
    }

    @Override
    protected boolean isPostRequest() {
        return true;
    }

    @Override
    protected String getPostData() {
        //拼接参数
        String appId = SDKParam.getAccountParam(this.loginCheckType, 0);
        StringBuilder sb = new StringBuilder();
        sb.append("service");
        sb.append("=");
        sb.append("sdk.game.checkenter");
        sb.append("&");
        sb.append("appid");
        sb.append("=");
        sb.append(appId);
        sb.append("&");
        sb.append("data");
        sb.append("=");
        sb.append(data);
        sb.append("&");
        sb.append("sign");
        sb.append("=");
        String sign;
        try {
            sign = createSign();
        } catch (NoSuchAlgorithmException | UnsupportedEncodingException e) {
            sign = "";
        }
        sb.append(sign);

        return sb.toString();
    }

    /**
     * 构建签名
     *
     * @return
     * @throws NoSuchAlgorithmException
     * @throws UnsupportedEncodingException
     */
    private String createSign() throws NoSuchAlgorithmException, UnsupportedEncodingException {

        String appId = SDKParam.getAccountParam(this.loginCheckType, 0);
        String loginKey = SDKParam.getAccountParam(this.loginCheckType, 1);

        String sign = appId + "sdk.game.checkenter" + parseSortedDataStr() + loginKey;
        //生成签名
        return Md5Util.MD5(URLEncoder.encode(sign, "UTF-8")).toLowerCase();
    }

    /**
     * 构建data的排序串
     */
    private String parseSortedDataStr() {
        HashMap<String, String> dataMap = JsonUtil.json2Obj(data, HashMap.class);

        List<String> keyList = new ArrayList<>(dataMap.keySet());

        Collections.sort(keyList);

        StringBuilder sb = new StringBuilder();

        for (String key : keyList) {
            sb.append(key);
            sb.append("=");
            sb.append(dataMap.get(key));
            sb.append("&");
        }
        String result = sb.toString().substring(0, sb.length() - 1);

        return result;
    }

    @Override
    protected void unPack(String text) {
        //检测状态
        JsonObject json = JsonUtil.getJsonObject(text);

        try {
            JsonObject stateJson = json.get("state").getAsJsonObject();
            if (stateJson.get("code").getAsString().equals(1) || stateJson.get("code").getAsString().equals("1")) {
                this.result = LoginCheck.RESULT_SUCCESS;
            } else {
                this.result = LoginCheck.RESULT_OVERDUE;
            }
        } catch (Exception e) {
            logger.catching(e);
            this.result = LoginCheck.RESULT_OVERDUE;
        }


    }

}
