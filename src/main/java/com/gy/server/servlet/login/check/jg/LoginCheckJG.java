package com.gy.server.servlet.login.check.jg;

import java.security.NoSuchAlgorithmException;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import com.google.gson.reflect.TypeToken;
import com.gy.server.assistant.login.LoginCheckType;
import com.gy.server.servlet.login.check.LoginCheck;
import com.gy.server.sdk.SDKParam;
import com.gy.server.utils.JsonUtil;
import com.gy.server.utils.Md5Util;


/**
 * 金刚账号登录校验 - 初始版本
 *
 * <AUTHOR> - [Created on 2018/3/28 21:57]
 */
public class LoginCheckJG extends LoginCheck {

    private String sessionId;

    public LoginCheckJG(List<String> params, String version) {
        super(params, version);
        this.sessionId = loginCheckParams.get(0);
        this.accountId = loginCheckParams.get(1);
    }

    @Override
    protected String getUrl(HttpServletRequest request) {
        long timestamp = (long) (System.currentTimeMillis() / 1000.0);
        String gameCode = SDKParam.getAccountParam(LoginCheckType.ACCOUNT_TYPE_JG, 0);
        String sign = createSign(timestamp);
        return String.format("http://api.zhengyueyinhe.com/api/channel_login_verify?game_id=%s&uid=%s&time=%s&sign=%s&sessionid=%s",
                gameCode, accountId, timestamp, sign, sessionId);
    }

    @Override
    protected boolean isPostRequest() {
        return false;
    }

    @Override
    protected String getPostData() {
        return "";
    }

    @Override
    protected void unPack(String text) {
        Map<String, Object> result = JsonUtil.json2Collection(text, new TypeToken<Map<String, Object>>() {
        }.getType());

        int status = JsonUtil.getJsonPrimitive(JsonUtil.obj2Json(result.get("status"))).getAsInt();
        String data = JsonUtil.getJsonObject(JsonUtil.obj2Json(result.get("data"))).getAsJsonPrimitive("msg").getAsString();
        if (status == 1 && "success".equals(data)) {
            //success
            this.result = LoginCheck.RESULT_SUCCESS;
        } else {
            //签名错误
            this.result = LoginCheck.RESULT_BUSY;
        }
    }

    private String createSign(long timestamp) {
        try {
            String gameCode = SDKParam.getAccountParam(LoginCheckType.ACCOUNT_TYPE_JG, 0);
            String key = SDKParam.getAccountParam(LoginCheckType.ACCOUNT_TYPE_JG, 1);
            return Md5Util.MD5(gameCode + accountId + key + timestamp).toLowerCase();
        } catch (NoSuchAlgorithmException e) {
            logger.catching(e);
        }
        return "";
    }
}
