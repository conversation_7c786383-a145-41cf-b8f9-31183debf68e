package com.gy.server.servlet.login.check.pinrui;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.NoSuchAlgorithmException;
import java.util.List;

import javax.servlet.http.HttpServletRequest;

import com.gy.server.assistant.login.LoginCheckType;
import com.gy.server.servlet.login.check.LoginCheck;
import com.gy.server.sdk.SDKParam;
import com.gy.server.utils.JsonUtil;
import com.gy.server.utils.Md5Util;
import com.gy.server.utils.net.HttpUtil;


/**
 * 6KW登录校验 - 初始版本1.0.0
 *
 * <AUTHOR> - [Created on 2019/09/26 28:57]
 */
public class LoginCheckPinrui extends LoginCheck {

    private String token;
    private String sign = "app_id=%s&mem_id=%s&user_token=%s&app_key=%s";

    public LoginCheckPinrui(List<String> params, String version) {
        super(params, version);
        accountId = loginCheckParams.get(0);
        token = loginCheckParams.get(1);
    }

    @Override
    protected String getUrl(HttpServletRequest request) {
        return "https://api.sycent.com/api/v7/cp/user/check";

    }

    @Override
    public void check(HttpServletRequest request) {
        String url = getUrl(request);
        try {
            String resultText = HttpUtil.requestHttpWithPostReturnString(url, getPostData().getBytes(StandardCharsets.UTF_8));
            this.unPack(resultText);
        } catch (IOException e) {
            logger.catching(e);
        }
    }

    @Override
    protected boolean isPostRequest() {
        return true;
    }

    @Override
    protected String getPostData() {
        String appId = SDKParam.getAccountParam(this.loginCheckType, 0);
        String apiKey = SDKParam.getAccountParam(this.loginCheckType, 1);
        if(this.loginCheckType == LoginCheckType.ACCOUNT_TYPE_PINRUI_IOS){
            return String.format(sign + "&sign=%s", appId, accountId, token, apiKey, createSign());
        }
        return String.format("app_id=%s&mem_id=%s&user_token=%s&sign=%s", appId, accountId, token, createSign());
    }

    @Override
    protected void unPack(String text) {
        int code = JsonUtil.getJsonObject(text).getAsJsonPrimitive("status").getAsInt();
        if (code == 1) {
            this.result = RESULT_SUCCESS;
        } else {
            this.result = RESULT_BUSY;
        }
    }

    private String createSign() {
        try {
            String appId = SDKParam.getAccountParam(this.loginCheckType, 0);
            String apiKey = SDKParam.getAccountParam(this.loginCheckType, 1);
            String key = String.format(sign, appId, accountId, token, apiKey);
            return Md5Util.MD5(key).toLowerCase();
        } catch (NoSuchAlgorithmException e) {
            logger.catching(e);
        }
        return "";
    }
}
