package com.gy.server.servlet.login.check.pinrui;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import com.google.gson.reflect.TypeToken;
import com.gy.server.servlet.login.check.LoginCheck;
import com.gy.server.sdk.SDKParam;
import com.gy.server.utils.JsonUtil;
import com.gy.server.utils.Md5Util;

/**
 * 品锐安卓登录校验 - 初始版本1.0.0
 *
 * <AUTHOR> - [Created on 2019/09/26 28:57]
 */
public class LoginCheckPinruiAnd extends LoginCheck {

    private String token;
    private long timestamp;

    public LoginCheckPinruiAnd(List<String> params, String version) {
        super(params, version);
        token = loginCheckParams.get(0);
        timestamp = (long) (System.currentTimeMillis() / 1000.0);
    }

    @Override
    protected String getUrl(HttpServletRequest request) {
        return "http://sdk.lingleigame.com/api/token";

    }

    @Override
    protected boolean isPostRequest() {
        return true;
    }

    @Override
    protected String getPostData() {
        return String.format("token=%s&time=%s&sign=%s", token, timestamp, createSign());
    }

    private String createSign() {
        try {
            String apiKey = SDKParam.getAccountParam(this.loginCheckType, 0);
            String md5Str = token + timestamp + apiKey;
            return Md5Util.MD5(md5Str).toLowerCase();
        } catch (Exception e) {
            logger.catching(e);
        }
        return "";
    }

    @Override
    protected void unPack(String text) {
        //检测状态
        Map<String, String> result = JsonUtil.json2Collection(text, new TypeToken<Map<String, String>>() {
        }.getType());

        String code = result.get("code");
        if ("1".equals(code)) {
            this.accountId = result.get("uid");
            this.result = RESULT_SUCCESS;
        } else {
            this.result = RESULT_BUSY;
        }
    }
}
