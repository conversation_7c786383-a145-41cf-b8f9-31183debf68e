package com.gy.server.servlet.login.check.gy;

import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import com.google.gson.reflect.TypeToken;
import com.gy.server.assistant.account.AccountAssistant;
import com.gy.server.servlet.login.check.LoginCheck;
import com.gy.server.utils.JsonUtil;
import com.ttlike.server.tl.baselib.config.ShareConfig;


/**
 * 光耀账号登录校验 - 初始版本1.0.0
 *
 * <AUTHOR> - [Created on 2018/3/28 21:57]
 */
public class LoginCheckGY extends LoginCheck {

    private String token;

    public LoginCheckGY(List<String> params, String version) {
        super(params, version);
        this.accountId = loginCheckParams.get(0);
        this.token = loginCheckParams.get(1);
    }

    @Override
    protected String getUrl(HttpServletRequest request) {
        String basePath = ShareConfig.gate.getValue();

        return basePath + "account_login_check?" + "id=" + this.accountId + "&token" + "=" + this.token + "&sign" + "=" + this.createSign();

    }

    @Override
    protected boolean isPostRequest() {
        return false;
    }

    @Override
    protected String getPostData() {
        return "";
    }

    @Override
    protected void unPack(String text) {
        Map<String, String> result = JsonUtil.json2Collection(text, new TypeToken<Map<String, String>>() {
        }.getType());

        int status = Integer.parseInt(result.get("status"));
        if (status == 0) {
            //success
            this.result = LoginCheck.RESULT_SUCCESS;
        } else if (status == 1) {
            //操作失败,渠道那边的问题，通知客户端重登录
            this.result = LoginCheck.RESULT_OVERDUE;
        } else if (status == 2 || status == 3) {
            //签名错误 || 参数错误
            this.result = LoginCheck.RESULT_BUSY;
            logger.info(LoginCheck.RESULT_BUSY + " from sign fail or para valid");
        } else {
            //未知错误代码
            this.result = LoginCheck.RESULT_BUSY;
            logger.info(LoginCheck.RESULT_BUSY + " from unkown error code of GY");
        }
    }

    private String createSign() {
        try {
            Map<String, String> map = new HashMap<>();
            map.put("id", accountId);
            map.put("token", token);
            return AccountAssistant.createSign(map);
        } catch (NoSuchAlgorithmException e) {
            logger.catching(e);
        }
        return "";
    }
}
