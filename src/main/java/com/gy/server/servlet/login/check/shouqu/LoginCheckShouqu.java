package com.gy.server.servlet.login.check.shouqu;

import java.security.NoSuchAlgorithmException;
import java.util.List;

import javax.servlet.http.HttpServletRequest;

import com.gy.server.servlet.login.check.LoginCheck;
import com.gy.server.sdk.SDKParam;
import com.gy.server.utils.Md5Util;

/**
 * 手趣sdk登录检测
 * <AUTHOR>
 * @date  2019年9月6日 下午1:33:54
 */
public class LoginCheckShouqu extends LoginCheck {
	
	//登录时间戳
	private long timestamp;
	//签名
	private String sign;
	
	public LoginCheckShouqu(List<String> params, String version) {
		super(params, version);
		this.timestamp = Long.valueOf(loginCheckParams.get(0));
		this.accountId = loginCheckParams.get(1);
		this.sign = loginCheckParams.get(2);
	}
	
	@Override
	public void check(HttpServletRequest request) {
		String _sign = createSign();
		if (_sign.equals(sign)) {
		    //success
		    this.result = LoginCheck.RESULT_SUCCESS;
		} else {
		    //签名错误
		    this.result = LoginCheck.RESULT_OVERDUE;
		}
	}
	
	private String createSign() {
        try {
            String key = SDKParam.getAccountParam(loginCheckType, 0);
            return Md5Util.MD5(accountId + timestamp + key).toLowerCase();
        } catch (NoSuchAlgorithmException e) {
            logger.catching(e);
        }
        return "";
    }
	
	@Override
	protected String getUrl(HttpServletRequest request) {
		return null;
	}

	@Override
	protected boolean isPostRequest() {
		return false;
	}

	@Override
	protected String getPostData() {
		return null;
	}

	@Override
	protected void unPack(String text) {
		
	}

}
