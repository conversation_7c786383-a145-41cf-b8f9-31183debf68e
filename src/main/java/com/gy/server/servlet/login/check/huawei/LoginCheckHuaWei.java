package com.gy.server.servlet.login.check.huawei;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.Signature;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import javax.net.ssl.SSLContext;
import javax.servlet.http.HttpServletRequest;

import com.google.common.reflect.TypeToken;
import com.gy.server.assistant.login.LoginCheckType;
import com.gy.server.servlet.login.check.LoginCheck;
import com.gy.server.sdk.SDKParam;
import com.gy.server.utils.CollectionUtil;
import com.gy.server.utils.JsonUtil;

import org.apache.commons.codec.binary.Base64;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.TrustStrategy;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.ssl.SSLContexts;
import org.apache.http.util.Asserts;
import org.apache.http.util.EntityUtils;

/**
 * <AUTHOR> - [Created on 2019/9/23 14:55]
 */
public class LoginCheckHuaWei extends LoginCheck {

    private static final int RETURN_CODE_SUCCEED = 0;
    private static final int HTTP_RESPONSE_STATUS_CODE_OK = 200;

    String ts;
    String playerLevel;
    String playerSSign;

    public LoginCheckHuaWei(List<String> params, String version) {
        super(params, version);
        this.ts = loginCheckParams.get(0);
        this.accountId = loginCheckParams.get(1);
        this.playerLevel = loginCheckParams.get(2);
        this.playerSSign = loginCheckParams.get(3);
    }

    @Override
    public void check(HttpServletRequest request) {
        try {
            String url = getUrl(request);
            // 响应消息中返回参数
            Map<String, Object> responseParamPairs = doPost(url, getParamData());
            if (CollectionUtil.isEmpty(responseParamPairs)) {
                logger.error("HaiWei login None response parameter.");
            } else {
                String rtnCode = getString("rtnCode", responseParamPairs);
                if (rtnCode != null && RETURN_CODE_SUCCEED == (int) Double.parseDouble(rtnCode)) {
                    //成功
                    this.result = LoginCheck.RESULT_SUCCESS;
                } else {
                    this.result = LoginCheck.RESULT_BUSY;
                    logger.error("HaiWei login rtnCode: " + getString("rtnCode", responseParamPairs) + "errMsg: " + getString("errMsg", responseParamPairs));
                }
            }
        } catch (Exception e) {
            logger.catching(e);
        }
    }

    @Override
    protected String getUrl(HttpServletRequest request) {
        return "https://jos-api.cloud.huawei.com/gameservice/api/gbClientApi";
    }

    @Override
    protected boolean isPostRequest() {
        return true;
    }

    @Override
    protected String getPostData() {
        return "";
    }

    private Map<String, String> getParamData() {
        String appId = SDKParam.getAccountParam(LoginCheckType.ACCOUNT_TYPE_HUAWEI, 0);
        String cpId = SDKParam.getAccountParam(LoginCheckType.ACCOUNT_TYPE_HUAWEI, 1);
        String cpAuthKey = SDKParam.getAccountParam(LoginCheckType.ACCOUNT_TYPE_HUAWEI, 2);

        Map<String, String> params = new HashMap<>();
        params.put("method", "external.hms.gs.checkPlayerSign");
        params.put("appId", appId);
        params.put("cpId", cpId);
        params.put("ts", this.ts);
        params.put("playerId", this.accountId);
        params.put("playerLevel", this.playerLevel);
        params.put("playerSSign", this.playerSSign);
        params.put("cpSign", generateCPSign(params, cpAuthKey));

        return params;
    }

    @Override
    protected void unPack(String text) {

    }

    private static String getString(String key, Map<String, Object> responseParamPairs) {
        Asserts.notNull(responseParamPairs, "responseParamPairs");
        Object value = responseParamPairs.get(key);
        if (value == null) {
            return null;
        }
        return value.toString();
    }

    /**
     * 创建跳过SSL验证的httpClient实例，https://jos-api.cloud.huawei.com/gameservice/api/gbClientApi这个地址暂时没有添加SSL证书，所以需要跳过SSL验证
     */
    private static CloseableHttpClient getIgnoeSSLClient() throws Exception {
        SSLContext sslContext = SSLContexts.custom().loadTrustMaterial(null, new TrustStrategy() {
            @Override
            public boolean isTrusted(X509Certificate[] x509Certificates, String s) throws CertificateException {
                return true;
            }
        }).build();
        //创建httpClient
        CloseableHttpClient client = HttpClients.custom().setSSLContext(sslContext).
                setSSLHostnameVerifier(new NoopHostnameVerifier()).build();
        return client;
    }

    private static Map<String, Object> doPost(String url, Map<String, String> paramaters) {
        HttpPost httpReq = new HttpPost(url);
        // 创建无需SSL验证的httpClient实例.
        CloseableHttpClient httpclient = null;
        try {
            httpclient = getIgnoeSSLClient();
        } catch (Exception e) {
            logger.catching(e);
        }
        try {
            if (paramaters != null) {
                List<NameValuePair> paramPairs = new ArrayList<NameValuePair>();
                BasicNameValuePair bnv;
                for (Map.Entry<String, String> entry : paramaters.entrySet()) {
                    bnv = new BasicNameValuePair(entry.getKey(), entry.getValue());
                    paramPairs.add(bnv);
                }
                httpReq.setEntity(new UrlEncodedFormEntity(paramPairs, "UTF-8"));
            }
            Map<String, Object> responseParamPairs = new HashMap<>();
            HttpResponse resp = httpclient.execute(httpReq);
            if (null != resp && HTTP_RESPONSE_STATUS_CODE_OK == resp.getStatusLine().getStatusCode()) {
                responseParamPairs = JsonUtil.json2Collection(EntityUtils.toString(resp.getEntity()), new TypeToken<Map<String, Object>>() {
                }.getType());
            }
            return responseParamPairs;
        } catch (Exception e) {
            logger.catching(e);
            return null;
        } finally {
            try {
                httpclient.close();
            } catch (IOException e) {
                logger.catching(e);
            }
        }
    }

    private static String format(Map<String, String> params) {
        StringBuffer base = new StringBuffer();
        Map<String, String> tempMap = new TreeMap<String, String>(params);
        //获取计算nsp_key的基础串
        try {
            for (Map.Entry<String, String> entry : tempMap.entrySet()) {
                String k = entry.getKey();
                String v = entry.getValue();
                base.append(k).append("=").append(URLEncoder.encode(v, "UTF-8")).append("&");
            }
        } catch (UnsupportedEncodingException e) {
            logger.error("HuaWei Login Encode parameters failed.");
            logger.catching(e);
        }
        String body = base.toString().substring(0, base.toString().length() - 1);
        // 空格和星号转义
        body = body.replaceAll("\\+", "%20").replaceAll("\\*", "%2A");
        return body;
    }

    private static String generateCPSign(Map<String, String> requestParams, final String cpAuthKey) {
        //对消息体中查询字符串按字典序排序并且进行URLCode编码
        String baseStr = format(requestParams);
        //用CP侧签名私钥对上述编码后的请求字符串进行签名
        String cpSign = sign(baseStr.getBytes(Charset.forName("UTF-8")), cpAuthKey);
        return cpSign;
    }

    private static String sign(byte[] data, String privateKey) {
        try {
            byte[] e = Base64.decodeBase64(privateKey);
            PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(e);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            PrivateKey privateK = keyFactory.generatePrivate(pkcs8KeySpec);
            Signature signature = Signature.getInstance("SHA256WithRSA");
            signature.initSign(privateK);
            signature.update(data);
            String signs = Base64.encodeBase64String(signature.sign());
            return signs;
        } catch (Exception var) {
            logger.error("HuaWei Login SignUtil.sign error." + var);
            return "";
        }
    }
}
