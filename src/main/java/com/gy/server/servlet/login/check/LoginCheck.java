package com.gy.server.servlet.login.check;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import javax.servlet.http.HttpServletRequest;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.gy.server.utils.CharsetEncoding;
import com.gy.server.utils.net.HttpUtil;

/**
 * <AUTHOR> - [Created on 2018/3/28 22:18]
 */
public abstract class LoginCheck {

    public final static String RESULT_SUCCESS = "SUCCESS";
    public final static String RESULT_OVERDUE = "OVERDUE";
    public final static String RESULT_BUSY = "BUSY";
    public final static String RESULT_LOGIN_CODE_NO = "LOGIN_CODE_NO";
    public final static String RESULT_LOGIN_CODE_ERROR = "LOGIN_CODE_ERROR";
    public final static String RESULT_LOGIN_CODE_USED = "LOGIN_CODE_USED";
    public final static String RESULT_LOGIN_CODE_MAINTAINS = "LOGIN_CODE_MAINTAINS";

    protected static Logger logger = LogManager.getLogger(LoginCheck.class);

    protected List<String> loginCheckParams;

    protected String version;


    protected List<String> resultParams = new ArrayList<>();

    protected String result = RESULT_BUSY;

    protected String accountId;

    protected int loginCheckType;

    public LoginCheck(List<String> params, String version) {
        this.loginCheckParams = params;
        this.version = version;
    }

    public void check(HttpServletRequest request) {
        String url = getUrl(request);
        try {
            String resultText;

            if (isPostRequest()) {
                resultText = HttpUtil.requestHttpWithPostReturnString(url, getPostData(), CharsetEncoding.ENCODING_UTF_8);
            } else {
                resultText = HttpUtil.requestHttpWithGetReturnString(url);
            }

            this.unPack(resultText);
        } catch (IOException e) {
            logger.catching(e);
        }

    }

    protected void addResultParams(String param) {
        resultParams.add(param);
    }


    public String getResult() {
        return result;
    }

    public boolean isSuccess() {
        return result.equalsIgnoreCase(RESULT_SUCCESS);
    }

    public String getAccountId() {
        return accountId;
    }

    public List<String> getResultParams() {
        return resultParams;
    }

    public int getLoginCheckType() {
        return loginCheckType;
    }

    public void setLoginCheckType(int loginCheckType) {
        this.loginCheckType = loginCheckType;
    }

    protected abstract String getUrl(HttpServletRequest request);

    protected abstract boolean isPostRequest();

    protected abstract String getPostData();

    protected abstract void unPack(String text);
}
