package com.gy.server.servlet.login.check.oasis;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTVerificationException;
import com.auth0.jwt.interfaces.DecodedJWT;

/**
 * <AUTHOR> - [Created on 2020/2/8 19:10]
 */
public class A {

    private static JWTVerifier verifier;

    static {
        try {
            String secret = "zqJGuA4ZpxQEPhuaJuPL";
            Algorithm algorithm = Algorithm.HMAC256(secret);
            verifier = JWT.require(algorithm).build();
        } catch (IllegalArgumentException e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }
    }

    public static void main(String[] args) {
        try {
            //String token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiIzMTIiLCJpYXQiOjE1ODExNTU0NTUsImNsaWVudFR5cGUiOiJnYW1lIiwiYXBwSWQiOjMxMiwiY2xpZW50SWQiOiJtYnNhZW4iLCJjaGVja3N1bSI6IjlhZTBlYTE2Y2U4NWQ3ZTgiLCJwbGF5ZXJJZCI6IjkwMDE1ODExNTA3NTg5NSIsInV1aWQiOiI5MDAxNTgxMTUwNzU4OTUiLCJuaWNrbmFtZSI6IiIsInVzZXJuYW1lIjoiIiwiZXhwIjoxNTgxNzYwMjU1LCJsaWZldGltZSI6NjA0ODAwLCJyb2xlcyI6WyJST0xFX1VTRVIiXSwiZXhwYW5zaW9uIjp7InVpZCI6IjkwMDE1ODExNTA3NTg5NSJ9fQ.c3gNTzxE3a9_iLyhlcRuQPUiVqGbh1ZuQGyHRXUFH9E";
            String token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJjbGllbnRUeXBlIjoiZ2FtZSIsImNsaWVudElkIjoibWFraWVuIiwidXVpZCI6IjMwMDAxODYyMjQ0MTAxMiIsInBsYXllcklkIjoiMzAwMDE4NjIyNDQxMDEyIiwibmlja25hbWUiOiIjdGVtcC41OTFjYjM2YzA5MTI1NzhmMTIxNDgwZjk4Y2JkZjYyNCIsInVzZXJuYW1lIjoiI3RlbXAuNTkxY2IzNmMwOTEyNTc4ZjEyMTQ4MGY5OGNiZGY2MjQiLCJpYXQiOjE1ODExNTQ4OTEsImV4cCI6MTU4MTE2MjA5MSwibGlmZXRpbWUiOjcyMDAsInJvbGVzIjpbIlJPTEVfVVNFUiJdLCJleHBhbnNpb24iOnsidWlkIjoiMzAwMDE4NjIyNDQxMDEyIn19.akvRdpptSXNUVfyIibWUb8VQQZbUj6lrceGyTamqNzQ";
            DecodedJWT jwt = verifier.verify(token);
        } catch (JWTVerificationException e) {
            e.printStackTrace();
        }
    }
}
