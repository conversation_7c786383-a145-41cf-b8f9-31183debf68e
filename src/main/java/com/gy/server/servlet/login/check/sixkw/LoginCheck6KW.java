package com.gy.server.servlet.login.check.sixkw;

import com.gy.server.assistant.login.LoginCheckType;
import com.gy.server.servlet.login.check.LoginCheck;
import com.gy.server.sdk.SDKParam;
import com.gy.server.utils.JsonUtil;
import com.gy.server.utils.Md5Util;
import com.gy.server.utils.net.HttpUtil;
import org.apache.commons.lang3.tuple.Pair;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.List;


/**
 * 6KW登录校验 - 初始版本1.0.0
 *
 * <AUTHOR> - [Created on 2019/09/26 28:57]
 */
public class LoginCheck6KW extends LoginCheck {

    private String token;

    public LoginCheck6KW(List<String> params, String version) {
        super(params, version);
        accountId = loginCheckParams.get(0);
        token = loginCheckParams.get(1);
    }

    @Override
    protected String getUrl(HttpServletRequest request) {
        String appId = SDKParam.getAccountParam(LoginCheckType.ACCOUNT_TYPE_6KW, 0);
        return "https://uscpcheck.6kw.com/uscpcheck/" + appId;
    }

    @Override
    public void check(HttpServletRequest request) {
        String url = getUrl(request);
        List<Pair<String, String>> requestPropertiesParam = new ArrayList<>();
        requestPropertiesParam.add(Pair.of("AUTH6KW", createSign()));
        requestPropertiesParam.add(Pair.of("Content-Type", "application/json"));
        try {
            String resultText = HttpUtil.requestHttpWithPostReturnString(url, getPostData().getBytes(StandardCharsets.UTF_8), requestPropertiesParam);
            this.unPack(resultText);
        } catch (IOException e) {
            logger.catching(e);
        }
    }

    @Override
    protected boolean isPostRequest() {
        return true;
    }

    @Override
    protected String getPostData() {
        return "{\"token\":\"" + token + "\"}";
    }

    @Override
    protected void unPack(String text) {
        int code  = JsonUtil.getJsonObject(text).getAsJsonPrimitive("code").getAsInt();
        if (code == 1) {
            this.result = RESULT_SUCCESS;
        } else {
            this.result = RESULT_BUSY;
        }
    }

    private String createSign() {
        try {
            String appId = SDKParam.getAccountParam(LoginCheckType.ACCOUNT_TYPE_6KW, 0);
            String apiKey = SDKParam.getAccountParam(LoginCheckType.ACCOUNT_TYPE_6KW, 1);
            String key = String.format("uscpcheck/%s{\"token\":\"%s\"}%s", appId, token, apiKey);
            return Md5Util.MD5(key).toLowerCase();
        } catch (NoSuchAlgorithmException e) {
            logger.catching(e);
        }
        return "";
    }
}
