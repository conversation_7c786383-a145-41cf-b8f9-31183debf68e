package com.gy.server.servlet.login.check.oasis;

import java.util.List;

import javax.servlet.http.HttpServletRequest;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTVerificationException;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.gy.server.assistant.login.LoginCheckType;
import com.gy.server.servlet.login.check.LoginCheck;
import com.gy.server.sdk.SDKParam;

/**
 * 绿洲账号登录校验（本地解析验证） - 初始版本4.9.8
 *
 * <AUTHOR> - [Created on 2018/11/14 14:59]
 */
public class LoginCheckOasisLocal extends LoginCheck {

    private static JWTVerifier verifier;

    static {
        try {
            String secret = SDKParam.getAccountParam(LoginCheckType.ACCOUNT_TYPE_OASIS, 2);
            Algorithm algorithm = Algorithm.HMAC256(secret);
            verifier = JWT.require(algorithm).build();
        } catch (IllegalArgumentException e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }
    }

    private String token;

    public LoginCheckOasisLocal(List<String> params, String version) {
        super(params, version);
        this.token = loginCheckParams.get(0);
    }


    @Override
    public void check(HttpServletRequest request) {
        try {
            DecodedJWT jwt = verifier.verify(token);
            accountId = jwt.getClaim("expansion").asMap().get("uid").toString();
            if (accountId != null) {
                this.result = LoginCheck.RESULT_SUCCESS;
            }
        } catch (JWTVerificationException e) {
            logger.catching(e);
            this.result = LoginCheck.RESULT_BUSY;//token不正确
        }
    }

    @Override
    protected String getUrl(HttpServletRequest request) {
        return null;
    }

    @Override
    protected boolean isPostRequest() {
        return false;
    }

    @Override
    protected String getPostData() {
        return null;
    }

    @Override
    protected void unPack(String text) {

    }
}
