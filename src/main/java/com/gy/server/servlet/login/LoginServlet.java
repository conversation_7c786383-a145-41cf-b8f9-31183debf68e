package com.gy.server.servlet.login;

import java.io.IOException;
import java.net.URLDecoder;
import java.util.*;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.google.gson.reflect.TypeToken;
import com.gy.server.Configuration;
import com.gy.server.assistant.login.LoginAssistant;
import com.gy.server.assistant.login.LoginCheckType;
import com.gy.server.assistant.login.LoginRecord;
import com.gy.server.game.server.GameAreaManager;
import com.gy.server.servlet.login.check.LoginCheck;
import com.gy.server.servlet.login.check.changyou.LoginChangYou;
import com.gy.server.servlet.login.check.douyou.LoginCheckDouYou;
import com.gy.server.servlet.login.check.gy.LoginCheckGY;
import com.gy.server.servlet.login.check.huawei.LoginCheckHuaWei;
import com.gy.server.servlet.login.check.jg.LoginCheckJG;
import com.gy.server.servlet.login.check.maoer.LoginCheckMaoer;
import com.gy.server.servlet.login.check.oasis.LoginCheckOasis52x;
import com.gy.server.servlet.login.check.oasis.LoginCheckOasisLocal;
import com.gy.server.servlet.login.check.oppo.LoginCheckOppo;
import com.gy.server.servlet.login.check.pinrui.LoginCheckPinrui;
import com.gy.server.servlet.login.check.pinrui.LoginCheckPinruiAnd;
import com.gy.server.servlet.login.check.shouqu.LoginCheckShouqu;
import com.gy.server.servlet.login.check.sixkw.LoginCheck6KW;
import com.gy.server.servlet.login.check.xiongmaowan.LoginCheckXiongmaowan;
import com.gy.server.servlet.login.check.yijie.LoginCheckYiJie;
import com.gy.server.db.relation.hibernate.HibernateUtil;
import com.gy.server.utils.CharsetEncoding;
import com.gy.server.utils.CollectionUtil;
import com.gy.server.utils.JsonUtil;
import com.gy.server.utils.LockUtil;
import com.gy.server.utils.jprotobuf.PbUtilCompress;
import com.gy.server.utils.key.KeyType;
import com.gy.server.utils.key.KeyUtil;
import com.gy.server.utils.net.ServletUtil;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.redis.key.BaseRedisKey;
import com.ttlike.server.tl.baselib.serialize.RedisAssistant;
import com.ttlike.server.tl.baselib.serialize.logincode.LoginCode;
import com.ttlike.server.tl.baselib.serialize.logincode.LoginCodeAssistant;
import com.ttlike.server.tl.baselib.serialize.logincode.LoginCodeGroup;
import com.ttlike.server.tl.baselib.serialize.maintain.Maintain;
import com.ttlike.server.tl.baselib.serialize.player.MiniPlayer;
import com.ttlike.server.tl.baselib.serialize.player.RoleInfo;

import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR> - [Created on 2018/3/28 21:24]
 */
@WebServlet(name = "LoginServlet", urlPatterns = "/login")
public class LoginServlet extends HttpServlet {
	/**
	 * 需要校验用户协议的渠道
	 */
	public static final Map<String, String> CHECK_USER_PROTOCOL = new HashMap<String, String>() {
		{
			put("3116", "bilibili");
			put("3025", "vivo");
			put("3164", "OPPO");
		}
	};

    private static final String RES_PARAM_RESULT = "result";
    private static final String RES_PARAM_PARAMS = "params";
    private static final String RES_PARAM_TOKEN = "gyToken";
    private static final String RES_PARAM_MAINTAINS = "maintains";
    private static final String RES_PARAM_NOW_SERVER_TIME = "nowServerTime";
    private static final String RES_PARAM_ROLE_INFO = "roleInfos";
    public static final String RES_PARAM_USER_PROTOCOL = "userProtocol";
    private static Logger logger = LogManager.getLogger("Login");

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        request.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);
        response.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);
        long now = System.currentTimeMillis();
        Map<Object, Object> res = new HashMap<>();
        res.put(RES_PARAM_NOW_SERVER_TIME, now);
        ServletUtil.infoLog(LoginServlet.class.getName(), request, logger);
        int roleCount = -1;
        try {
            String loginCheckResult = null;
            String ip = ServletUtil.getIpByHttpServletRequest(request);
            if (GameAreaManager.getInstance().getServers().values().stream()
                    .noneMatch(a -> a.getLimitIps().contains(ip))) {
                /* 停服维护公告 */
                Map<Long, String> maintainMap = new HashMap<>();
                for (byte[] b : TLBase.getInstance().getRedisAssistant().hashGetAllBytes(BaseRedisKey.Billing.MAINTAIN_TEXT.getRedisKey())
                        .values()) {
                    try {
                        Maintain maintain = PbUtilCompress.decode(Maintain.class, b);
                        if (now >= maintain.getStartDateTime()
                                && now < maintain.getStartDateTime() + maintain.getTime() * 60 * 1000L) {
                            maintainMap.put(maintain.getStartDateTime() + maintain.getTime() * 60 * 1000L,
                                    maintain.getText());
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
                if (maintainMap.size() > 0) {
                    loginCheckResult = LoginCheck.RESULT_LOGIN_CODE_MAINTAINS;
                    res.put(RES_PARAM_MAINTAINS, maintainMap);
                    res.put(RES_PARAM_RESULT, loginCheckResult);
                    String str = JsonUtil.map2Json(res);
                    response.getWriter().write(str);
                    return;
                }
            }

            /* 取参数 */
            int accountType = Integer.parseInt(request.getParameter("accountType"));
            String version = request.getParameter("version");
            String loginCodeText = request.getParameter("loginCode");
            String paramStr = URLDecoder.decode(request.getParameter("params"), CharsetEncoding.ENCODING_UTF_8);
            List<String> params = JsonUtil.json2Collection(paramStr, new TypeToken<List<String>>() {
            }.getType());
            String accountId = null;
            LoginCheck check = null;
            int loginCheckType = LoginAssistant.calculateLoginCheckTypeByAccountType(accountType);
            if (loginCheckType > 0) {
                check = createLoginCheck(loginCheckType, params, version);//创建校验对象
            }
            if (check != null) {
                //☆☆☆☆ 核心逻辑：校验 ☆☆☆☆
                check.check(request);
                List<String> resultParams = check.getResultParams();//结果参数列表
                if (check.isSuccess()) {//校验成功
                    accountId = check.getAccountId();//获得第三方账号
                    if (Configuration.isOpenActiveCode() && !TLBase.getInstance().getRedisAssistant()
                            .setIsExist(BaseRedisKey.Billing.LOGIN_CODE_ACCOUNT_SET.getRedisKey(), accountId)) {//激活码检查
                        if (StringUtils.isEmpty(loginCodeText)) {
                            loginCheckResult = LoginCheck.RESULT_LOGIN_CODE_NO;
                        } else if (loginCodeText.length() > 50 && !loginCodeText.matches("[0-9a-zA-Z]+")) {
                            loginCheckResult = LoginCheck.RESULT_LOGIN_CODE_ERROR;
                        } else {
                            loginCodeText = loginCodeText.toUpperCase();
                            LoginCode loginCode = LoginCodeAssistant.getLoginCodeByKey(loginCodeText);
                            if (loginCode == null) {
                                loginCheckResult = LoginCheck.RESULT_LOGIN_CODE_ERROR;
                            } else if (loginCode.getUsedCount() >= loginCode.getMaxUseCount()) {
                                loginCheckResult = LoginCheck.RESULT_LOGIN_CODE_USED;
                            } else {
                                LoginCodeGroup loginCodeGroup = LoginCodeAssistant
                                        .getLoginCodeGroupByGroupId(loginCode.getGroupId());
                                if (loginCodeGroup != null && now >= loginCodeGroup.getStartTime()
                                        && now <= loginCodeGroup.getEndTime()
                                        && LoginCodeAssistant.updateLoginCodeInfo(loginCodeText)) {
                                    TLBase.getInstance().getRedisAssistant().setAdd(BaseRedisKey.Billing.LOGIN_CODE_ACCOUNT_SET.getRedisKey(),
                                            accountId);//记录使用情况
                                } else {
                                    loginCheckResult = LoginCheck.RESULT_LOGIN_CODE_ERROR;
                                }
                            }
                        }
                    }
                    if (StringUtils.isEmpty(loginCheckResult)) {
                        synchronized (LockUtil.getLockKey(accountId + accountType)) {
                            /* 根据账号及账号类型获得LoginRecord对象，如果不存在则创建并存储 */
                            LoginRecord loginRecord = LoginAssistant
                                    .getLoginRecordByAccountTypeAndAccountId(accountType, accountId);
                            if (loginRecord == null) {
                                loginRecord = new LoginRecord();
                                loginRecord.setAccountType(accountType);
                                loginRecord.setAccountId(accountId);
                                loginRecord.setLoginToken(KeyUtil.createKey(KeyType.num_word, 25));
                            }
                            if(System.currentTimeMillis() - loginRecord.getUpdateTime().getTime() > 5000) {
                                //超过5s，才重新生成token
                                loginRecord.setLoginToken(KeyUtil.createKey(KeyType.num_word, 25));
                                loginRecord.setUpdateTime(new Date());
                            }
                            HibernateUtil.update(loginRecord);
                            resultParams.add(0, accountId);//账号需要放在结果参数的第一位发回去
                            res.put(RES_PARAM_TOKEN, loginRecord.getLoginToken());//返回数据-token

                            /* 返回数据-角色信息 */
                            List<RoleInfo> roleInfos = getRoleInfos(accountId, accountType);
                            if (CollectionUtil.isNotEmpty(roleInfos)) {
                                res.put(RES_PARAM_ROLE_INFO, roleInfos);
                            }
                            roleCount = roleInfos.size();
                        }
                    }
                }
                res.put(RES_PARAM_PARAMS, resultParams);//返回数据-结果参数列表
                if (StringUtils.isEmpty(loginCheckResult)) {//更新登录校验结果
                    loginCheckResult = check.getResult();
                    if (LoginCheck.RESULT_BUSY.equalsIgnoreCase(check.getResult())) {
                        logger.info(LoginCheck.RESULT_BUSY + " from init");
                    }
                } else {
                    loginCheckResult = LoginCheck.RESULT_BUSY;//更新登录校验结果，无对应类型的登录校验对象，结果为BUSY
                    logger.info(LoginCheck.RESULT_BUSY + " from no the login type");
                }
            }
			if (CHECK_USER_PROTOCOL.containsKey(request.getParameter("channel"))) {
				if (accountId != null) {
					res.put(RES_PARAM_USER_PROTOCOL, TLBase.getInstance().getRedisAssistant()
							.setIsExist(BaseRedisKey.Billing.USER_PROTOCOL_ACCOUNT_SET.getRedisKey(), accountId));//是否已同意用户协议
				}
			} else {
				res.put(RES_PARAM_USER_PROTOCOL, true);//不校验用户协议时直接给客户端已同意过用户协议标识
			}
            res.put(RES_PARAM_RESULT, loginCheckResult);//返回数据-登录校验结果
            this.logInfo(accountType, ServletUtil.getIpByHttpServletRequest(request), accountId, loginCheckResult,
                    params, roleCount);//日志
        } catch (Exception e) {
            logger.warn("login error " + e.toString(), e);
            logger.catching(e);
            res.put(RES_PARAM_RESULT, LoginCheck.RESULT_BUSY);
            logger.info(LoginCheck.RESULT_BUSY + " from exception");
        }
        String str = JsonUtil.map2Json(res);
        response.getWriter().write(str);
    }


    /**
     * 获取指定账号的角色信息
     */
    public List<RoleInfo> getRoleInfos(String accountId, int accountType) {
        String hash = BaseRedisKey.PlayerKey.Account_2_PID.getRedisKey(accountId, accountType);
        List<RoleInfo> rst = new ArrayList<>();
        RedisAssistant redisAssistant = TLBase.getInstance().getRedisAssistant();
        Map<String, String> roles = redisAssistant.hashGetAllString(hash);
        if (CollectionUtil.isNotEmpty(roles)) {
            boolean isActive = redisAssistant.setIsExist(BaseRedisKey.Billing.LOGIN_CODE_ACCOUNT_SET.getRedisKey(), accountId);
            for (String pid : roles.values()) {
                String minRedisKey = BaseRedisKey.Commons.MINI_PLAYER.getRedisKey(pid);
                byte[] bytes = redisAssistant.bytesGet(minRedisKey);
                if (bytes != null) {
                    MiniPlayer miniPlayer = PbUtilCompress.decode(MiniPlayer.class, bytes);
                    RoleInfo roleInfo = new RoleInfo(miniPlayer);
                    roleInfo.isActive = isActive;
                    rst.add(roleInfo);
                }
            }
        }
        return rst;
    }


    /**
     * 根据账号类型创建LoginCheck对象
     */
    private LoginCheck createLoginCheck(int loginCheckType, List<String> params, String version) {
        LoginCheck check = null;
        switch (loginCheckType) {
            case LoginCheckType.ACCOUNT_TYPE_GY: {
                check = new LoginCheckGY(params, version);
                break;
            }
            case LoginCheckType.ACCOUNT_TYPE_OASIS: {
                check = new LoginCheckOasisLocal(params, version);
                break;
            }
            case LoginCheckType.ACCOUNT_TYPE_JG: {
                check = new LoginCheckJG(params, version);
                break;
            }
            case LoginCheckType.ACCOUNT_TYPE_YIJIE: {
                check = new LoginCheckYiJie(params, version);
                break;
            }
            case LoginCheckType.ACCOUNT_TYPE_SHOUQU_IOS:
            case LoginCheckType.ACCOUNT_TYPE_SHOUQU2_IOS:
            case LoginCheckType.ACCOUNT_TYPE_SHOUQU: {
                check = new LoginCheckShouqu(params, version);
                break;
            }
            case LoginCheckType.ACCOUNT_TYPE_MAOER: {
                check = new LoginCheckMaoer(params, version);
                break;
            }
            case LoginCheckType.ACCOUNT_TYPE_6KW: {
                check = new LoginCheck6KW(params, version);
                break;
            }
            case LoginCheckType.ACCOUNT_TYPE_OPPO: {
                check = new LoginCheckOppo(params, version);
                break;
            }
            case LoginCheckType.ACCOUNT_TYPE_HUAWEI: {
                check = new LoginCheckHuaWei(params, version);
                break;
            }
            case LoginCheckType.ACCOUNT_TYPE_6KW_IOS:
            case LoginCheckType.ACCOUNT_TYPE_XIONGMAOWAN: {
                check = new LoginCheckXiongmaowan(params, version);
                break;
            }
            case LoginCheckType.ACCOUNT_TYPE_PINRUI2_IOS:
            case LoginCheckType.ACCOUNT_TYPE_PINRUI_IOS: {
                check = new LoginCheckPinrui(params, version);
                break;
            }
            case LoginCheckType.ACCOUNT_TYPE_DOUYOU_IOS:
            case LoginCheckType.ACCOUNT_TYPE_DOUYOU_AND:
            case LoginCheckType.ACCOUNT_TYPE_DOUYOU_IOS2:
            case LoginCheckType.ACCOUNT_TYPE_DOUYOU: {
                check = new LoginCheckDouYou(params, version);
                break;
            }
            case LoginCheckType.ACCOUNT_TYPE_CHANGYOU: {
                check = new LoginChangYou(params, version);
                break;
            }
            case LoginCheckType.ACCOUNT_TYPE_PINRUI: {
                check = new LoginCheckPinruiAnd(params, version);
                break;
            }
            case LoginCheckType.ACCOUNT_TYPE_OASIS_52x: {
                check = new LoginCheckOasis52x(params, version);
                break;
            }
        }
        if (check != null) {//设置账号登录类型
            check.setLoginCheckType(loginCheckType);
        }
        return check;
    }

    /**
     * 登录日志
     */
    private void logInfo(int accountType, String ip, String accountId, String loginResult, List<String> params, int roleCount) {
        StringBuilder sb = new StringBuilder();
        sb.append(accountType).append("|").append(ip).append("|").append(accountId).append("|").append(loginResult);
        for (String param : params) {
            sb.append("|").append(param);
        }
        sb.append("|").append(roleCount);
        logger.info(sb.toString());
    }

}
