package com.gy.server.servlet.login.check.oasis;

import java.util.Base64;
import java.util.List;

import javax.servlet.http.HttpServletRequest;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTVerificationException;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.google.gson.JsonObject;
import com.gy.server.assistant.login.LoginCheckType;
import com.gy.server.servlet.login.check.LoginCheck;
import com.gy.server.sdk.SDKParam;
import com.gy.server.utils.JsonUtil;

/**
 * 绿洲账号登录校验（本地解析验证） - 初始版本5.2.x
 */
public class LoginCheckOasis52x extends LoginCheck {

    private static JWTVerifier verifier;

    static {
        try {
            String secret = SDKParam.getAccountParam(LoginCheckType.ACCOUNT_TYPE_OASIS, 2);
            Algorithm algorithm = Algorithm.HMAC256(secret);
            verifier = JWT.require(algorithm).build();
        } catch (IllegalArgumentException e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }
    }

    private String token;

    public LoginCheckOasis52x(List<String> params, String version) {
        super(params, version);
        this.token = loginCheckParams.get(0);
    }


    @Override
    public void check(HttpServletRequest request) {
        try {
            DecodedJWT jwt = verifier.verify(token);
            String tokenParams = new String(Base64.getUrlDecoder().decode(jwt.getPayload()));
            JsonObject jsonObject = JsonUtil.getJsonObject(tokenParams);
            accountId = jsonObject.get("playerId").getAsString();
            if (accountId != null) {
                this.result = LoginCheck.RESULT_SUCCESS;
            }
        } catch (JWTVerificationException e) {
            logger.catching(e);
            this.result = LoginCheck.RESULT_BUSY;//token不正确
        }
    }

    @Override
    protected String getUrl(HttpServletRequest request) {
        return null;
    }

    @Override
    protected boolean isPostRequest() {
        return false;
    }

    @Override
    protected String getPostData() {
        return null;
    }

    @Override
    protected void unPack(String text) {

    }
}
