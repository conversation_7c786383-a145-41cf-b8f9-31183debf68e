package com.gy.server.servlet.login;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.google.gson.reflect.TypeToken;
import com.gy.server.Configuration;
import com.gy.server.assistant.login.LoginAssistant;
import com.gy.server.assistant.login.LoginCheckData;
import com.gy.server.assistant.login.LoginCheckType;
import com.gy.server.assistant.login.LoginRecord;
import com.gy.server.message.DefaultMessage;
import com.gy.server.utils.CharsetEncoding;
import com.gy.server.utils.CollectionUtil;
import com.gy.server.utils.JsonUtil;
import com.gy.server.utils.time.DateTimeUtil;

import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR> - [Created on 2018/3/29 11:59]
 */
@WebServlet(name = "LoginCheckServlet", urlPatterns = "/login_check")
public class LoginCheckServlet extends HttpServlet {

    public static long TOKEN_CD = 4 * DateTimeUtil.MillisOfHour;

    private final static int CHECK_STATUS_SUCCESS = 0;
    private final static int CHECK_STATUS_NOT_EXIST = 1;
    private final static int CHECK_STATUS_OVERDUE = 2;
    private final static int CHECK_STATUS_BUSY = 3;
    private final static int CHECK_STATUS_ERROR = 4;

    private static final String RES_PARAM_ACCOUNT_ID = "accountId";
    private static final String RES_PARAM_ACCOUNT_TYPE = "accountType";
    private static final String RES_PARAM_STATUS = "status";
    private static final String RES_PARAM_MESSAGE = "msg";

    private static Logger logger = LogManager.getLogger(LoginCheckServlet.class);


    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        request.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);
        response.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);

        List<Map<String, Object>> resultList = new ArrayList<>();

        String dataParam = request.getParameter("data");
        List<LoginCheckData> dataList = JsonUtil.json2Collection(dataParam, new TypeToken<List<LoginCheckData>>() {
        }.getType());

        if (CollectionUtil.isNotEmpty(dataList)) {

            Map<Integer, Map<String, LoginCheckData>> dataMap = new HashMap<>();
            for (LoginCheckData data : dataList) {
                if (LoginAssistant.calculateLoginCheckTypeByAccountType(data.accountType) == LoginCheckType.ACCOUNT_TYPE_GUEST) {
                    //测试渠道，直接返回成功
                    Map<String, Object> loginRecordMap = new HashMap<>();
                    if(Configuration.isTest()) {
                        loginRecordMap.put(RES_PARAM_ACCOUNT_ID, data.accountId);
                        loginRecordMap.put(RES_PARAM_ACCOUNT_TYPE, data.accountType);
                        loginRecordMap.put(RES_PARAM_STATUS, CHECK_STATUS_SUCCESS);
                        loginRecordMap.put(RES_PARAM_MESSAGE, DefaultMessage.login_check_success.getText());
                    }else{
                        loginRecordMap.put(RES_PARAM_ACCOUNT_ID, data.accountId);
                        loginRecordMap.put(RES_PARAM_ACCOUNT_TYPE, data.accountType);
                        loginRecordMap.put(RES_PARAM_STATUS, CHECK_STATUS_NOT_EXIST);
                        loginRecordMap.put(RES_PARAM_MESSAGE, DefaultMessage.login_check_token_error.getText());
                        System.err.println("Guest account type cannot be used in live mode.");
                    }
                    resultList.add(loginRecordMap);
                } else {
                    dataMap.computeIfAbsent(data.accountType, integer -> new HashMap<>()).put(data.accountId, data);
                }
            }

            if (CollectionUtil.isNotEmpty(dataMap)) {

                for (Map.Entry<Integer, Map<String, LoginCheckData>> entry : dataMap.entrySet()) {
                    int accountType = entry.getKey();
                    Map<String, LoginCheckData> accountTypeDataMap = entry.getValue();

                    try {
                        if (CollectionUtil.isNotEmpty(accountTypeDataMap)) {
                            List<LoginRecord> loginRecords = LoginAssistant.getLoginRecordsByAccountTypeAndAccountIds(accountType, accountTypeDataMap.keySet());
                            for (LoginRecord loginRecord : loginRecords) {

                                Map<String, Object> loginRecordMap = new HashMap<>();
                                loginRecordMap.put(RES_PARAM_ACCOUNT_ID, loginRecord.getAccountId());
                                loginRecordMap.put(RES_PARAM_ACCOUNT_TYPE, loginRecord.getAccountType());
                                LoginCheckData loginCheckData = accountTypeDataMap.get(loginRecord.getAccountId());

                                if (!StringUtils.equals(loginCheckData.token, loginRecord.getLoginToken())) {
                                    loginRecordMap.put(RES_PARAM_STATUS, CHECK_STATUS_ERROR);
                                    loginRecordMap.put(RES_PARAM_MESSAGE, DefaultMessage.login_check_token_error.getText());
                                } else if (loginRecord.getUpdateTime().getTime() + TOKEN_CD < System.currentTimeMillis()) {
                                    loginRecordMap.put(RES_PARAM_STATUS, CHECK_STATUS_OVERDUE);
                                    loginRecordMap.put(RES_PARAM_MESSAGE, DefaultMessage.login_check_token_overdue.getText());
                                } else {
                                    loginRecordMap.put(RES_PARAM_STATUS, CHECK_STATUS_SUCCESS);
                                    loginRecordMap.put(RES_PARAM_MESSAGE, DefaultMessage.login_check_success.getText());
                                }
                                resultList.add(loginRecordMap);
                                dataList.remove(loginCheckData);
                            }
                        }
                    } catch (Exception e) {
                        logger.catching(e);
                    }
                }

                if (CollectionUtil.isNotEmpty(dataList)) {
                    for (LoginCheckData loginCheckData : dataList) {
                        Map<String, Object> loginRecordMap = new HashMap<>();
                        loginRecordMap.put(RES_PARAM_ACCOUNT_ID, loginCheckData.accountId);
                        loginRecordMap.put(RES_PARAM_ACCOUNT_TYPE, loginCheckData.accountType);
                        loginRecordMap.put(RES_PARAM_STATUS, CHECK_STATUS_BUSY);
                        loginRecordMap.put(RES_PARAM_MESSAGE, DefaultMessage.server_error.getText());
                        resultList.add(loginRecordMap);
                    }
                    dataList.clear();
                }
            }
        }

        response.getWriter().write(JsonUtil.list2Json(resultList));

    }
}
