package com.gy.server.servlet;

import com.gy.server.utils.CharsetEncoding;
import com.gy.server.utils.JsonUtil;
import com.gy.server.utils.Md5Util;
import com.gy.server.utils.compressor.CompressorUtil;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

/**
 * <AUTHOR> - [Created on 2025/1/20 10:06]
 **/

public abstract class Md5RequestHttpServlet extends HttpServlet {

	private static final String MD5Key = "ttlike_md5";
	private static final String ErrorKey = "error";

	protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
		doPost(request, response);
	}

	@Override
	protected final void doPost(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
		req.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);
		resp.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);
		String compress = req.getParameter("ttlike_compress");
		boolean isCompress = false;
		if (compress!= null && compress.equals("1")) {
			isCompress = true;
		}

		if (req.getParameterMap().isEmpty() || StringUtils.isEmpty(req.getParameter(MD5Key)) || checkMd5Success(req)) {
			Map<Object, Object> res = doPost0(req, resp);
			respWriteMd5Data(resp, res, 1, isCompress);
		} else {
			Map<Object, Object> res = new HashMap<>();
			if (StringUtils.isNotEmpty(req.getParameter(MD5Key))) {
				respWriteMd5Data(resp, res, -1, isCompress);
			} else {
				respWriteMd5Data(resp, res, -2, isCompress);
			}
		}
    }

	/**
	 * 校验客户端 md5
	 */
	private boolean checkMd5Success(HttpServletRequest request) {
		Map<String, String[]> params = request.getParameterMap();
		// 使用TreeMap来排序参数
		TreeMap<String, String> sortedParams = new TreeMap<>();
		for (Map.Entry<String, String[]> entry : params.entrySet()) {
			// 假设参数只有一个值，多值参数需要额外处理
			sortedParams.put(entry.getKey(), entry.getValue()[0]);
		}
		// 拼接所有参数，忽略空值和签名参数
		StringBuilder sb = new StringBuilder();
		for (Map.Entry<String, String> entry : sortedParams.entrySet()) {
			if (!entry.getKey().equals(MD5Key) && entry.getValue() != null && !entry.getValue().isEmpty()) {
				sb.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
			}
		}
		String paramStr = "";
		if (sb.length() > 0) {
			paramStr = sb.substring(0, sb.length() - 1);
		}
		String myMd5 = "";
		try {
			myMd5 = Md5Util.MD5(paramStr);
		} catch (NoSuchAlgorithmException e) {
			throw new RuntimeException(e);
		}
		if (!myMd5.equalsIgnoreCase(request.getParameter(MD5Key))) {
			System.err.println("=======MD5Param : " + paramStr);
			System.err.println("=======MyMD5 : " + myMd5);
			System.err.println("=======CLIENT_MD5 : " + request.getParameter(MD5Key));
			return false;
		}

		return true;
	}

    public abstract Map<Object, Object> doPost0(HttpServletRequest req, HttpServletResponse resp)
            throws ServletException, IOException;

	private static void respWriteMd5Data(HttpServletResponse response, Map<Object, Object> res, int error, boolean isCompress) throws IOException {
		String str = JsonUtil.map2Json(res);
		String md5 = "";
		try {
			md5 = Md5Util.MD5(str);
		} catch (NoSuchAlgorithmException e) {
			throw new RuntimeException(e);
		}
		Map<Object, Object> result = new HashMap<>();
		result.put(MD5Key, md5);
		result.put("data", str);
		result.put(ErrorKey, error);
		String rst = JsonUtil.map2Json(result);
		if (isCompress) {
			response.setContentType("application/octet-stream");
			byte[] compress = CompressorUtil.deflate().compress(rst.getBytes(StandardCharsets.UTF_8));
			response.getOutputStream().write(compress);
		} else {
			response.getWriter().write(rst);
		}

	}

}
