package com.gy.server.servlet.ban;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

import com.google.gson.JsonObject;
import com.gy.server.utils.JsonUtil;
import com.gy.server.utils.Md5Util;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.redis.key.BaseRedisKey;
import com.ttlike.server.tl.baselib.serialize.ban.BanChat;
import com.ttlike.server.tl.baselib.serialize.ban.BanManager;
import com.ttlike.server.tl.baselib.serialize.player.MiniPlayer;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * @program: xhx-billing
 * @description: 畅游接入的网易护盾
 * @author: Huang.Xia
 * @create: 2021-03-09 11:24
 **/
@WebServlet(name="CyouNeteasyBanServlet",urlPatterns = "/neteasy_ban")
public class CyouNeteasyBanServlet extends HttpServlet {

    private static final String key = "WMfEU8lrZn45Z3HF";
    private static Logger logger = LogManager.getLogger(CyouNeteasyBanServlet.class);

    @Override
    protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        String errorInfo = null;
        Map<String,Object> result = new HashMap<>();
        result.put("result",1);

        try{
            req.setCharacterEncoding(StandardCharsets.UTF_8.name());
            resp.setCharacterEncoding(StandardCharsets.UTF_8.name());

            String cmd = req.getParameter("id");
            String data = req.getParameter("data");
            String sign = req.getParameter("sign");

            logger.info("cmd={}, data={}, sign={}",cmd,data,sign);

            String newSign = Md5Util.MD5((key+data).getBytes(StandardCharsets.UTF_8));

            if(newSign.equalsIgnoreCase(sign)) {

                JsonObject json = JsonUtil.getJsonObject(data);
                if (json != null && Integer.parseInt(cmd) == 4149) {
                    String banReson = json.get("BanReason").getAsString();
                    int banTime = json.get("BanTime").getAsInt();
                    long playerId = json.get("RoleId").getAsLong();

                    String miniPlayerRedisKey = BaseRedisKey.Commons.MINI_PLAYER.getRedisKey(playerId);
                    Map<String, MiniPlayer> miniPlayers = TLBase.getInstance().getRedisAssistant().mget(10, MiniPlayer.class, miniPlayerRedisKey);
                    MiniPlayer miniPlayer = miniPlayers.get(miniPlayerRedisKey);
                    if (miniPlayer != null) {

                        BanChat banChat = new BanChat(playerId, banTime / 60, banReson, miniPlayer.getAccountType(), miniPlayer.getAccountId(), miniPlayer.getName(),miniPlayer.getSdkPayChannel(),miniPlayer.getAccountAdChannel());
                        BanManager.createBanChat(banChat);

                        result.put("result", 0);
                        result.put("RoleId", playerId);
                        result.put("BanReason", banReson);
                    } else {
                        errorInfo = "不存在的角色id：" + playerId;
                    }
                } else {
                    errorInfo = "不能处理的指令： " + cmd + " data: " + data;
                }
            }else{
                errorInfo = "sign miss match";
            }
        }catch (Exception e){
            e.printStackTrace();
            errorInfo = e.toString();
        }

        if(errorInfo != null){
            result.put("can not parse request: ", "errorInfo" + errorInfo);
        }
        String rst = JsonUtil.map2Json(result);
        resp.getWriter().write(rst);
    }

    @Override
    protected void doPost(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        doGet(req, resp);
    }
}
