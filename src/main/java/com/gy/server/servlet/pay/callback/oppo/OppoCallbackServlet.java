package com.gy.server.servlet.pay.callback.oppo;

import java.io.IOException;
import java.net.URLDecoder;
import java.security.KeyFactory;
import java.security.PublicKey;
import java.security.Signature;
import java.security.spec.X509EncodedKeySpec;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.gy.server.assistant.pay.PayAssistant;
import com.gy.server.assistant.pay.Receipt;
import com.gy.server.sdk.SDKParam;
import com.gy.server.servlet.pay.callback.PayCallbackServlet;
import com.gy.server.utils.LockUtil;
import com.gy.server.utils.CharsetEncoding;

import org.apache.commons.codec.binary.Base64;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * oppo充值回调接口
 *
 * <AUTHOR>
 * @date 2019年9月16日 下午4:56:45
 */
@WebServlet(name = "OppoCallbackServlet", urlPatterns = "/callback_oppo")
public class OppoCallbackServlet extends PayCallbackServlet {

    private static final String RESULT_SUCCESS = "result=OK&resultMsg=成功";
    private static final String RESULT_SIGN_MISS_MATCH = "result=FAIL&resultMsg=sign error";
    private static final String RESULT_RECEIPT_EXIST = "result=FAIL&resultMsg=receipt exist";
    private static final String RESULT_FAIL = "result=FAIL&resultMsg=error";

    private static Logger logger = LogManager.getLogger(OppoCallbackServlet.class);

    @Override
    protected void callback(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        request.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);
        response.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);

        String result = RESULT_SUCCESS;
        try {
            //oppo订单号
            String orderId = request.getParameter("notifyId");
            String btOrderId = request.getParameter("partnerOrder");
            String productName = request.getParameter("productName");
            String productDesc = request.getParameter("productDesc");
            int payMoney = Integer.valueOf(request.getParameter("price"));
            int count = Integer.valueOf(request.getParameter("count"));
            String exInfo = URLDecoder.decode(request.getParameter("attach"), "UTF-8");
            String sign = request.getParameter("sign");

            //附加参数： 内部订单号$渠道$内部商品id
            String[] extArray = exInfo.split("\\$");
            String channel = extArray[0];
            long playerId = Long.valueOf(extArray[1]);
            String accountId = extArray[2];
            int goodsId = Integer.valueOf(extArray[3]);
            String coGoodsId = extArray[4];
            int serverNum = Integer.valueOf(extArray[5]);


            String baseString = getBaseString(orderId, btOrderId, productName, productDesc, payMoney, count, exInfo);
            String key = SDKParam.getPayParam(channel, 0);
            if (!doCheck(baseString, sign, key)) {
                //签名不匹配
                result = RESULT_SIGN_MISS_MATCH;
            } else {
                synchronized (LockUtil.getLockKey(btOrderId)) {
                    Receipt receipt = PayAssistant.getReceiptByOrderId(btOrderId);
                    if (receipt != null) {
                        //订单已存在
                        result = RESULT_RECEIPT_EXIST;
                    } else {
                        receipt = new Receipt(btOrderId, orderId, playerId, accountId, channel, goodsId, coGoodsId, payMoney, serverNum);
                        updateReceipt(receipt);
                    }
                }
            }

        }catch (Exception e) {
            logger.catching(e);
            result = RESULT_FAIL;
        }

        response.getWriter().write(String.valueOf(result));
    }

    /**
     * 构建基串
     */
    private String getBaseString(String notifyId, String partnerOrder, String productName
            , String productDesc, int price, int count, String attach) {
        StringBuilder sb = new StringBuilder();
        sb.append("notifyId=").append(notifyId);
        sb.append("&partnerOrder=").append(partnerOrder);
        sb.append("&productName=").append(productName);
        sb.append("&productDesc=").append(productDesc);
        sb.append("&price=").append(price);
        sb.append("&count=").append(count);
        sb.append("&attach=").append(attach);
        return sb.toString();
    }

    /**
     * 校验签名
     */
    public boolean doCheck(String content, String sign, String publicKey) throws Exception {
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        byte[] encodedKey = Base64.decodeBase64(publicKey);
        PublicKey pubKey = keyFactory.generatePublic(new X509EncodedKeySpec(encodedKey));

        Signature signature = Signature.getInstance("SHA1WithRSA");

        signature.initVerify(pubKey);
        signature.update(content.getBytes("UTF-8"));
        try {
            boolean bverify = signature.verify(Base64.decodeBase64(sign));
            return bverify;
        } catch (Exception e) {
            logger.catching(e);
            return false;
        }
    }

}
