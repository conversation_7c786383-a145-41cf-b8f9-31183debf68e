package com.gy.server.servlet.pay.pull;

import com.google.common.eventbus.Subscribe;
import com.google.gson.JsonObject;
import com.google.gson.reflect.TypeToken;
import com.gy.server.Configuration;
import com.gy.server.assistant.login.LoginCheckType;
import com.gy.server.assistant.pay.PayAssistant;
import com.gy.server.assistant.pay.Receipt;
import com.gy.server.db.relation.hibernate.HibernateUtil;
import com.gy.server.sdk.SDKParam;
import com.gy.server.servlet.login.check.changyou.LoginChangYou;
import com.gy.server.servlet.pay.callback.changyou.ChangYouCallBackServlet;
import com.gy.server.utils.CharsetEncoding;
import com.gy.server.utils.CollectionUtil;
import com.gy.server.utils.JsonUtil;
import com.gy.server.utils.net.HttpUtil;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;

/**
 * 支付信息拉取反馈
 * <AUTHOR> - [Created on 2018/6/1 13:46]
 */
@WebServlet(name = "PayPullFeedbackServlet", urlPatterns = "/pay_pull_feedback")
public class PayPullFeedbackServlet extends HttpServlet {

    public static final Set<String> CHANGEYOU_ABOARD_CHANNEL_LIST = new HashSet<String>() {{
        add("2002");//appstore
        add("4002");//google play
        add("7002");//mycard
        add("9002");//海外私域-网页商城，iOS、googlePlay、官网包都可以通过网页商城购买
        add("5005");//官方独享渠道
        add("1");//海外-广告渠道标识:ironSource
        add("2");//海外-广告渠道标识:tapjoy
    }};

    private static Logger logger = LogManager.getLogger(PayPullFeedbackServlet.class);

    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        request.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);
        response.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);

        try {
            Set<String> orderIds = JsonUtil.json2Collection(request.getParameter("orderIds"), new TypeToken<Set<String>>() {
            }.getType());

            if (CollectionUtil.isNotEmpty(orderIds)) {
                for (String orderId : orderIds) {
                    try {
                        Receipt receipt = PayAssistant.getReceiptByOrderId(orderId);
                        if (receipt != null) {
                            //更新状态、时间，保存
                            receipt.setStatus(Receipt.Status.finish);
                            receipt.setUpdateTime(new Date());
                            HibernateUtil.update(receipt);

                            //完成处理
                            if (receipt.getExtendText() != null && CHANGEYOU_ABOARD_CHANNEL_LIST.contains(receipt.getExtendText().trim())) {
                                updateChangYouAbroadReceipt(receipt.getCoOrderId(), receipt.getExtendText());
                            } else {
                                updateChangYouReceipt(receipt.getCoOrderId(), receipt.getExtendText());
                            }
                            receiptFinish(receipt);
                        }
                    } catch (Exception e) {
                        logger.error("PayPull feedback error, orderId : " + orderId, e);
                    }
                }
            }

        } catch (Exception e) {
            logger.catching(e);
        }

        response.getWriter().write("success");
    }

    /**
     * 订单完成后处理，子类可实现
     */
    protected void receiptFinish(Receipt receipt) {

    }

    private static final Random random = new Random();
    private static String tag = String.valueOf(random.nextInt(10000000));

    /**
     * 更新畅游订单
     */
    public static boolean updateChangYouReceipt(String cyOrderId, String channelId) throws Exception {
        Set<String> cyOrderIds = new HashSet<>();
        cyOrderIds.add(cyOrderId);
        return updateChangYouReceipt(cyOrderIds, channelId);
    }

    public static boolean updateChangYouReceipt(Set<String> cyOrderId, String channelId) throws Exception {
        //构建data数据
        Map<String, Object> dataMap = new HashMap<>();
        List<String> orderList = new ArrayList<>();
        Map<String, Object> orderListMap = new HashMap<>();
        for (String orderId : cyOrderId) {
            orderListMap.clear();
            orderListMap.put("orderedId", orderId);
            orderList.add(JsonUtil.map2Json(orderListMap));
        }
        dataMap.put("orders", orderList);
        String data = JsonUtil.map2Json(dataMap);
        //验证订单地址
        String update_pay_url = ChangYouCallBackServlet.getUrl();
        //第三方sdk参数
        String appKey = SDKParam.getAccountParam(LoginCheckType.ACCOUNT_TYPE_CHANGYOU, 0);
        String appSecret = SDKParam.getAccountParam(LoginCheckType.ACCOUNT_TYPE_CHANGYOU, 1);
        //构建消息头
        List<Pair<String, String>> requestPropertiesParam = new ArrayList<>();
        requestPropertiesParam.add(Pair.of("appkey", appKey));
        //opcode+data+appkey +appsecret+tag+channelId
        String sb = "5004" + data + appKey + appSecret + tag + channelId;
        requestPropertiesParam.add(Pair.of("sign", LoginChangYou.createSign(sb)));
        requestPropertiesParam.add(Pair.of("tag", tag));
        requestPropertiesParam.add(Pair.of("opcode", "5004"));
        requestPropertiesParam.add(Pair.of("channelId", channelId));
        data = "data=" + data;
        //请求验证
        String resultText = HttpUtil.requestHttpWithPostReturnString(update_pay_url, data.getBytes(), requestPropertiesParam);
        //处理验证结果
        JsonObject resultJson = JsonUtil.getJsonObject(resultText);
        int state = resultJson.get("state").getAsInt();
        return state == 200 && resultJson.get("data").getAsString().equals("0");
    }

    /**
     * 更新畅游海外订单
     */
    public static boolean updateChangYouAbroadReceipt(String cyOrderId, String channelId) throws Exception {
        Set<String> cyOrderIds = new HashSet<>();
        cyOrderIds.add(cyOrderId);
        return updateChangYouAbroadReceipt(cyOrderIds, channelId);
    }

    public static boolean updateChangYouAbroadReceipt(Set<String> cyOrderId, String channelId) throws Exception {
        //构建data数据
        Map<String, Object> dataMap = new HashMap<>();
        List<String> orderList = new ArrayList<>();
        Map<String, Object> orderListMap = new HashMap<>();
        for (String orderId : cyOrderId) {
            orderListMap.clear();
            orderListMap.put("orderedId", orderId);
            orderList.add(JsonUtil.map2Json(orderListMap));
        }
        dataMap.put("orders", orderList);

        //是否是广告奖励订单
        boolean isAd = channelId.equals("1") || channelId.equals("2");
        if (isAd) {
            dataMap.put("ad_flag", true);
        }

        String data = JsonUtil.map2Json(dataMap);
        //验证订单地址
        String update_pay_url = getUrl();
        //第三方sdk参数
        String appKey = SDKParam.getAccountParam(LoginCheckType.ACCOUNT_TYPE_CHANGYOU_ABROAD, 0);
        String appSecret = SDKParam.getAccountParam(LoginCheckType.ACCOUNT_TYPE_CHANGYOU_ABROAD, 1);
        //构建消息头
        List<Pair<String, String>> requestPropertiesParam = new ArrayList<>();
        //构建消息头
        requestPropertiesParam.add(Pair.of("app_key", appKey));
        //数字签名Sign：data + appKey + appSecret + channelId
        String sb = data + appKey + appSecret + channelId;
        requestPropertiesParam.add(Pair.of("sign", LoginChangYou.createSign(sb)));
        requestPropertiesParam.add(Pair.of("channel_id", channelId));

        data = "data=" + data;
        //请求验证
        logger.error("PayPullFeedbackServlet =============>  updateChangYouAbroadReceipt start");
        String resultText = HttpUtil.requestHttpWithPostReturnString(update_pay_url, data.getBytes(), requestPropertiesParam);

        //处理验证结果
        JsonObject resultJson = JsonUtil.getJsonObject(resultText);
        int state = resultJson.get("status").getAsInt();
        logger.error("PayPullFeedbackServlet =============>  updateChangYouAbroadReceipt end. result state=" + state + " message=" + resultJson.get("message").getAsString());
        return state == 0;
    }

    public static String getUrl() {
        //线上测试
        String test_url = "https://tnsdk.gaming.com/billing/cyou/gameserver/orderComplet.json";
        //线上正式
        String live_url = "https://nsdk.gaming.com/billing/cyou/gameserver/orderComplet.json";

        return Configuration.isTest() ? test_url : live_url;
    }
}
