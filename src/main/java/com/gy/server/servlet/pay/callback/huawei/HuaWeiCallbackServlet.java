package com.gy.server.servlet.pay.callback.huawei;

import java.io.IOException;
import java.math.BigDecimal;
import java.security.KeyFactory;
import java.security.PublicKey;
import java.security.spec.X509EncodedKeySpec;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.gy.server.assistant.pay.PayAssistant;
import com.gy.server.assistant.pay.Receipt;
import com.gy.server.sdk.SDKParam;
import com.gy.server.servlet.pay.callback.PayCallbackServlet;
import com.gy.server.utils.LockUtil;
import com.gy.server.utils.CollectionUtil;
import com.gy.server.utils.JsonUtil;

import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR> - [Created on 2019/9/23 17:23]
 */
@WebServlet(name = "HuaWeiCallbackServlet", urlPatterns = "/callback_huawei")
public class HuaWeiCallbackServlet extends PayCallbackServlet {

    private static final int RESULT_SUCCESS = 0;
    private static final int RESULT_SIGN_MISS_MATCH = 1;
    private static final int RESULT_PAY_ERROR = 97;
    private static final int RESULT_PARAM_ERROR = 98;
    private static final int RESULT_OTHER_ERROR = 99;

    public static final String SIGN_ALGORITHMS = "SHA1WithRSA";
    public static final String SIGN_ALGORITHMS256 = "SHA256WithRSA";
    public static final Base64.Decoder decoder = Base64.getDecoder();

    private static Logger logger = LogManager.getLogger(HuaWeiCallbackServlet.class);

    @SuppressWarnings("Duplicates")
    @Override
    protected void callback(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

        int result = RESULT_SUCCESS;
        try {
            Map<String, Object> map = getValue(request);
            if (CollectionUtil.isEmpty(map)) {
                result = RESULT_PARAM_ERROR;
            } else {
                String resultCode = (String) map.get("result");
                if (!StringUtils.equals(resultCode, "0")) {
                    result = RESULT_PAY_ERROR;
                } else {
                    String sign = (String) map.get("sign");
                    String amount = (String) map.get("amount");
                    String coOrderId = (String) map.get("orderId");
                    String orderId = (String) map.get("requestId");
                    //附加参数： 渠道$内部商品id
                    String ext = (String) map.get("extReserved");
                    String[] extArray = ext.split("\\$");
                    String channel = extArray[0];
                    int goodsId = Integer.parseInt(extArray[1]);
                    long playerId = Long.parseLong(extArray[2]);
                    String accountId = extArray[3];
                    String coGoodsId = extArray[4];
                    int serverNum = Integer.parseInt(extArray[5]);

                    //配置参数
                    String key = SDKParam.getPayParam(channel, 0);
                    //调用验签rsaDoCheck函数，验签返回Boolean值。True为验签成功，False为验签失败。
                    if (!rsaDoCheck(map, sign, key, (String) map.get("signType"))) {
                        result = RESULT_SIGN_MISS_MATCH;
                    } else {
                        synchronized (LockUtil.getLockKey(orderId)) {
                            Receipt receipt = PayAssistant.getReceiptByOrderId(orderId);
                            if (receipt != null) {
                                //订单已存在
                                result = RESULT_SUCCESS;
                            } else {
                                BigDecimal decimal = new BigDecimal(amount);
                                int price = decimal.multiply(new BigDecimal(100)).intValue();
                                receipt = new Receipt(orderId, coOrderId, playerId, accountId, channel, goodsId, coGoodsId, price, serverNum);
                                updateReceipt(receipt);
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.catching(e);
            result = RESULT_OTHER_ERROR;
        }

        ResultDomain resultDomain = new ResultDomain();
        resultDomain.setResult(result);
        response.setContentType("application/json");
        response.getWriter().write(JsonUtil.obj2Json(resultDomain));
    }

    public Map<String, Object> getValue(HttpServletRequest request) {
        Map<String, Object> valueMap = new HashMap<String, Object>();

        Enumeration<String> itx = request.getParameterNames();
        while (itx.hasMoreElements()) {
            String key = itx.nextElement();
            valueMap.put(key, request.getParameter(key));
        }
        return valueMap;
    }

    /**
     * @param params    回调中实际携带的字段参数解析后的map集合
     * @param sign      回调中签名sign字段
     * @param publicKey 支付公钥
     * @param signtype  签名算法
     */
    public static boolean rsaDoCheck(Map<String, Object> params, String sign, String publicKey, String signtype) {
        //content是去除map中的sign以及signtype字段后的的排序拼接。
        String content = getSignData(params);
        return doCheck(content, sign, publicKey, signtype);
    }

    /**
     * @param params 实际回调中的字段map集合
     * @return 去除sign和signType字段，以升序排序 key1=value1&key2=value2&.....
     */
    public static String getSignData(Map<String, Object> params) {
        StringBuffer content = new StringBuffer();
        List<String> keys = new ArrayList<String>(params.keySet());
        Collections.sort(keys);
        for (int i = 0; i < keys.size(); i++) {
            String key = (String) keys.get(i);
            if ("sign".equals(key) || "signType".equals(key)) {
                continue;
            }
            String value = (String) params.get(key);
            if (value != null) {
                content.append((i == 0 ? "" : "&") + key + "=" + value);
            } else {
                content.append((i == 0 ? "" : "&") + key + "=");
            }
        }
        return content.toString();
    }

    /**
     * doCheck函数各个字段参数示例参考：
     *
     * @param content  验签拼接
     *                 示例：accessMode=0&amount=10.00&bankId=AliPay&extReserved=11&notifyTime=*************&orderId=A3b6a*******c1312dee66b32e085692&orderTime=2019-04-08
     *                 22:18:48&payType=4&productName=1000元宝&requestId=832*******592&result=0&spending=0.00&tradeTime=2019-04-08
     *                 22:18:49&userName=8008******32
     * @param signtype sigtype=rsa256 时：SIGN_ALGORITHMS256 = "SHA256WithRSA", 其他情况都使用：
     *                 SIGN_ALGORITHMS = "SHA1WithRSA"
     * @param sign     回调中的sign字段，示例：Xmc+JExSPFr9FUjd4IPKtRAkkkOTm0oXCFNwlvXdQJHbhB0prD8ycpw0Wdr6enKLUgHBKB8JQ9s7VB9wMcgRXOJ6iArn0NqTTTl9Z5gP7zGBGDVH8J22CRObd8JKeQ9YiJ2SUM+r2H+jQdMpj3n3Cp47Z4rGR3kUDDIRCOEhSwHAqwzdeFJg2utEJfuYQIhseM7/hxB/H1ssoe9SaIAmKfjuZgYS+VzsUBjCiTmd9jKdpq0kTCEas1lL8MZVxkZ91g+zd2PH0ajgYkU8wvxRZgLRWw4h28DrKD7ziQKHOt92ZZjvcej+maSSDeQtyp7DhFgH4T+2uHy/c3Kf33kUFw==
     *                 联盟后台支付服务下支付公钥 示例：
     *                 MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAxuJTzr44cJQGXT6Y09Y902AoIDhKhJv8/QSZLnG92L8yIhfLDCCjd5Xprnqxu0GirGESywin1qj6QhHbtBxr5hXz4IzhMHfiYV3TdxGcfxY7sxUYvAwOYk8700vRokdZq35lfg+oYcc9u2ztFRvhthHwH5R7bNCX0aiFP3POjhGPriRLzWAcA4p0FfWg1cqTDoGhH89SKRopdr1AKVpjOZxuOcnnN/vejgdGgUTurvypZCFCnUifcGxjtE/tWQmVv9xNQLwDACJm8JjiP2dAm3l4KViSl/j6j65kGT7WxHTxNM1jvLWzPMnPCvn/O9cWmsfAfmwaa6sIot1L5KlgNwIDAQAB
     */
    public static boolean doCheck(String content, String sign, String publicKey, String signtype) {
        try {
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            byte[] encodedKey = decoder.decode(publicKey);
            PublicKey pubKey = keyFactory.generatePublic(new X509EncodedKeySpec(encodedKey));
            java.security.Signature signature = null;
            if ("RSA256".equals(signtype)) {
                signature = java.security.Signature.getInstance(SIGN_ALGORITHMS256);
            } else {
                signature = java.security.Signature.getInstance(SIGN_ALGORITHMS);
            }
            signature.initVerify(pubKey);
            signature.update(content.getBytes("utf-8"));
            boolean bverify = signature.verify(decoder.decode(sign));
            return bverify;
        } catch (Exception e) {
            logger.catching(e);
        }
        return false;
    }

    /**
     * @ResultDomain 支付验签响应类，验签成功返回0，否则返回1.
     */
    public class ResultDomain {

        private int result;

        public int getResult() {
            return result;
        }

        public void setResult(int result) {
            this.result = result;
        }
    }
}
