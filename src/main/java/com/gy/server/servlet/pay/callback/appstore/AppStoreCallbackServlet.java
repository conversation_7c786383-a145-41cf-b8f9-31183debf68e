package com.gy.server.servlet.pay.callback.appstore;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.google.gson.reflect.TypeToken;
import com.gy.server.Configuration;
import com.gy.server.assistant.pay.PayAssistant;
import com.gy.server.assistant.pay.Receipt;
import com.gy.server.servlet.pay.callback.PayCallbackServlet;
import com.gy.server.utils.CharsetEncoding;
import com.gy.server.utils.JsonUtil;
import com.gy.server.utils.net.HttpUtil;

import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR> - [Created on 2018/6/5 11:11]
 */
@WebServlet(name = "AppStoreCallbackServlet", urlPatterns = "/callback_appstore")
public class AppStoreCallbackServlet extends PayCallbackServlet {

    private static final String CHECK_URL_TEST = "https://sandbox.itunes.apple.com/verifyReceipt";
    private static final String CHECK_URL_PRODUCTION = "https://buy.itunes.apple.com/verifyReceipt";

    private static Logger logger = LogManager.getLogger(AppStoreCallbackServlet.class);



    @Override
    protected void callback(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        String data = request.getParameter("data");

        boolean result = execute(data, Configuration.isTest() ? CHECK_URL_TEST : CHECK_URL_PRODUCTION);

        response.getWriter().write(result ? "success" : "fail");
    }

    private boolean execute(String data, String url) {
        try {

            Map<String, String> map = JsonUtil.json2Collection(data, new TypeToken<Map<String, String>>() {
            }.getType());

            String payChannel = map.getOrDefault("channel", "null");
            String orderId = map.getOrDefault("orderId", "null");
            int serverNum = Integer.parseInt(map.getOrDefault("serverNum", "0"));
            long playerId = Long.parseLong(map.getOrDefault("playerId", "0"));
            String accountId = map.getOrDefault("accountId", "null");
            int goodsId = Integer.parseInt(map.getOrDefault("goodsId", "0"));
            int price = Integer.parseInt(map.getOrDefault("price", "0"));
            String coGoodsId = map.getOrDefault("coGoodsId", "null");
            String receiptData = map.getOrDefault("receipt-data", "null");

            //校验订单信息
            String checkMessage = checkReceiptData(receiptData, url);

            if (checkMessage != null) {
                //解析校验结果
                Map<String, String> checkMessageMap = JsonUtil.json2Collection(checkMessage, new TypeToken<Map<String, String>>() {
                }.getType());

                int status = Integer.parseInt(checkMessageMap.get("status"));
                if (status == 0) {//成功
                    //解析校验结果中receipt数据
                    Map<String, String> checkMessageReceiptMap = JsonUtil.json2Collection(checkMessageMap.get("receipt"), new TypeToken<Map<String, String>>() {
                    }.getType());

                    //cancellation_date存在表示玩家已退款
                    String cancelInfo = checkMessageReceiptMap.get("cancellation_date");
                    if (StringUtils.isEmpty(cancelInfo)) {

                        //比较第三方商品id，避免玩家充值大商品，却用小商品的支付信息进行校验
                        String checkMessageCoGoodsId = checkMessageReceiptMap.get("product_id");
                        if (StringUtils.equals(coGoodsId, checkMessageCoGoodsId)) {

                            //根据第三方订单号及充值渠道获得订单数据，不存在则表示回调成功，创建订单数据，存储，等待gs拉取
                            String coOrderId = checkMessageReceiptMap.get("transaction_id");
                            Receipt receipt = PayAssistant.getReceiptByCoOrderIdAndPayChannel(coOrderId, payChannel);
                            if (receipt == null) {
                                receipt = new Receipt(orderId, coOrderId, playerId, accountId, payChannel, goodsId, coGoodsId, price, serverNum);
                                updateReceipt(receipt);

                                return true;
                            }
                        }
                    }
                } else if (status == 21007) {//沙盒订单
                    return execute(data, CHECK_URL_TEST);
                } else if (status == 21008) {//正式订单
                    return execute(data, CHECK_URL_PRODUCTION);
                }
            }
        } catch (Exception e) {
            logger.catching(e);
        }

        return false;
    }


    /**
     * 向appstore校验指定的receiptData，成功返回结果字符串，否则返回null
     */
    private String checkReceiptData(String receiptData, String url) {

        String message = null;
        try {

            //postData
            Map<String, String> postDataMap = new HashMap<>();
            postDataMap.put("receipt-data", receiptData);
            String postData = JsonUtil.map2Json(postDataMap);

            //connect
            message = HttpUtil.requestHttpWithPostReturnString(url, postData, CharsetEncoding.ENCODING_UTF_8);
        } catch (Exception e) {
            logger.catching(e);
        }
        return message;
    }
}
