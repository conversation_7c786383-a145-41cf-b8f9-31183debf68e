package com.gy.server.servlet.pay.callback.changyou;

import com.google.gson.JsonObject;
import com.gy.server.Configuration;
import com.gy.server.assistant.login.LoginCheckType;
import com.gy.server.assistant.pay.PayAssistant;
import com.gy.server.assistant.pay.Receipt;
import com.gy.server.sdk.SDKParam;
import com.gy.server.servlet.login.check.changyou.LoginChangYou;
import com.gy.server.servlet.pay.callback.PayCallbackServlet;
import com.gy.server.servlet.pay.pull.PayPullFeedbackServlet;
import com.gy.server.utils.JsonUtil;
import com.gy.server.utils.LockUtil;
import com.gy.server.utils.Md5Util;
import com.gy.server.utils.net.HttpUtil;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * <AUTHOR> -[Create on 2023/2/15]
 */
@WebServlet(name = "ChangYouAbroadCallBackServlet", urlPatterns = "/callback_changyou_abroad")
public class ChangYouAbroadCallBackServlet extends PayCallbackServlet {

    private static Logger logger = LogManager.getLogger(ChangYouAbroadCallBackServlet.class);
    //成功
    private static final String RESULT_SUCCESS = "1";
    //签名失败
    private static final String RESULT_SIGN_FAIL = "0";
    //订单重复
    private static final String RESULT_RECEIPT_EXIST = "0";

    @Override
    protected void callback(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String result = RESULT_SUCCESS;
        String resultStr = "";
        String _receipt = "_receipt is Empty";
        try {
            //请求参数
            String receipt = new String(request.getParameter("receipt").getBytes("ISO8859-1"), StandardCharsets.UTF_8);
            if (checkPay(receipt)) {
                String sign = request.getParameter("sign");
                String pushInfo = new String(request.getParameter("pushInfo").getBytes("ISO8859-1"), StandardCharsets.UTF_8);

                String signText = "pushInfo=" + pushInfo + "&receipt=" + receipt;
                String _sign = Md5Util.MD5(signText);
                if (sign.equalsIgnoreCase(_sign)) {
                    _receipt = new String(Base64.decodeBase64(receipt.getBytes(StandardCharsets.UTF_8)), StandardCharsets.UTF_8);
                    JsonObject json = JsonUtil.getJsonObject(_receipt);

                    if (json.has("isAd") && json.get("isAd").getAsBoolean()) {
                        String coOrderId = json.get("orderId").getAsString();//订单编号
                        String coGoodsId = json.get("goodsRegisterId").getAsString();//商品注册ID
                        String channelId = json.get("channelId").getAsString();//channelId
                        long playerId = json.get("roleId").getAsLong();//角色ID
                        String accountId = json.get("userid").getAsString();//userid
                        String placementName = json.get("placementName").getAsString();//placementName
                        int serverNum = json.get("areaId").getAsInt();//分区ID


                        synchronized (LockUtil.getLockKey(coOrderId)) {
                            Receipt receiptDb = PayAssistant.getReceiptByOrderId(coOrderId);
                            if (receiptDb == null) {
                                receiptDb = new Receipt(coOrderId, coOrderId, playerId, accountId, "advertisement", 0, placementName, 0, serverNum);
                                receiptDb.setExtendText(channelId);
                                updateReceipt(receiptDb);
                            } else {
                                //订单已存在
                                result = RESULT_SUCCESS;
                                if (receiptDb.getStatus() == Receipt.Status.finish) {
                                    PayPullFeedbackServlet.updateChangYouAbroadReceipt(coOrderId, channelId);
                                }
                                resultStr = "receipt existed !!";
                            }
                        }
                    } else {
                        String coOrderId = json.get("orderId").getAsString();//订单编号
                        String coGoodsId = json.get("goodsRegisterId").getAsString();//商品注册ID
                        String money = json.get("goodsPrice").getAsString();//产品实际支付价格（RMB级别为元，此参数不带引号，请用数字类型处理）
                        long playerId = json.get("roleId").getAsLong();//角色ID
                        String accountId = json.get("userid").getAsString();//userid
                        String channelId = json.get("channelId").getAsString();//channelId

                        String orderId;
                        int serverNum;
                        int goodsId;
                        String channel;//渠道

                        //9002 海外私域-网页商城，iOS、googlePlay、官网包都可以通过网页商城购买
                        if (channelId.equals("9002")) {
                            String[] extArray = pushInfo.split("\\$");
                            orderId = coOrderId;
                            serverNum = Integer.parseInt(extArray[1]);
                            goodsId = Integer.parseInt(extArray[2]);
                            channel = channelId;
                        } else {
                            //附加参数： 内部订单号$渠道$内部商品id
                            String[] extArray = pushInfo.split("\\$");
                            orderId = extArray[0];
                            serverNum = Integer.parseInt(extArray[1]);
                            goodsId = Integer.parseInt(extArray[2]);
                            channel = extArray[3];//渠道
                        }

                        synchronized (LockUtil.getLockKey(orderId)) {
                            Receipt receiptDb = PayAssistant.getReceiptByOrderId(orderId);
                            if (receiptDb == null) {
                                BigDecimal decimal = new BigDecimal(money);
                                int price = decimal.multiply(new BigDecimal(100)).intValue();
                                receiptDb = new Receipt(orderId, coOrderId, playerId, accountId, channel, goodsId, coGoodsId, price, serverNum);
                                receiptDb.setExtendText(channelId);
                                updateReceipt(receiptDb);
                            } else {
                                //订单重复，但是第三方订单Id不重复，此类算是掉单，需要重点标记出来，需要捞日志确认补单
                                if (!receiptDb.getCoOrderId().equals(coOrderId)) {
                                    callBackResultLog(_receipt, "orderId is equals, but coCoderId is not");
                                }

                                //订单已存在
                                result = RESULT_SUCCESS;
                                if (receiptDb.getStatus() == Receipt.Status.finish) {
                                    PayPullFeedbackServlet.updateChangYouAbroadReceipt(coOrderId, channelId);
                                }
                                resultStr = "receipt existed !!";
                            }
                        }
                    }
                } else {
                    //签名错误
                    result = "0";
                    resultStr = "sign verification fails !!";
                }
            } else {
                result = "0";
                resultStr = "checkPay verification fails !!";
            }
        } catch (Exception e) {
            logger.error(" chang you abroad pay is error !!" + e.getMessage());

            StringWriter writer = new StringWriter();
            e.printStackTrace(new PrintWriter(writer));
            logger.error(writer.getBuffer().toString());

            //异常
            result = RESULT_SIGN_FAIL;
            resultStr = "has Exception !!";
        }

        callBackResultLog(_receipt, resultStr);

        logger.error(" result : " + result);
        Map<String, String> results = new HashMap<>();
        results.put("status", result);
        response.getWriter().write(JsonUtil.map2Json(results));
    }

    private static final Random random = new Random();
    private String tag = String.valueOf(random.nextInt(10000000));

    /**
     * 检查畅游订单
     */
    private boolean checkPay(String receipt) throws Exception {
        //构建data数据
        Map<String, String> dataMap = new HashMap<>();

        String tplt = "{\"receipt\":\"%s\"}";
        String data = String.format(tplt, URLDecoder.decode(receipt, "ISO8859-1"));

        /* 会把base64数据尾巴上的==转义，导致签名不对
        dataMap.put("receipt", receipt);
        String data = JsonUtil.map2Json(dataMap);*/

        //获取相关订单信息
        String _receipt = new String(Base64.decodeBase64(receipt.getBytes(StandardCharsets.UTF_8)), StandardCharsets.UTF_8);
        JsonObject json = JsonUtil.getJsonObject(_receipt);
        String channelId = json.get("channelId").getAsString();
        //验证订单地址
        String check_pay_url = getUrl();
        //第三方sdk参数
        String appKey = SDKParam.getAccountParam(LoginCheckType.ACCOUNT_TYPE_CHANGYOU_ABROAD, 0);
        String appSecret = SDKParam.getAccountParam(LoginCheckType.ACCOUNT_TYPE_CHANGYOU_ABROAD, 1);
        //构建消息头
        List<Pair<String, String>> requestPropertiesParam = new ArrayList<>();
        requestPropertiesParam.add(Pair.of("app_key", appKey));
        //数字签名Sign：data + appKey + appSecret + channelId
        String sb = data + appKey + appSecret + channelId;
        requestPropertiesParam.add(Pair.of("sign", LoginChangYou.createSign(sb)));
        requestPropertiesParam.add(Pair.of("channel_id", channelId));

        data = "data=" + data;
        String resultText = HttpUtil.requestHttpWithPostReturnString(check_pay_url, data.getBytes(), requestPropertiesParam);
        logger.info(" ChangYouAboardCallBackServlet,checkResult, " + resultText);
        //处理验证结果
        JsonObject resultJson = JsonUtil.getJsonObject(resultText);
        int state = resultJson.get("status").getAsInt();
        if (state != 0) {
            logger.error(" ChangYouAboardCallBackServlet,signData, " + data + " sign " + LoginChangYou.createSign(sb));
            logger.error(" ChangYouAboardCallBackServlet,checkResult, " + resultText);
        }
        return state == 0;
    }

    public String getUrl() {
        //线上测试
        String test_url = "https://tnsdk.gaming.com/billing/cyou/gameserver/orderVerify.json";
        //线上正式
        String live_url = "https://nsdk.gaming.com/billing/cyou/gameserver/orderVerify.json";

        return Configuration.isTest() ? test_url : live_url;
    }
}
