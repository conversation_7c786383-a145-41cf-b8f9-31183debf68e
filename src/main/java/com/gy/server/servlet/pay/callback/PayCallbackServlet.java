package com.gy.server.servlet.pay.callback;

import java.io.IOException;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.gy.server.assistant.pay.PayLogger;
import com.gy.server.assistant.pay.Receipt;
import com.gy.server.db.relation.hibernate.HibernateUtil;
import com.gy.server.utils.CharsetEncoding;

/**
 * 充值回调抽象类
 *
 * <AUTHOR> - [Created on 2018/6/8 14:06]
 */
public abstract class PayCallbackServlet extends HttpServlet {

    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        this.doGet(request, response);
    }

    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        request.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);
        response.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);

        PayLogger.logCallbackInfo(request, this.getModuleName());

        callback(request, response);
    }

    /**
     * 更新订单，如果新订单则保存，输出日志
     */
    protected void updateReceipt(Receipt receipt) {

        PayLogger.logUpdateReceipt(this.getModuleName(), receipt);

        if (receipt.getId() > 0) {
            HibernateUtil.update(receipt);
        } else {
            HibernateUtil.save(receipt);
        }
    }

    /**
     * 获得模块名字
     */
    private String getModuleName() {
        String text = this.getClass().getSimpleName();
        int index = text.indexOf("Call");
        if (index < 0) {
            index = text.indexOf("call");
        }

        if (index > 0) {
            text = text.substring(0, index);
        }

        return text;
    }

    protected void callBackResultLog(String receiptStr, String result) {
        PayLogger.logCallbackResult(this.getModuleName(), receiptStr, result);
    }

    protected abstract void callback(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException;
}
