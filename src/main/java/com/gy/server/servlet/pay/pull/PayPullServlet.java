package com.gy.server.servlet.pay.pull;

import com.google.gson.reflect.TypeToken;
import com.gy.server.assistant.pay.PayAssistant;
import com.gy.server.assistant.pay.PayPullData;
import com.gy.server.assistant.pay.Receipt;
import com.gy.server.utils.CharsetEncoding;
import com.gy.server.utils.CollectionUtil;
import com.gy.server.utils.JsonUtil;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * 支付信息拉取
 *
 * <AUTHOR> - [Created on 2018/5/31 16:46]
 */
@WebServlet(name = "PayPullServlet", urlPatterns = "/pay_pull")
public class PayPullServlet extends HttpServlet {

    private static Logger logger = LogManager.getLogger(PayPullServlet.class);

    @Override
    protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        doPost(req, resp);
    }

    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        request.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);
        response.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);

        List<PayPullData> list = new ArrayList<>();

        try {

            //区服集合
            Set<Integer> serverNumSet = JsonUtil.json2Collection(request.getParameter("serverNums"), new TypeToken<Set<Integer>>() {
            }.getType());

            if (CollectionUtil.isNotEmpty(serverNumSet)) {
                //获得所有代发货订单
                List<Receipt> receipts = PayAssistant.getReceiptsByServerNum(serverNumSet);
                if (CollectionUtil.isNotEmpty(receipts)) {
                    receipts.forEach(receipt -> list.add(new PayPullData(receipt)));
                }
            }
        } catch (Exception e) {
            logger.catching(e);
        }

        response.getWriter().write(JsonUtil.list2Json(list));

    }
}
