package com.gy.server.servlet.pay.callback.yijie;

import java.io.IOException;
import java.math.BigDecimal;
import java.text.MessageFormat;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.gy.server.assistant.pay.PayAssistant;
import com.gy.server.assistant.pay.Receipt;
import com.gy.server.sdk.SDKParam;
import com.gy.server.servlet.pay.callback.PayCallbackServlet;
import com.gy.server.utils.LockUtil;
import com.gy.server.utils.Md5Util;

import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 易接充值回调 - SDK版本：
 *
 * <AUTHOR> - [Created on 2018/9/12 14:27]
 */
@WebServlet(name = "YiJieCallbackServlet", urlPatterns = "/callback_yijie")
public class YiJieCallbackServlet extends PayCallbackServlet {

    private static final String RESULT_SUCCESS = "success";
    private static final String RESULT_FAIL = "fail";


    private static Logger logger = LogManager.getLogger(YiJieCallbackServlet.class);

    @SuppressWarnings("Duplicates")
    @Override
    protected void callback(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

        String result = RESULT_SUCCESS;
        try {
            //请求参数
            String gamecode = request.getParameter("app");
            String ext = request.getParameter("cbi");
            String ct = request.getParameter("ct");
            String amount = request.getParameter("fee");
            String pt = request.getParameter("pt");
            String sdk = request.getParameter("sdk");
            String ssid = request.getParameter("ssid");
            String st = request.getParameter("st");
            String coOrderId = request.getParameter("tcd");
            String accountId = request.getParameter("uid");
            String ver = request.getParameter("ver");
            String sign = request.getParameter("sign");

            //附加参数： 内部订单号$渠道$内部商品id$第三方商品id$服务器Num$玩家id
            String[] extArray = ext.split("\\$");
            String orderId = extArray[0];
            String channel = extArray[1];
            int goodsId = Integer.parseInt(extArray[2]);
            String coGoodsId = extArray[3];
            int serverNum = Integer.parseInt(extArray[4]);
            long playerId = Long.parseLong(extArray[5]);

            //配置参数
            String key = SDKParam.getPayParam(channel, 0);

            /*
             * 校验签名
             * sign = md5(app=1234567890ABCDEF&cbi=CBI123456&ct=**********&fee=100&pt=**********&sdk=09CE2B99C22E6D06&ssid=123456&st=1&tcd=137657AVDEDFS&uid=1234&ver=1)
             */
            String signText = MessageFormat.format("app={0}&cbi={1}&ct={2}&fee={3}&pt={4}&sdk={5}&ssid={6}&st={7}&tcd={8}&uid={9}&ver={10}",
                    gamecode, ext, ct, amount, pt, sdk, ssid, st, coOrderId, accountId, ver);
            String _sign = Md5Util.MD5(signText + key);


            if (!"1".equals(st)) {
                //支付不成功
                result = RESULT_FAIL;
            } else if (!StringUtils.equalsIgnoreCase(sign, _sign)) {
                //签名不匹配
                result = RESULT_FAIL;
            } else {
                synchronized (LockUtil.getLockKey(orderId)) {
                    Receipt receipt = PayAssistant.getReceiptByOrderId(orderId);
                    if (receipt != null) {
                        //订单已存在
                        result = RESULT_FAIL;
                    } else {
                        BigDecimal decimal = new BigDecimal(amount);
                        int price = decimal.intValue();
                        receipt = new Receipt(orderId, coOrderId, playerId, accountId, channel, goodsId, coGoodsId, price, serverNum);
                        updateReceipt(receipt);
                    }
                }
            }
        } catch (Exception e) {
            logger.catching(e);
            result = RESULT_FAIL;
        }

        response.getWriter().write(result);
    }
}
