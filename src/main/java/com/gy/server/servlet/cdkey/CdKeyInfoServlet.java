package com.gy.server.servlet.cdkey;

import java.io.IOException;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.gy.server.assistant.cdkey.CdKey;
import com.gy.server.assistant.cdkey.CdKeyAssistant;
import com.gy.server.assistant.cdkey.CdKeyGroup;
import com.gy.server.utils.CharsetEncoding;
import com.gy.server.utils.JsonUtil;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR> - [Created on 2018/4/26 14:27]
 */
@WebServlet(name = "CdKeyInfoServlet", urlPatterns = "/cdkey_info")
public class CdKeyInfoServlet extends HttpServlet {

    private static Logger logger = LogManager.getLogger(CdKeyInfoServlet.class);

    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        this.doGet(request, response);
    }

    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        request.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);
        response.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);

        String key = request.getParameter("cdKey");
        logger.info("[CdKeyInfoServlet][cdKey INFO][request info: cdKey：" + key + "]");

        CdKey cdKey = CdKeyAssistant.getCdKeyByKey(key);
        CdKeyGroup cdKeyGroup = CdKeyAssistant.getCdKeyGroupById(cdKey.getGroupId());

        response.getWriter().write(JsonUtil.obj2Json(cdKeyGroup));
    }
}
