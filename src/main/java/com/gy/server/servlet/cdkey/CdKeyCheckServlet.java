package com.gy.server.servlet.cdkey;

import java.io.IOException;
import java.util.Date;
import java.util.regex.Pattern;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.gy.server.assistant.cdkey.CdKey;
import com.gy.server.assistant.cdkey.CdKeyAssistant;
import com.gy.server.assistant.cdkey.CdKeyGroup;
import com.gy.server.assistant.cdkey.KeyCheckRst;
import com.gy.server.db.relation.hibernate.HibernateUtil;
import com.gy.server.utils.CharsetEncoding;
import com.gy.server.utils.JsonUtil;
import com.gy.server.utils.LockUtil;

import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR> - [Created on 2018/4/26 14:23]
 */
@WebServlet(name = "CdKeyCheckServlet", urlPatterns = "/cdkey_check")
public class CdKeyCheckServlet extends HttpServlet {

    private static final String CDKEY_REGEX = "[a-zA-Z0-9]+";

    private static Logger logger = LogManager.getLogger(CdKeyCheckServlet.class);

    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        this.doGet(request, response);
    }

    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        request.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);
        response.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);

        KeyCheckRst r = new KeyCheckRst();
        r.setResult(false);
        try {
            String cdKey = request.getParameter("cdKey");
            String channel = request.getParameter("channel");
            long registerTime = Long.parseLong(request.getParameter("registerTime"));
            Date registerDate = new Date(registerTime);
            int level = Integer.parseInt(request.getParameter("level"));

            logger.info("[CdKeyCheckServlet][cdKey CHECK][request info: cdKey：" + cdKey + " channel:" + channel
                    + " registerTime:" + registerTime + " level:" + level + "]");

            if (cdKey != null && cdKey.trim().length() != 0 && cdKey.length() < 50
                    && Pattern.matches(CDKEY_REGEX, cdKey)) {
                synchronized (LockUtil.getLockKey(cdKey)) {
                    cdKey = cdKey.toUpperCase();
                    CdKey key = CdKeyAssistant.getCdKeyByKey(cdKey);
                    if (key != null) {
                        CdKeyGroup group = CdKeyAssistant.getCdKeyGroupById(key.getGroupId());
                        if (group != null) {
                            //渠道检查
                            boolean flag = false;
                            if (!StringUtils.equals(group.getChannel(), "")) {
                                String[] str = group.getChannel().split(",");
                                for (String s : str) {
                                    if (s.equals(channel)) {
                                        flag = true;
                                        break;
                                    }
                                }
                            } else {
                                flag = true;
                            }

                            if (!flag) {
                                r.setErrorCode(KeyCheckRst.ErrorCode.channelError);
                            } else if (key.getUsedCount() >= key.getMaxUseCount()) {
                                r.setErrorCode(KeyCheckRst.ErrorCode.codeMax);
                            } else if (group.getStartTime().after(new Date())) {
                                r.setErrorCode(KeyCheckRst.ErrorCode.timeError);
                            } else if (group.getEndTime().before(new Date())) {
                                r.setErrorCode(KeyCheckRst.ErrorCode.timeError);
                            } else if (group.getRegisterStartTime() != null && group.getRegisterStartTime().after(registerDate)) {
                                r.setErrorCode(KeyCheckRst.ErrorCode.timeError);
                            } else if (group.getRegisterEndTime() != null && group.getRegisterEndTime().before(registerDate)) {
                                r.setErrorCode(KeyCheckRst.ErrorCode.timeError);
                            } else if (level < group.getLevel()) {
                                r.setErrorCode(KeyCheckRst.ErrorCode.levelError);
                            } else {
                                key.setUsedCount(key.getUsedCount() + 1);
                                HibernateUtil.update(key);

                                r.setResult(true);
                                r.setRewards(group.getRewards().trim());
                            }
                        } else {
                            r.setErrorCode(KeyCheckRst.ErrorCode.codeInvalid);
                        }
                    } else {
                        r.setErrorCode(KeyCheckRst.ErrorCode.codeInvalid);
                    }
                }
            } else {
                r.setErrorCode(KeyCheckRst.ErrorCode.formatError);
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
            r.setErrorCode(KeyCheckRst.ErrorCode.serverBusy);
        }

        if (r.getErrorCode() != null) {
            logger.info("[CdKeyCheckServlet][cdKey CHECK][cdKey info response: result:" + r.isResult()
                    + " errorCode" + r.getErrorCode().name() + " rewards:" + r.getRewards() + "]");
        } else {
            logger.info("[CdKeyCheckServlet][cdKey CHECK][cdKey info response: result:" + r.isResult()
                    + " rewards:" + r.getRewards() + "]");
        }

        response.getWriter().write(JsonUtil.obj2Json(r));
    }
}
