package com.gy.server.servlet.cdkey;

import java.io.*;
import java.util.ArrayList;
import java.util.List;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.gy.server.assistant.cdkey.CdKey;
import com.gy.server.assistant.cdkey.CdKeyAssistant;
import com.gy.server.assistant.cdkey.ExportCdkey;
import com.gy.server.assistant.user.UserAssistant;
import com.gy.server.message.DefaultMessage;
import com.gy.server.utils.CharsetEncoding;
import com.gy.server.utils.CollectionUtil;
import com.gy.server.utils.OfficeUtil;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR> - [Created on 2018/4/26 14:24]
 */
@WebServlet(name = "CdKeyExportServlet", urlPatterns = "/cdkey_export")
public class CdKeyExportServlet extends HttpServlet {

    private static Logger logger = LogManager.getLogger(CdKeyExportServlet.class);

    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        this.doGet(request, response);
    }

    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        request.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);
        response.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);

        //检查用户是否登录
        if(!UserAssistant.checkUserAuthority(request, response)){
            return;
        }

        int groupId = Integer.parseInt(request.getParameter("groupId"));
        List<CdKey> keys = CdKeyAssistant.getCdKeysByGroupId(groupId);
        if (CollectionUtil.isEmpty(keys)) {
            response.getWriter().write(DefaultMessage.cdkey_list_empty.getText());
        } else {
            List<ExportCdkey> exportList = new ArrayList<>();
            for (CdKey key : keys) {
                exportList.add(new ExportCdkey(key));
            }

            File file = null;
            FileInputStream fis;
            BufferedInputStream bis;
            byte[] buffer = new byte[1024];
            try {
                String title = "兑换码";
                file = File.createTempFile("cdkey", ".xls");

                FileOutputStream fout = new FileOutputStream(file);

                String[] headers = {"CD-KEY", "最大兑换次数", "已使用次数"};
                OfficeUtil.exportExcel(title, headers, exportList, fout);
                fout.close();

                response.setContentType("application/xls");
                response.addHeader("Content-Disposition", "attachment; filename=\"" + file.getName() + "\"");

                //return file;
                fis = new FileInputStream(file);
                bis = new BufferedInputStream(fis);
                OutputStream os = response.getOutputStream();
                int i = bis.read(buffer);
                while (i != -1) {
                    os.write(buffer, 0, i);
                    i = bis.read(buffer);
                }
            } catch (Exception e) {
                logger.error(e.getMessage());
            } finally {
                if (file != null) {
                    file.deleteOnExit();
                }
            }
        }
    }
}