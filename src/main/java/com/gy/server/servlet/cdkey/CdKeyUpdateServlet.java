package com.gy.server.servlet.cdkey;

import java.io.IOException;
import java.util.Date;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.gy.server.assistant.cdkey.CdKeyAssistant;
import com.gy.server.assistant.cdkey.CdKeyGroup;
import com.gy.server.assistant.user.UserAssistant;
import com.gy.server.db.relation.hibernate.HibernateUtil;
import com.gy.server.message.DefaultMessage;
import com.gy.server.utils.CharsetEncoding;
import com.gy.server.utils.time.DateTimeFormatterType;
import com.gy.server.utils.time.DateTimeUtil;

import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR> - [Created on 2018/4/26 13:34]
 */
@WebServlet(name = "CdKeyUpdateServlet", urlPatterns = "/cdkey_update")
public class CdKeyUpdateServlet extends HttpServlet {

    private static Logger logger = LogManager.getLogger(CdKeyUpdateServlet.class);

    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        this.doGet(request, response);
    }

    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        request.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);
        response.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);

        //检查用户是否登录
        if(!UserAssistant.checkUserAuthority(request, response)){
            return;
        }

        String action = request.getParameter("action");

        int type = Integer.parseInt(request.getParameter("type"));
        Date startDate = DateTimeUtil.toDate(DateTimeUtil.parseString(request.getParameter("startTime"), DateTimeFormatterType.date_time));
        Date endDate = DateTimeUtil.toDate(DateTimeUtil.parseString(request.getParameter("endTime"), DateTimeFormatterType.date_time));

        String registerStartTime = request.getParameter("registerStartTime");
        String registerEndTime = request.getParameter("registerEndTime");
        Date registerStartDate = null;
        Date registerEndDate = null;
        if (!StringUtils.equals(registerStartTime, "")) {
            registerStartDate = DateTimeUtil.toDate(DateTimeUtil.parseString(registerStartTime, DateTimeFormatterType.date_time));
        }
        if (!StringUtils.equals(registerEndTime, "")) {
            registerEndDate = DateTimeUtil.toDate(DateTimeUtil.parseString(registerEndTime, DateTimeFormatterType.date_time));
        }

        String rewards = request.getParameter("rewardStr");
        String itemInfo = request.getParameter("itemInfo");
        String channel = request.getParameter("channel");
        String channelName = request.getParameter("channelName");
        int level = Integer.parseInt(request.getParameter("level"));
        String desc = request.getParameter("desc");

        if (StringUtils.equals(rewards, "")) {
            response.getWriter().write(DefaultMessage.cdkey_reward_empty.getText());
        } else {
            CdKeyGroup cdKeyGroup = new CdKeyGroup(type, rewards, itemInfo, channel, channelName,
                    startDate, endDate, registerStartDate, registerEndDate, level, desc);

            if ("add".equals(action)) {
                int maxUseTime = Integer.parseInt(request.getParameter("maxUseTime"));
                int count = Integer.parseInt(request.getParameter("count"));
                if (count > 50000) {
                    response.getWriter().write(DefaultMessage.count_is_over_5000_failure.getText());
                    return;
                }
                HibernateUtil.save(cdKeyGroup);
                CdKeyAssistant.createCdkey(cdKeyGroup, count, maxUseTime);

                logger.info("[CdKeyUpdateServlet][cdKey ADD][cdKey info: " + cdKeyGroup.toString() + "]");
            } else {
                int id = Integer.parseInt(request.getParameter("id"));
                cdKeyGroup.setId(id);
                HibernateUtil.update(cdKeyGroup);

                logger.info("[CdKeyUpdateServlet][cdKey EDIT][cdKey info: " + cdKeyGroup.toString() + "]");
            }

            response.getWriter().write("success");
        }


    }
}
