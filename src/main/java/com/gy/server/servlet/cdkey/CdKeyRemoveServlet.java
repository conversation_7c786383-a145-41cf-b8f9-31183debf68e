package com.gy.server.servlet.cdkey;

import java.io.IOException;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.gy.server.assistant.cdkey.CdKeyAssistant;
import com.gy.server.assistant.user.UserAssistant;
import com.gy.server.utils.CharsetEncoding;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR> - [Created on 2018/4/26 13:35]
 */
@WebServlet(name = "CdKeyRemoveServlet", urlPatterns = "/cdkey_remove")
public class CdKeyRemoveServlet extends HttpServlet {

    private static Logger logger = LogManager.getLogger(CdKeyRemoveServlet.class);

    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        this.doGet(request, response);
    }

    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        request.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);
        response.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);

        //检查用户是否登录
        if(!UserAssistant.checkUserAuthority(request, response)){
            return;
        }

        String groupIds = request.getParameter("ids");
        CdKeyAssistant.deleteCdkeyGroup(groupIds);

        logger.info("[CdKeyRemoveServlet][cdKey REMOVE][groupIds: " + groupIds + "]");

        response.getWriter().write("success");
    }
}
