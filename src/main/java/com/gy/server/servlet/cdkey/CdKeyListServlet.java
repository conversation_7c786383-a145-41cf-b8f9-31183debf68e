package com.gy.server.servlet.cdkey;

import java.io.IOException;
import java.util.List;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.gy.server.assistant.cdkey.CdKeyAssistant;
import com.gy.server.assistant.cdkey.CdKeyGroup;
import com.gy.server.assistant.user.UserAssistant;
import com.gy.server.utils.CharsetEncoding;
import com.gy.server.utils.JsonUtil;

/**
 * <AUTHOR> - [Created on 2018/4/26 13:32]
 */
@WebServlet(name = "CdKeyListServlet", urlPatterns = "/cdkey_list")
public class CdKeyListServlet extends HttpServlet {

    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        this.doGet(request, response);
    }

    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        request.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);
        response.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);

        //检查用户是否登录
        if(!UserAssistant.checkUserAuthority(request, response)){
            return;
        }

        int pageIndex = Integer.parseInt(request.getParameter("pageIndex"));
        int pageSize = Integer.parseInt(request.getParameter("pageSize"));

        List<CdKeyGroup> cdKeyGroups = CdKeyAssistant.getCdkeyGroupByPage(pageIndex, pageSize);
        for (CdKeyGroup cdKeyGroup : cdKeyGroups) {
            cdKeyGroup.init();
        }

        response.getWriter().write(JsonUtil.list2Json(cdKeyGroups));
    }
}
