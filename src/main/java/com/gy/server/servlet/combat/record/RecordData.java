package com.gy.server.servlet.combat.record;

import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.security.NoSuchAlgorithmException;

import com.gy.server.utils.Md5Util;

/**
 * @program: xhx-billing
 * @description:
 * @author: Huang.<PERSON>
 * @create: 2021-10-15 15:12
 **/
public class RecordData {

    private String data1Md5;
    private byte[] data1;
    private long key;
    private String data2Md5;
    private byte[] data2;

    public static RecordData create(byte[] bytes){
        final ByteBuffer buffer = ByteBuffer.wrap(bytes);
        buffer.order(ByteOrder.LITTLE_ENDIAN);

        RecordData r = new RecordData();

        byte[] md5bytes = new byte[32];
        buffer.get(md5bytes);
        r.data1Md5 = new String(md5bytes);

        int length = buffer.getInt();
        r.data1 = new byte[length];
        buffer.get(r.data1);

        r.key = buffer.getLong();

        buffer.get(md5bytes);
        r.data2Md5 = new String(md5bytes);

        length = buffer.getInt();
        r.data2 = new byte[length];
        buffer.get(r.data2);
        return r;
    }

    public boolean check() throws NoSuchAlgorithmException {
        if(!Md5Util.MD5(this.data1).equalsIgnoreCase(this.data1Md5)){
            return false;
        }

        if(!Md5Util.MD5(this.data2).equalsIgnoreCase(this.data2Md5)){
            return false;
        }

        return true;
    }

    public String getData1Md5() {
        return data1Md5;
    }

    public void setData1Md5(String data1Md5) {
        this.data1Md5 = data1Md5;
    }

    public byte[] getData1() {
        return data1;
    }

    public void setData1(byte[] data1) {
        this.data1 = data1;
    }

    public long getKey() {
        return key;
    }

    public void setKey(long key) {
        this.key = key;
    }

    public String getData2Md5() {
        return data2Md5;
    }

    public void setData2Md5(String data2Md5) {
        this.data2Md5 = data2Md5;
    }

    public byte[] getData2() {
        return data2;
    }

    public void setData2(byte[] data2) {
        this.data2 = data2;
    }
}
