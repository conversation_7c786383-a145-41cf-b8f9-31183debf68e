package com.gy.server.servlet.combat.record;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import com.gy.server.util.QCloudUtil;
import com.gy.server.utils.Md5Util;
import com.gy.server.utils.net.ServletUtil;
import com.qcloud.cos.COSClient;
import javax.servlet.ServletException;
import javax.servlet.annotation.MultipartConfig;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.Part;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * @program: xhx-billing
 * @description:
 * @author: <PERSON><PERSON>
 * @create: 2021-10-09 15:05
 *
 * 下载地址：  http://xhxbjz-1251125656.cos.ap-shanghai.myqcloud.com/cbtrecord/
 **/
@WebServlet(urlPatterns = "/record_upload")
@MultipartConfig(maxFileSize = 2*1024*1024)
public class CombatRecordUploadServlet extends HttpServlet {

    private static Logger logger = LogManager.getLogger(CombatRecordUploadServlet.class);
    public static String cbtDir = "cbtrecord/";

    private static COSClient client = QCloudUtil.cosClient();

    @Override
    protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        String rst = "1";
        try{
            req.setCharacterEncoding("utf-8");
            resp.setCharacterEncoding("utf-8");

            ServletUtil.infoLog(CombatRecordUploadServlet.class.getName(), req, logger);

            String fileName = req.getParameter("fileName");
            String md5 = fileName.split("_")[2];
            String data = req.getParameter("bytes");
            byte[] bytes = data.getBytes("utf-8");
            if(Md5Util.MD5(bytes).equalsIgnoreCase(md5)){
            QCloudUtil.updateToCOS(client, fileName, cbtDir, bytes);
            }else{
                rst = "5";
            }
        }catch (Exception e){
            e.printStackTrace();
            rst = "2";
        }

        resp.getWriter().write(rst);
    }

    @Override
    protected void doPost(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        String rst = "1";
        try {
            req.setCharacterEncoding("utf-8");
            resp.setCharacterEncoding("utf-8");

            byte[] bytes = null;
            String fileName = null;

            for (Part httpPart : req.getParts()){
                String name = httpPart.getName();
                ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream((int) httpPart.getSize());
                InputStream is = httpPart.getInputStream();
                byte[] buffer = new byte[1024];

                int readLength;
                while((readLength = is.read(buffer)) > 0) {
                    byteArrayOutputStream.write(buffer, 0, readLength);
                }

                byte[] newData = byteArrayOutputStream.toByteArray();
                if(name.equalsIgnoreCase("fileName")){
                    fileName = new String(newData);
                }else if (name.equalsIgnoreCase("bytes")){
                    bytes = newData;
                }
            }

            String md5 = fileName.split("_")[2];
            if(Md5Util.MD5(bytes).equalsIgnoreCase(md5)){

                RecordData recordData = RecordData.create(bytes);
                if(!recordData.check()){
                    rst = "51";
                }else {
                    QCloudUtil.updateToCOS(client, fileName, cbtDir, bytes);
                }
            }else{
                rst = "5";
            }
        }catch (Exception e){
            e.printStackTrace();
            rst = "2";
        }

        resp.getWriter().write(rst);
    }
}
