package com.gy.server.servlet.account;

import com.gy.server.assistant.account.Account;
import com.gy.server.assistant.account.AccountAssistant;
import com.gy.server.assistant.account.AccountManager;
import com.gy.server.assistant.account.Token;
import com.gy.server.db.relation.hibernate.HibernateUtil;
import com.gy.server.message.DefaultMessage;
import com.gy.server.servlet.Md5RequestHttpServlet;
import com.gy.server.utils.CharsetEncoding;
import com.gy.server.utils.JsonUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> - [Created on 2018/3/28 20:25]
 */
@WebServlet(name = "AccountRegisterMD5Servlet", urlPatterns = "/account_register_md5")
public class AccountRegisterMD5Servlet extends Md5RequestHttpServlet {

    private static final String RES_PARAM_STATUS = "status";
    private static final String RES_PARAM_DESC = "desc";
    private static final String RES_PARAM_TOKEN = "token";
    private static final String RES_PARAM_SURPLUS = "surplus";
    private static final String RES_PARAM_ID = "id";

    private static Logger logger = LogManager.getLogger(AccountRegisterMD5Servlet.class);

    public Map<Object, Object> doPost0(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        request.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);
        response.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);

        Map<Object, Object> res = new HashMap<>();

        String accountId = request.getParameter("account_id");
        String password = request.getParameter("password");
        String uuid = request.getParameter("uuid");
        String mail = request.getParameter("mail");

        try {
            if (StringUtils.isBlank(accountId)
                    || StringUtils.isBlank(password)
                    || StringUtils.isBlank(uuid)
                    || StringUtils.isBlank(mail)
                    || !AccountAssistant.isValidAccountId(accountId)
                    || !AccountAssistant.isValidMail(mail)) {
                res.put(RES_PARAM_STATUS, 2);
                res.put(RES_PARAM_DESC, DefaultMessage.params_error.getText());
            } else {
                Account account = AccountManager.getAccountByAccountId(accountId);
                if (account != null) {
                    res.put(RES_PARAM_STATUS, 3);
                    res.put(RES_PARAM_DESC, DefaultMessage.account_exist.getText());
                } else {
                    String id = null;
                    for (int i = 0; i < 10; i++) {
                        id = AccountAssistant.createId();
                        Account b = AccountManager.getAccount(id);
                        if (b != null) {
                            id = null;
                            continue;
                        }
                        break;
                    }
                    if (id == null) {
                        res.put(RES_PARAM_STATUS, 1);
                        res.put(RES_PARAM_DESC, DefaultMessage.account_register_fail.getText());
                    } else {
                        account = new Account();
                        account.setId(id);
                        account.setAccountId(accountId);
                        account.setPassword(AccountAssistant.createPassword(password));
                        account.setUuid(uuid);
                        account.setMail(mail);

                        HibernateUtil.save(account);

                        Token token = AccountManager.createToken(account.getId());
                        res.put(RES_PARAM_TOKEN, token.getToken());
                        res.put(RES_PARAM_SURPLUS, token.getSurplusSeconds());
                        res.put(RES_PARAM_ID, account.getId());
                        res.put(RES_PARAM_STATUS, 0);
                        res.put(RES_PARAM_DESC, DefaultMessage.account_register_success.getText());
                    }
                }

            }
        } catch (Exception e) {
            logger.catching(e);
            res.put(RES_PARAM_STATUS, 1);
            res.put(RES_PARAM_DESC, DefaultMessage.server_error.getText());
        }

        return res;
    }
}
