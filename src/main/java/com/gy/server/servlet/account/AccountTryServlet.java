package com.gy.server.servlet.account;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.gy.server.assistant.account.Account;
import com.gy.server.assistant.account.AccountAssistant;
import com.gy.server.assistant.account.AccountManager;
import com.gy.server.assistant.account.Token;
import com.gy.server.db.relation.hibernate.HibernateUtil;
import com.gy.server.message.DefaultMessage;
import com.gy.server.utils.CharsetEncoding;
import com.gy.server.utils.JsonUtil;
import com.gy.server.utils.Md5Util;

import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR> - [Created on 2018/3/28 18:20]
 */
@WebServlet(name = "AccountTryServlet", urlPatterns = "/account_try")
public class AccountTryServlet extends HttpServlet {

    private static final String RES_PARAM_STATUS = "status";
    private static final String RES_PARAM_DESC = "desc";
    private static final String RES_PARAM_TOKEN = "token";
    private static final String RES_PARAM_SURPLUS = "surplus";
    private static final String RES_PARAM_ACCOUNT = "account_id";
    private static final String RES_PARAM_ID = "id";
    private static final String RES_PARAM_PASSWORD = "password";

    private static Logger logger = LogManager.getLogger(AccountTryServlet.class);


    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        request.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);
        response.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);

        Map<Object, Object> res = new HashMap<>();

        String uuid = request.getParameter("uuid");

        try {
            if (uuid == null || StringUtils.isBlank(uuid)) {
                res.put(RES_PARAM_STATUS, 2);
                res.put(RES_PARAM_DESC, DefaultMessage.params_error.getText());
            } else {
                Account account = null;
                String password = null;
                for (int i = 0; i < 10; i++) {
                    String id = AccountAssistant.createId();
                    String accountId = AccountAssistant.createRandomText();
                    Account a = AccountManager.getAccount(id);
                    Account b = AccountManager.getAccountByAccountId(accountId);
                    if (a != null || b != null) {
                        continue;
                    }
                    account = new Account();
                    account.setId(id);
                    account.setAccountId(accountId);
                    password = Md5Util.MD5(AccountAssistant.createRandomText());
                    account.setPassword(AccountAssistant.createPassword(password));
                    account.setUuid(uuid);
                    break;
                }

                if (account == null) {
                    res.put(RES_PARAM_STATUS, 1);
                    res.put(RES_PARAM_DESC, DefaultMessage.account_register_fail.getText());
                } else {
                    HibernateUtil.save(account);

                    Token token = AccountManager.createToken(account.getId());
                    res.put(RES_PARAM_TOKEN, token.getToken());
                    res.put(RES_PARAM_SURPLUS, token.getSurplusSeconds());
                    res.put(RES_PARAM_ACCOUNT, account.getAccountId());
                    res.put(RES_PARAM_ID, account.getId());
                    res.put(RES_PARAM_PASSWORD, password);
                    res.put(RES_PARAM_STATUS, 0);
                    res.put(RES_PARAM_DESC, DefaultMessage.account_register_success.getText());
                }
            }
        } catch (Exception e) {
            logger.catching(e);
            res.put(RES_PARAM_STATUS, 1);
            res.put(RES_PARAM_DESC, DefaultMessage.server_error.getText());
        }

        response.getWriter().write(JsonUtil.map2Json(res));
    }
}
