package com.gy.server.servlet.account;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.gy.server.assistant.account.AccountAssistant;
import com.gy.server.assistant.account.AccountManager;
import com.gy.server.assistant.account.Token;
import com.gy.server.message.DefaultMessage;
import com.gy.server.utils.CharsetEncoding;
import com.gy.server.utils.JsonUtil;

import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR> - [Created on 2018/3/28 21:11]
 */
@WebServlet(name = "AccountLoginCheckServlet", urlPatterns = "/account_login_check")
public class AccountLoginCheckServlet extends HttpServlet {

    private static final String RES_PARAM_STATUS = "status";
    private static final String RES_PARAM_DESC = "desc";

    private static Logger logger = LogManager.getLogger(AccountLoginCheckServlet.class);


    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

        request.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);
        response.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);

        Map<Object, Object> res = new HashMap<>();

        String id = request.getParameter("id");
        String token = request.getParameter("token");
        String sign = request.getParameter("sign");

        try {
            if (StringUtils.isBlank(id)
                    || StringUtils.isBlank(token)
                    || StringUtils.isBlank(sign)) {
                res.put(RES_PARAM_STATUS, 2);
                res.put(RES_PARAM_DESC, DefaultMessage.params_error.getText());
            } else {
                Map<String, String> keyMap = new HashMap<>();
                keyMap.put("id", id);
                keyMap.put("token", token);

                if (!AccountAssistant.checkSign(keyMap, sign)) {
                    res.put(RES_PARAM_STATUS, 3);
                    res.put(RES_PARAM_DESC, DefaultMessage.account_sign_error.getText());
                } else {
                    Token t = AccountManager.getToken(id);
                    if (t == null) {
                        res.put(RES_PARAM_STATUS, 1);
                        res.put(RES_PARAM_DESC, DefaultMessage.account_login_fail.getText());
                    } else {
                        res.put(RES_PARAM_STATUS, 0);
                        res.put(RES_PARAM_DESC, DefaultMessage.account_login_success.getText());
                    }
                }
            }

        } catch (Exception e) {
            logger.catching(e);
            res.put(RES_PARAM_STATUS, 1);
            res.put(RES_PARAM_DESC, DefaultMessage.server_error.getText());
        }

        response.getWriter().write(JsonUtil.map2Json(res));
    }
}
