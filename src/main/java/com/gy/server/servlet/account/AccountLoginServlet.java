package com.gy.server.servlet.account;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.gy.server.assistant.account.Account;
import com.gy.server.assistant.account.AccountAssistant;
import com.gy.server.assistant.account.AccountManager;
import com.gy.server.assistant.account.Token;
import com.gy.server.message.DefaultMessage;
import com.gy.server.utils.CharsetEncoding;
import com.gy.server.utils.JsonUtil;

import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR> - [Created on 2018/3/28 20:15]
 */
@WebServlet(name = "AccountLoginServlet", urlPatterns = "/account_login")
public class AccountLoginServlet extends HttpServlet {

    private static final String RES_PARAM_STATUS = "status";
    private static final String RES_PARAM_DESC = "desc";
    private static final String RES_PARAM_TOKEN = "token";
    private static final String RES_PARAM_SURPLUS = "surplus";
    private static final String RES_PARAM_ID = "id";

    private static Logger logger = LogManager.getLogger(AccountLoginServlet.class);


    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

        request.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);
        response.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);

        Map<Object, Object> res = new HashMap<>();

        String accountId = request.getParameter("account_id");
        String password = request.getParameter("password");

        try {
            if (StringUtils.isBlank(accountId) || StringUtils.isBlank(password)) {
                res.put(RES_PARAM_STATUS, 2);
                res.put(RES_PARAM_DESC, DefaultMessage.params_error.getText());
            } else {
                Account account = AccountManager.getAccountByAccountId(accountId);
                if (account == null || !account.getPassword().equals(AccountAssistant.createPassword(password))) {
                    res.put(RES_PARAM_STATUS, 3);
                    res.put(RES_PARAM_DESC, DefaultMessage.account_account_not_match_password.getText());
                } else {
                    res.put(RES_PARAM_STATUS, 0);
                    res.put(RES_PARAM_DESC, DefaultMessage.account_login_success.getText());
                    Token token = AccountManager.createToken(account.getId());

                    res.put(RES_PARAM_TOKEN, token.getToken());
                    res.put(RES_PARAM_ID, account.getId());
                    res.put(RES_PARAM_SURPLUS, token.getSurplusSeconds());
                }
            }
        } catch (Exception e) {
            logger.catching(e);
            res.put(RES_PARAM_STATUS, 1);
            res.put(RES_PARAM_DESC, DefaultMessage.server_error.getText());
        }

        response.getWriter().write(JsonUtil.map2Json(res));
    }
}
