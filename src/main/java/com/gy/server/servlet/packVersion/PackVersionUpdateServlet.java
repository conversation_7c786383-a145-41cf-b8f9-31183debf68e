package com.gy.server.servlet.packVersion;

import com.gy.server.assistant.packVersion.PackVersion;
import com.gy.server.utils.CharsetEncoding;
import com.gy.server.utils.JsonUtil;
import com.ttlike.server.tl.baselib.CommonsConfiguration;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.redis.key.BaseRedisKey;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@WebServlet(name = "PackVersionUpdateServlet", urlPatterns = "/pack_version_update")
public class PackVersionUpdateServlet extends HttpServlet {

    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        this.doGet(request, response);
    }

    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        request.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);
        response.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);
        if (CommonsConfiguration.runMode.isLive()) {
            return;
        }
        String result = "success";
        String data = request.getParameter("data");
        PackVersion packVersion = JsonUtil.json2Obj(data, PackVersion.class);
        if (packVersion == null) {
            result = "packVersion is null";
        } else {
            String key = BaseRedisKey.Commons.PACK_VERSION.getRedisKey(packVersion.getPlatform());
            TLBase.getInstance().getRedisAssistant().setBean(key, packVersion);
        }

        response.getWriter().write(result);
    }

}
