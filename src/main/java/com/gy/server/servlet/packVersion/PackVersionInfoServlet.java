package com.gy.server.servlet.packVersion;

import com.gy.server.assistant.packVersion.PackVersion;
import com.gy.server.utils.CharsetEncoding;
import com.gy.server.utils.JsonUtil;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.redis.key.BaseRedisKey;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@WebServlet(name = "PackVersionInfoServlet", urlPatterns = "/pack_version_info")
public class PackVersionInfoServlet extends HttpServlet {

    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        this.doPost(request, response);
    }

    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        request.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);
        response.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);
        if (com.ttlike.server.tl.baselib.CommonsConfiguration.runMode.isLive()) {
            return;
        }
        String platform = request.getParameter("platform");
        String key = BaseRedisKey.Commons.PACK_VERSION.getRedisKey(platform);
        PackVersion maxPackVersion = TLBase.getInstance().getRedisAssistant().getBean(PackVersion.class, key);
        if (maxPackVersion == null) {
            maxPackVersion = new PackVersion();
            maxPackVersion.setVersion("0.0.0");
        }
        response.getWriter().print(JsonUtil.obj2Json(maxPackVersion));
    }
}
