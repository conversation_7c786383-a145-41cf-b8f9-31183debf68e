package com.gy.server.servlet.server;

import com.gy.server.game.server.GameAreaManager;
import com.gy.server.gate.Gate;
import com.gy.server.gate.GateManager;
import com.gy.server.gate.GateManagerCommandService;
import com.gy.server.server.JsonArenaServer;
import com.gy.server.utils.CharsetEncoding;
import com.gy.server.utils.JsonUtil;
import com.gy.server.utils.MathUtil;
import com.gy.server.utils.net.ServletUtil;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.redis.key.BaseRedisKey;
import com.ttlike.server.tl.baselib.serialize.AreaServer;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;

/**
 * @program: tl-billing
 * @description: 节点列表
 * @author: Huang.Xia
 * @create: 2019-10-31 12:01
 **/
@WebServlet(name = "NodeListServlet", urlPatterns = "/node_list")
public class NodeListServlet extends HttpServlet {

    @Override
    protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        req.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);
        resp.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);
        String accountId = req.getParameter("accountId");
        String accountType = req.getParameter("accountType");
        String channel = req.getParameter("channel");

		req.getParameterMap();
        Map<String, Object> data = new HashMap<>();
        {
			TLBase.getInstance().getRedisAssistant()
					.setAdd(BaseRedisKey.Billing.USER_PROTOCOL_SET.getRedisKey(accountId, accountType), channel);
			//如果有拒绝协议，从拒绝协议中删除
            TLBase.getInstance().getRedisAssistant()
                    .setRemove(BaseRedisKey.Billing.USER_PROTOCOL_REJECT_SET.getRedisKey(accountId, accountType), channel);

            Collection<JsonArenaServer> areas = GameAreaManager.getInstance().getServers(ServletUtil.getIpByHttpServletRequest(req));

            List<JsonArenaServer> recomms = new ArrayList<>();
            areas.forEach(area->{
                if(area.status == AreaServer.Status.recommend.ordinal()){
                    recomms.add(area);
                }
            });

            if(recomms.size() > 1){
                //排序取最少的二分之一，然后随机
                recomms.sort(Comparator.comparingInt(o -> o.regist));

                int index = MathUtil.randomInt(Math.max(1,recomms.size()/2));
                JsonArenaServer rec = recomms.get(index);

                areas.forEach(a->{
                    if(a.number != rec.number && a.status == AreaServer.Status.recommend.ordinal()){
                        //非选中目标，则设置为新服
                        a.status = AreaServer.Status.newOpen.ordinal();
                    }
                });
            }


            data.put("gameAreas", areas);


            List<Gate.JsonGate> gates = new ArrayList<>();
            GateManager.getInstance().getGates().forEach(area -> gates.add(area.toJsonGate()));
            //取负载最低的前二分之一，然后随机
            gates.sort(Comparator.comparingInt(Gate.JsonGate::getOnline));

            List<Gate.JsonGate> gatesRst = gates;
            if(gates.size() > 3) {
                gatesRst = gates.subList(0, Math.max(3,gates.size()/2));
            }
            Collections.shuffle(gatesRst);
            if(gatesRst.size() >3) {
                data.put("gates", gatesRst.subList(0,3));
            }else {
                data.put("gates", gatesRst);
            }
        }
        resp.getWriter().write(JsonUtil.map2Json(data));
    }

    @Override
    protected void doPost(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        doGet(req, resp);
    }

}
