package com.gy.server.servlet.server;

import com.gy.server.game.server.GameArea;
import com.gy.server.game.server.GameAreaManager;
import com.gy.server.server.JsonArenaServer;
import com.gy.server.servlet.Md5RequestHttpServlet;
import org.apache.commons.lang3.math.NumberUtils;

import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

/**
 * 服务器查询单个服务器的接口
 * Created by liuyuzhen on 2018/1/18.
 */
@WebServlet(name = "AreaStateMD5Servlet", urlPatterns = "/area_state_md5")
public class AreaStateMD5Servlet extends Md5RequestHttpServlet {

    @Override
    public Map<Object, Object> doPost0(HttpServletRequest req, HttpServletResponse resp) {
        String serverNumStr = req.getParameter("serverNum");
        Map<Object, Object> data = new HashMap<>();
        data.put("error", "");
        if (NumberUtils.isCreatable(serverNumStr)) {
            int serverNum = Integer.parseInt(serverNumStr);
            GameArea gameServer = GameAreaManager.getInstance().getServers().get(serverNum);
            if (gameServer == null) {
                JsonArenaServer jsonArenaServer = new JsonArenaServer(gameServer);
                data.put("area", jsonArenaServer);
            }else{
                data.put("error", "areaServer不存在:" + serverNumStr);
            }
        } else {
            data.put("error", "serverNum非法传入:" + serverNumStr);
        }
        return data;
    }
}
