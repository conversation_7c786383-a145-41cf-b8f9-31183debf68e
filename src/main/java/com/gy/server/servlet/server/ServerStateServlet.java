package com.gy.server.servlet.server;

import com.gy.server.game.server.GameArea;
import com.gy.server.game.server.GameAreaManager;
import com.gy.server.utils.CharsetEncoding;
import com.gy.server.utils.CollectionUtil;
import com.gy.server.utils.JsonUtil;
import com.ttlike.server.tl.baselib.serialize.server.GameServerState;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

/**
 * <AUTHOR> - [Created on 2018/6/27 11:28]
 * gs 访问用的
 */
@WebServlet(name = "ServerStateServlet", urlPatterns = "/server_state")
public class ServerStateServlet extends HttpServlet {


    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        this.doGet(request, response);
    }

    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        request.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);
        response.setCharacterEncoding(CharsetEncoding.ENCODING_UTF_8);

        int serverId = Integer.parseInt(request.getParameter("serverId"));

        List<GameArea> gameAreas = GameAreaManager.getInstance().getServersById(serverId);

        GameServerState state = new GameServerState(serverId);

        // 没有配置服务器信息
        if (CollectionUtil.isEmpty(gameAreas)) {
            response.getWriter().print(JsonUtil.obj2Json(state));
            return;
        }

        for(GameArea area : gameAreas){
            state.getServerNumbers().add(area.getAreaNumber());
            if(area.getLimitIpSet().size() > 0){
                state.getLimitIps().addAll(area.getLimitIpSet());
            }
            if(area.getOpenTimeLong() > state.getOpenTime()){
                state.setOpenTime(area.getOpenTimeLong());
            }
        }
        response.getWriter().print(JsonUtil.obj2Json(state));
    }

}