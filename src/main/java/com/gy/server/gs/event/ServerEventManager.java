package com.gy.server.gs.event;

import java.util.concurrent.ConcurrentLinkedDeque;

import com.gy.server.utils.event.EventBag;
import com.gy.server.utils.function.Ticker;

import org.apache.commons.lang3.ArrayUtils;

/* 系统事件管理器
 * <AUTHOR> 
 * @date 2019年9月7日 下午2:24:24 
 */
public class ServerEventManager implements Ticker {

    private final static ServerEventManager instance = new ServerEventManager();

    private final EventBag<ServerEventType, ServerEvent, ServerEventHandler> eventBags = new EventBag<>();

    private final ConcurrentLinkedDeque<ServerEvent> events = new ConcurrentLinkedDeque<>();


    private ServerEventManager() {

    }

    public static ServerEventManager getInstance() {
        return instance;
    }

    public static void registerEventHandler(ServerEventHandler handler) {
        ServerEventType[] eventTypes = handler.getEventTypes();
        if (!ArrayUtils.isEmpty(eventTypes)) {
            instance.addEventHandler(eventTypes, handler);
        }
    }

    public void addEventHandler(ServerEventType[] eventTypes, ServerEventHandler handler) {
        for (ServerEventType eventType : eventTypes) {
            addEventHandler(eventType, handler);
        }
    }

    public void addEventHandler(ServerEventType eventType, ServerEventHandler handler) {
        eventBags.add(eventType, handler);
    }

    public void syncPostEvent(ServerEventType type, Object... params) {
        syncPostEvent(ServerEvent.build(type, params));
    }

    /**
     * 同步执行事件
     */
    public void syncPostEvent(ServerEvent event) {
        eventBags.post(event);
    }

    public void asyncPostEvent(ServerEventType type, Object... params) {
        asyncPostEvent(ServerEvent.build(type, params));
    }

    /**
     * 异步执行事件
     */
    public void asyncPostEvent(ServerEvent event) {
        events.offer(event);
    }

    @Override
    public void tick() {
        while (!events.isEmpty()) {
            ServerEvent event = events.remove();
            if (event != null) {
                syncPostEvent(event);
            }
        }
    }
}
