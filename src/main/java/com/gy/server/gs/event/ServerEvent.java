package com.gy.server.gs.event;

import com.gy.server.utils.event.Event;

/* 系统事件
 * <AUTHOR> 
 * @date 2019年9月7日 下午2:23:50 
 */
public class ServerEvent extends Event<ServerEventType> {

    private ServerEvent() {
        super(null);
    }

    public static ServerEvent build(ServerEventType type, Object... params) {
        ServerEvent event = new ServerEvent();
        event.eventType = type;
        event.params = params;
        return event;
    }

}
