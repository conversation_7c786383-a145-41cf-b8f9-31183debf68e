package com.gy.server.gs.event;

/* 系统事件类型
 * <AUTHOR> 
 * @date 2019年9月7日 下午2:24:38 
 */
public enum ServerEventType {

    // @formatter:off

    //******************* 定时器类型 ***********************
    newDay                          ("0 0 0 * * ? "),
    day5c<PERSON>                       ("0 0 5 * * ? "),
    every10Sec                      ("0/10 * * * * ? "),
    cacheCleanUp                    ("0 0/10 * * * ? "),
    arenaRankReward                 ("0 0 22 * * ? "),



    //******************* 事件类型 ***********************
    test,
    ;

    // @formatter:on


    /**
     * 定时器时间表达式
     */
    String timeExpression;

    ServerEventType() {

    }

    ServerEventType(String timeExpression) {
        this.timeExpression = timeExpression;
    }

    public String getTimeExpression() {
        return timeExpression;
    }
}
