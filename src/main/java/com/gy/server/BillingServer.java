package com.gy.server;


import com.gy.server.assistant.languagePack.LanguagePackManager;
import com.gy.server.assistant.notice.NoticeManager;
import com.gy.server.assistant.version.VersionManager;
import com.gy.server.game.server.GameAreaManager;
import com.gy.server.gate.GateManager;
import com.gy.server.gate.GateManagerCommandService;
import com.gy.server.utils.runner.RunnerManager;
import com.ttlike.server.tl.baselib.TLBase;

/* 项目入口
 * <AUTHOR>
 * @date 2019年9月9日 上午10:31:15
 */
public class BillingServer extends MainThread {

//    public static MessageHandlerBag defaultMessageHandlerBag = new MessageHandlerBag<>();

    @Override
    public void init() throws Exception {
//      定时检测gate
        addTicker(GateManager.getInstance());

        NoticeManager.init();
        VersionManager.init();
        LanguagePackManager.init();

        //消息处理器注册
        RunnerManager.addRunner(NoticeManager.getInstance(), "NoticeManager", true);
        RunnerManager.addRunner(VersionManager.getInstance(), "VersionManager", true);
        RunnerManager.addRunner(LanguagePackManager.getInstance(), "LanguagePackManager", true);
        RunnerManager.addRunner(GameAreaManager.getInstance(), "GameServerManager", true);
        RunnerManager.addRunner(GateManager.getInstance(), "GateManager", true);
    }

    @Override
    public void startup() throws Exception {

        this.start();
    }

    @Override
    public void shutdown() throws Exception {
        NoticeManager.shutdown();
        VersionManager.shutdown();
        LanguagePackManager.shutdown();
        TLBase.getInstance().shutdown();
    }

}
