package com.gy.server.gm;

import com.gy.server.annotation.MessageMethod;
import com.gy.server.annotation.MessageServiceBean;
import com.gy.server.core.MessageServerType;
import com.gy.server.core.MethodInvokeType;
import com.gy.server.core.command.CommandRequestParams;
import com.gy.server.core.command.ServerCommandRequest;
import com.gy.server.game.server.GameArea;
import com.gy.server.game.server.GameAreaAssistant;
import com.gy.server.game.server.GameAreaManager;
import com.gy.server.gate.Gate;
import com.gy.server.gate.GateAssistant;
import com.gy.server.gate.GateManager;
import com.gy.server.util.IPUtils;
import com.gy.server.utils.CollectionUtil;
import com.gy.server.utils.StringUtil;
import com.gy.server.utils.time.DateTimeFormatterType;
import com.gy.server.utils.time.DateTimeUtil;
import com.ttlike.server.tl.baselib.message.rst.GmExecuteGroovyMessageRst;
import com.ttlike.server.tl.baselib.serialize.AreaServer;
import com.ttlike.server.tl.baselib.serialize.gm.GmtGameArea;
import com.ttlike.server.tl.baselib.serialize.gm.GmtGate;
import com.ttlike.server.tl.baselib.util.GroovyExecutor;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * gm rpc消息处理
 * @author: gbk
 * @date: 2024-08-02 13:27
 */
@MessageServiceBean(description = "GMCommandService", messageServerType = MessageServerType.billing)
public class GMCommandService{


	@MessageMethod(description = "执行groovy脚本", invokeType = MethodInvokeType.async)
	private static void groovyDeal(ServerCommandRequest request, CommandRequestParams params) {
		GmExecuteGroovyMessageRst rst = new GmExecuteGroovyMessageRst();
		String code = params.getParam(0);
		Map<GroovyExecutor.ResultText, String> texts = GroovyExecutor.execute(code);
		for (Map.Entry<GroovyExecutor.ResultText, String> entry : texts.entrySet()) {
			GroovyExecutor.ResultText type = entry.getKey();
			String value = entry.getValue();
			type.setValue(rst, value);
		}
		System.out.println("execute groovy script success.");
		// 回调返回
		request.addCallbackParam(rst);
	}

	@MessageMethod(description = "更新服务器白名单", invokeType = MethodInvokeType.async)
	private static void gameAreaLimitIpsUpdate(ServerCommandRequest request, CommandRequestParams params) {
		int type = params.getParam(0);//1.添加  2.移除  3.清空
		String idsStr = params.getParam(1);
		String ipsStr = params.getParam(2);

		int[] serverIds = StringUtil.splitToIntArray(idsStr, ",");
		List<GameArea> areas = GameAreaAssistant.getServerList(serverIds);
		if(type == 3){
			for(GameArea area : areas){
				area.getLimitIpSet().clear();
				area.setLimitIps("");
			}

			GameAreaAssistant.updateBatch(areas);
			request.addCallbackParam("清空成功");
			return;
		}

		//核对ip
		ipsStr = ipsStr.trim();
		if (StringUtils.isEmpty(ipsStr)) {
			request.addCallbackParam("ip输入有误");
			return;
		}

		String[] ips = ipsStr.split(",");
        for(String ip : ips){
            if(!IPUtils.checkIp(ip)){
				request.addCallbackParam("ip格式不对");
                return;
            }
        }

		List<String> changeIps = Arrays.asList(ips);

		if(ips.length == 0){
			request.addCallbackParam("ip输入有误");
			return;
		}

		for(GameArea area : areas){
			if(type == 1){
				area.getLimitIpSet().addAll(changeIps);
			}

			if(type == 2){
				area.getLimitIpSet().removeAll(changeIps);
			}

			area.updateLimitIps();
		}

		GameAreaAssistant.updateBatch(areas);
		request.addCallbackParam("成功");
	}

	@MessageMethod(description = "更新服务器状态及白名单", invokeType = MethodInvokeType.async)
	private static void areaMultiUpdate(ServerCommandRequest request, CommandRequestParams params) {
		String areaIdsStr = params.getParam(0);
		String statusStr = params.getParam(1);
		String fluencyStatusStr = params.getParam(2);
		String ipLimits = params.getParam(3);

		int[] numbers = StringUtil.splitToIntArray(areaIdsStr, ",");
		AreaServer.Status status = AreaServer.Status.valueOf(statusStr);
		AreaServer.FluencyStatus fluencyStatus = AreaServer.FluencyStatus.valueOf(fluencyStatusStr);
		Set<String> ipLimitSet = CollectionUtil.toCollection(StringUtils.split(ipLimits, ","), HashSet::new);

		String rst = "success";
		List<GameArea> areas = GameAreaAssistant.getServerList(numbers);
		for (GameArea areaServer : areas) {
				//已经开放的服务器不能改为toOpen状态
			if (areaServer.getStatus() != AreaServer.Status.toOpenForTest
					&& areaServer.getStatus() != AreaServer.Status.toOpenForPublic) {
				if (status == AreaServer.Status.toOpenForTest || status == AreaServer.Status.toOpenForPublic) {
					rst = "已经开放的服务器不能改为[待开服]";
					break;
				}
			} else if (areaServer.getStatus() == AreaServer.Status.toOpenForTest || areaServer.getStatus() == AreaServer.Status.toOpenForPublic) {
				rst = "[待开服]状态不能修改，必须等时间到达";
				break;
			}

			areaServer.setStatus(status);
			areaServer.setFluencyStatus(fluencyStatus);
			areaServer.getLimitIpSet().clear();
			areaServer.getLimitIpSet().addAll(ipLimitSet);
			areaServer.updateLimitIps();
		}

		GameAreaAssistant.updateBatch(areas);
		request.addCallbackParam(rst);
	}

	@MessageMethod(description = "区服添加或更新", invokeType = MethodInvokeType.async)
	private static void areasAddOrUpdate(ServerCommandRequest request, CommandRequestParams params) {
		List<GmtGameArea> areas = params.getParam(0);
		List<GameArea> rst = new ArrayList<>();
		for(GmtGameArea area : areas){
			GameArea gameArea = GameAreaAssistant.find(area.getAreaNumber());
			long time = DateTimeUtil.toMillis(DateTimeFormatterType.date_time.parseString(area.getOpenTime()));
			if(gameArea == null){
				//新增
				gameArea = new GameArea();
				gameArea.setAreaNumber(area.getAreaNumber());
				gameArea.setKeyword(area.getKeyword());
				gameArea.setLimitIps(area.getLimitIps());
				gameArea.setStatus(AreaServer.Status.valueOf(area.getStats()));
				gameArea.setServerId(area.getServerId());
				gameArea.setFluencyStatus(AreaServer.FluencyStatus.valueOf(area.getFluencyStatus()));
				gameArea.setOpenTime(area.getOpenTime());
				gameArea.setName(area.getName());

				rst.add(gameArea);
			}else{
				AreaServer.Status stats = AreaServer.Status.valueOf(area.getStats());
				//更新
				//已经开放的服务器不能修改开服时间、且不能改为toOpen状态
//				if (gameArea.getStatus() != AreaServer.Status.toOpenForTest
//						&& gameArea.getStatus() != AreaServer.Status.toOpenForPublic) {
//					if (time != gameArea.getOpenTimeLong()) {
//						request.addCallbackParam("已经开放的服务器不能修改开服时间");
//						return;
//					} else if (stats ==AreaServer.Status.toOpenForTest
//							|| stats == AreaServer.Status.toOpenForPublic) {
//						request.addCallbackParam("已经开放的服务器不能改为[待开服]");
//						return;
//					}
//				} else if ((gameArea.getStatus() == AreaServer.Status.toOpenForTest
//						|| gameArea.getStatus() == AreaServer.Status.toOpenForPublic)
//						&& (stats != AreaServer.Status.toOpenForTest && stats != AreaServer.Status.toOpenForPublic)) {
//					request.addCallbackParam("[待开服]状态不能修改，必须等时间到达");
//					return;
//				}

				if(time != gameArea.getOpenTimeLong()){
					if(gameArea.getOpenTimeLong() < System.currentTimeMillis()){
						request.addCallbackParam("已经开放的服务器不能修改开服时间");
						return;
					}
				}

				gameArea.setKeyword(area.getKeyword());
				gameArea.setLimitIps(area.getLimitIps());
				gameArea.setStatus(AreaServer.Status.valueOf(area.getStats()));
				gameArea.setServerId(area.getServerId());
				gameArea.setFluencyStatus(AreaServer.FluencyStatus.valueOf(area.getFluencyStatus()));
				gameArea.setOpenTime(area.getOpenTime());
				gameArea.setName(area.getName());

				rst.add(gameArea);
			}
		}

		GameAreaAssistant.updateBatch(rst);
		request.addCallbackParam("success");
	}
	@MessageMethod(description = "区服删除", invokeType = MethodInvokeType.async)
	private static void areaDelete(ServerCommandRequest request, CommandRequestParams params) {
		String areaIdsStr = params.getParam(0);
		int[] numbers = StringUtil.splitToIntArray(areaIdsStr, ",");
		for(int number : numbers){
			GameArea gameArea = GameAreaManager.getInstance().getServers().get(number);
			if(gameArea != null) {
				GameAreaManager.getInstance().getServers().remove(number);
				GameAreaAssistant.deleteArea(gameArea);
			}
		}
		request.addCallbackParam("success");
	}

	@MessageMethod(description = "区服列表", invokeType = MethodInvokeType.async)
	private static void areaListByPage(ServerCommandRequest request, CommandRequestParams params) {
		int pageIndex = params.getParam(0);
		int pageSize = params.getParam(1);

		List<GameArea> areas = GameAreaAssistant.getServerByPage(pageIndex, pageSize);
		List<GmtGameArea> rst = new ArrayList<>();
		areas.forEach(area -> rst.add(area.toGmtGameArea()));
		request.addCallbackParam(rst);
		request.addCallbackParam(GameAreaManager.getInstance().getServers().size());
	}

	@MessageMethod(description = "网关列表", invokeType = MethodInvokeType.async)
	private static void gateListByPage(ServerCommandRequest request, CommandRequestParams params) {
		int pageIndex = params.getParam(0);
		int pageSize = params.getParam(1);

		pageIndex = pageIndex - 1;
		if(pageIndex < 0){
			pageIndex = 0;
		}

		List<Gate> areas = GateAssistant.getServerByPage(pageIndex, pageSize);
		List<GmtGate> rst = new ArrayList<>();
		areas.forEach(area -> {
			GmtGate json = area.toGmtGate();
			Gate gate = GateManager.getInstance().getGate(area.getId());
			if(gate != null){
				json.setOnline(gate.getOnline());
				json.setActive(gate.isActive());
			}
			rst.add(json);
		});
		request.addCallbackParam(rst);
		request.addCallbackParam(GateManager.getInstance().getGates().size());
	}

	@MessageMethod(description = "区服删除", invokeType = MethodInvokeType.async)
	private static void gateDelete(ServerCommandRequest request, CommandRequestParams params) {
		String areaIdsStr = params.getParam(0);
		int[] numbers = StringUtil.splitToIntArray(areaIdsStr, ",");
		for(int number : numbers){
			Gate gate = GateAssistant.find(number);
			if(gate != null) {
				GateAssistant.deleteArea(gate);
			}
		}
		GateManager.updateFromDb();
		request.addCallbackParam("success");
	}

	@MessageMethod(description = "区服添加或更新", invokeType = MethodInvokeType.async)
	private static void gatesAddOrUpdate(ServerCommandRequest request, CommandRequestParams params) {
		List<GmtGate> areas = params.getParam(0);
		List<Gate> rst = new ArrayList<>();
		for(GmtGate gmtGate : areas){
			Gate gate = GateAssistant.find(gmtGate.getId());
			if(gate == null){
				//新增
				gate = new Gate();
				gate.setId(gmtGate.getId());
				gate.setIp(gmtGate.getIp());
				gate.setPort(gmtGate.getPort());

				rst.add(gate);
			}else{
				gate.setIp(gmtGate.getIp());
				gate.setPort(gmtGate.getPort());

				rst.add(gate);
			}
		}

		GateAssistant.updateBatch(rst);
		request.addCallbackParam("success");
	}

	@MessageMethod(description = "区服列表根据id", invokeType = MethodInvokeType.async)
	private static void areaListById(ServerCommandRequest request, CommandRequestParams params) {
		List<Integer> serverIds = params.getParam(0);

		List<GameArea> serverList = GameAreaAssistant.getServerList();
		List<GmtGameArea> rst = new ArrayList<>();
		for (GameArea gameArea : serverList) {
			if(serverIds.contains(gameArea.getServerId())){
				rst.add(gameArea.toGmtGameArea());
			}
		}
		request.addCallbackParam(rst);
		request.addCallbackParam(GameAreaManager.getInstance().getServers().size());
	}
}
