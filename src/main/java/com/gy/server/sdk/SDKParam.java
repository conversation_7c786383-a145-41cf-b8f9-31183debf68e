package com.gy.server.sdk;

import java.io.FileNotFoundException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Table;
import com.gy.server.Configuration;
import com.gy.server.utils.CollectionUtil;
import com.gy.server.utils.XmlUtil;
import com.gy.server.web.log.LoggerManager;

import org.apache.commons.lang3.ArrayUtils;
import org.dom4j.Element;

/**
 * SDK参数类，管理account和pay的参数
 *
 * <AUTHOR> - [Created on 2018/9/11 18:20]
 */
public class SDKParam {

    /**
     * 账号参数，row：gameId，column：accountType，value：参数列表
     */
    private static Table<String, Integer, List<String>> accountParamTables = HashBasedTable.create();

    /**
     * 支付参数，row：gameId，column：channel，value：参数列表
     */
    private static Table<String, String, List<String>> payParamTables = HashBasedTable.create();


    public static void initParams() throws Exception {
        String messageFileName = "sdk.xml";
        InputStream inputStream = SDKParam.class.getClassLoader().getResourceAsStream(messageFileName);
        if (inputStream == null) {
            LoggerManager.error(new FileNotFoundException("file not found -> " + messageFileName));
            return;
        }
        Element rootElement = XmlUtil.loadDocumentElement(inputStream);
        Element[] gameElements = XmlUtil.getChildren(rootElement, "game");
        for (Element gameElement : gameElements) {
            String gameId = XmlUtil.getAttribute(gameElement, "id");

            //login
            Element loginElement = XmlUtil.getChild(gameElement, "accounts");
            for (Element element : XmlUtil.getChildren(loginElement, "account")) {
                Integer loginCheckType = XmlUtil.getChildValue(element, "loginCheckType", Integer::parseInt);
                String[] paramArray = XmlUtil.getChildrenText(element, "param");
                if (ArrayUtils.isNotEmpty(paramArray)) {
                    accountParamTables.put(gameId, loginCheckType, CollectionUtil.toCollection(paramArray, ArrayList::new));
                }
            }

            //pay
            Element payElement = XmlUtil.getChild(gameElement, "pays");
            for (Element element : XmlUtil.getChildren(payElement, "pay")) {
                String channel = XmlUtil.getChildText(element, "channel");
                String[] paramArray = XmlUtil.getChildrenText(element, "param");
                if (ArrayUtils.isNotEmpty(paramArray)) {
                    payParamTables.put(gameId, channel, CollectionUtil.toCollection(paramArray, ArrayList::new));
                }
            }
        }
    }

    public static String getAccountParam(int accountType, int index) {
        String gameId = Configuration.getGameId();
        if (gameId == null || !accountParamTables.containsRow(gameId)) {
            throw new IllegalArgumentException(String.format("get account param gameId error, gameId -> %s, accountType -> %s, index -> %s", gameId, accountType, index));
        }
        List<String> params = accountParamTables.get(gameId, accountType);

        if (params == null || index < 0 || index >= params.size()) {
            throw new IllegalArgumentException(String.format("get account param index error, gameId -> %s, accountType -> %s, index -> %s, params -> %s", gameId, accountType, index, params));
        }
        return params.get(index);
    }

    public static String getPayParam(String channel, int index) {
        String gameId = Configuration.getGameId();
        if (gameId == null || !payParamTables.containsRow(gameId)) {
            throw new IllegalArgumentException(String.format("get pay param gameId error, gameId -> %s, channel -> %s, index -> %s", gameId, channel, index));
        }
        List<String> params = payParamTables.get(gameId, channel);

        if (params == null || index < 0 || index >= params.size()) {
            throw new IllegalArgumentException(String.format("get pay param index error, gameId -> %s, channel -> %s, index -> %s, params -> %s", gameId, channel, index, params));
        }
        return params.get(index);
    }
}
