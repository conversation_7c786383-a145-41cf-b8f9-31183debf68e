package com.gy.server;

import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

import com.gy.server.util.ServerConstants;
import com.gy.server.utils.function.Ticker;
import com.gy.server.utils.structure.TickerWatcher;
import com.gy.server.web.log.LoggerManager;


/* 
 * <AUTHOR> 
 * @date 2019年9月7日 下午2:30:22 
 */
public abstract class MainThread extends Thread {


    protected static volatile boolean running = true;
    protected static volatile boolean shutdown = false;

    private long lastTickTimeMillis = ServerConstants.getCurrentTimeMillis(); //上个游戏帧的时间点(毫秒)


    private final List<Ticker> tickersList = new CopyOnWriteArrayList<>();

    public MainThread() {
        this.setName(this.getClass().getName());
    }


    protected void addTicker(Ticker... tickers) {
        for (Ticker ticker : tickers) {
            if (!tickersList.contains(ticker)) {
            	tickersList.add(ticker);
            }
        }
    }

    private long foreachTickers(List<Ticker> tickers) {
        long millis = ServerConstants.getCurrentTimeMillis();
        for (Ticker ticker : tickers) {
            try {
                long start = System.nanoTime();
                TickerWatcher.setOn(ticker.getClass().getName());
                ticker.tick();
                TickerWatcher.setOff(ticker.getClass().getName());
                ElapsedTimeStatistics.addElapsedNanoTime(ticker.getClass().getName(), System.nanoTime() - start);
            } catch (Throwable e) {
                LoggerManager.error(e);
            }
        }
        return ServerConstants.getCurrentTimeMillis() - millis;
    }


    private void tick() {
        long costTime = foreachTickers(tickersList);
        if (costTime > 4L) {
            LoggerManager.warn(String.format("billing tick too long -> (%s)", costTime));
        }
    }

    @Override
    public void run() {
        while (running) {
            try {
                long currentTickTimeMillis = ServerConstants.getCurrentTimeMillis();
                if (currentTickTimeMillis - lastTickTimeMillis > ServerConstants.PERIOD_SLOW_MILLIS) {
                    lastTickTimeMillis = currentTickTimeMillis;
                    tick();
                }

                long sleep = currentTickTimeMillis + ServerConstants.PERIOD_FAST_MILLIS - ServerConstants.getCurrentTimeMillis();
                if (sleep >= 0) {
                    Thread.sleep(sleep);
                }
            } catch (InterruptedException e) {
                LoggerManager.error(e);
            }
        }

        shutdown = true;
    }

    protected abstract void init() throws Exception;

    protected abstract void startup() throws Exception;

    protected abstract void shutdown() throws Exception;


}
