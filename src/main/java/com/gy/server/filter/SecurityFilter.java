package com.gy.server.filter;

import java.io.IOException;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

public class SecurityFilter implements Filter {

	@Override
	public void destroy() {
		
	}

	@Override
	public void doFilter(ServletRequest arg0, ServletResponse arg1, Filter<PERSON>hain arg2) throws IOException,
            ServletException {
		
		HttpServletRequest req = (HttpServletRequest) arg0;
	    HttpServletResponse res = (HttpServletResponse) arg1;

	    if(!req.getRequestURI().endsWith("web/login.jsp")) {
	    	HttpSession session = req.getSession();
		    if (session.getAttribute("user") != null) {
		    	arg2.doFilter(arg0, arg1);
		    } else {
		        res.sendRedirect(req.getContextPath() + "/web/login.jsp");
		    }
	    }else {
	    	arg2.doFilter(arg0, arg1);
	    }
	    
	}

	@Override
	public void init(FilterConfig arg0) throws ServletException {
		
	}

}
