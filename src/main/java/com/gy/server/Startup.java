package com.gy.server;

import java.util.Properties;

import com.gy.server.db.relation.hibernate.HibernateUtil;
import com.gy.server.message.MessageLanguage;
import com.gy.server.web.db.HibernateMappingClass;
import com.ttlike.server.tl.baselib.CommonsConfiguration;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;


/**
 * 项目入口
 *
 * <AUTHOR>
 */
public class Startup extends AbstractStartup {

    BillingServer bill = new BillingServer();

    @Override
    protected void initConfiguration() throws Exception {
        Configuration.init();
    }

    @Override
    protected void init() throws Exception {
        bill.init();
    }

    @Override
    protected void startup() throws Exception {
        bill.start();
    }

    @Override
    protected void shutdown() throws Exception {
        TLBase.getInstance().preShutdown();
        bill.shutdown();
    }

    @Override
    protected Properties getHibernateSessionProperties() throws Exception {
        return Configuration.hibernateSessionProperties;
    }

    @Override
    protected MessageLanguage getSystemMessageLanguage() throws Exception {
        // TODO Auto-generated method stub
        return null;
    }

    @Override
    protected String getCurrentGameId() {
        return Configuration.gameId;
    }

    @Override
    protected boolean isTest() {
        return CommonsConfiguration.runMode.isTest();
    }

    @Override
    protected boolean isOpenActiveCode() {
        return Configuration.openActiveCode;
    }

    @Override
    protected void initDb() throws Exception {
        //初始化hibernate
        String hibernateConfigPath = this.getClass().getClassLoader().getResource("hibernate.cfg.xml").getPath();
        Properties hibernateSessionProperties = this.getHibernateSessionProperties();
        HibernateUtil.initWithMappingClasses(hibernateConfigPath, hibernateSessionProperties, ((HibernateMappingClass) this).getHibernateMappingClasses());

        //初始化TLBase
        TLBase.getInstance().init(-1, ServerType.BILLING, "billing", "tomcat", null, null);
    }
}
