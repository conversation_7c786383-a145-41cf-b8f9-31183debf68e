<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE hibernate-configuration PUBLIC
        "-//Hibernate/Hibernate Configuration DTD 3.0//EN"
        "http://hibernate.sourceforge.net/hibernate-configuration-3.0.dtd">
<hibernate-configuration>

    <session-factory>
        <property name="hibernate.connection.driver_class">com.mysql.jdbc.Driver</property>
        <property name="hibernate.dialect">org.hibernate.dialect.MySQL5Dialect</property>
        <!--请务必确认此处db的ip、端口以及实例名-->
        <property name="hibernate.connection.pool.size">100</property>
        <property name="hibernate.show_sql">false</property>
        <property name="hibernate.connection.provider_class">org.hibernate.connection.C3P0ConnectionProvider</property>
        <property name="hibernate.c3p0.max_size">100</property>
        <property name="hibernate.c3p0.min_size">5</property>
        <property name="hibernate.c3p0.timeout">120</property>
        <property name="hibernate.c3p0.max_statements">100</property>
        <property name="hibernate.c3p0.idle_test_period">120</property>
        <property name="hibernate.c3p0.acquire_increment">1</property>
        <property name="hibernate.jdbc.fetch_size">200</property>
        <property name="hibernate.jdbc.batch_size">200</property>
        <property name="hibernate.hibernate.cache.use_second_level_cache">false</property>
        <property name="hibernate.current_session_context_class">thread</property>
        <property name="hibernate.generate_statistics">false</property>

        <!--<property name="show_sql">true</property>
        <property name="format_sql">true</property>-->

        <!-- <mapping class="com.gy.server.item.ItemData"/> -->


    </session-factory>

</hibernate-configuration>