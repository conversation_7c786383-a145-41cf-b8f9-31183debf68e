<?xml version="1.0" encoding="UTF-8"?>
<!--级别:TRACE > DEBUG > INFO > WARN > ERROR > FATAL-->
<Configuration status="WARN">
    <Properties>
        <Property name="path_log">${web:rootDir}/WEB-INF/log/ttlkGame</Property>

        <Property name="date_suffix">%d{yyyy-MM-dd}</Property>
        <Property name="extension">.log</Property>

        <Property name="file_billing">billing</Property>
        <Property name="file_login">login</Property>
        <Property name="file_pay">pay</Property>
    </Properties>

    <Loggers>

        <logger name="org.hibernate" level="warn"/>
        <logger name="net.sf.ehcache" level="warn"/>
        <logger name="io.netty" level="warn"/>
        <logger name="com.mchange" level="warn"/>
        <logger name="org.redisson" level="warn" />


        <!--additivity,boolean,用来指定输出添加关系,子Logger的输出是否需要输出到父Logger中-->
        <Root level="info" includeLocation="false" additivity="true">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="Billing"/>
        </Root>

        <AsyncLogger name="Login" level="info" includeLocation="false" additivity="true">
            <AppenderRef ref="Login"/>
        </AsyncLogger>

        <AsyncLogger name="Pay" level="info" includeLocation="false" additivity="true">
            <AppenderRef ref="Pay"/>
        </AsyncLogger>

    </Loggers>

    <Appenders>
        <!--log格式采用'|'为分列符号,方便日志分析程序进行分列处理,并且带时区,用以跨地区日志分析使用-->
        <Console name="Console">
            <PatternLayout charset="UTF-8" pattern="%-5p|%d{yyyy-MM-dd HH:mm:ss,SSS Z}|%-6c|%m%n"/>
        </Console>

        <RollingRandomAccessFile name="Billing"
                                 immediateFlush="false"
                                 fileName="${path_log}/${file_billing}${extension}"
                                 filePattern="${path_log}/${file_billing}-${date_suffix}${extension}">
            <PatternLayout charset="UTF-8" pattern="%-5p|%d{yyyy-MM-dd HH:mm:ss,SSS Z}|%-6c|%m%n"/>
            <Policies>
                <CronTriggeringPolicy schedule="0 0 0 * * ?" evaluateOnStartup="true"/>
            </Policies>
        </RollingRandomAccessFile>

        <RollingRandomAccessFile name="Login"
                                 immediateFlush="false"
                                 fileName="${path_log}/${file_login}${extension}"
                                 filePattern="${path_log}/${file_login}-${date_suffix}${extension}">
            <PatternLayout charset="UTF-8" pattern="%-5p|%d{yyyy-MM-dd HH:mm:ss,SSS Z}|%-6c|%m%n"/>
            <Policies>
                <CronTriggeringPolicy schedule="0 0 0 * * ?" evaluateOnStartup="true"/>
            </Policies>
        </RollingRandomAccessFile>

        <RollingRandomAccessFile name="Pay"
                                 immediateFlush="false"
                                 fileName="${path_log}/${file_pay}${extension}"
                                 filePattern="${path_log}/${file_pay}-${date_suffix}${extension}">
            <PatternLayout charset="UTF-8" pattern="%-5p|%d{yyyy-MM-dd HH:mm:ss,SSS Z}|%-6c|%m%n"/>
            <Policies>
                <CronTriggeringPolicy schedule="0 0 0 * * ?" evaluateOnStartup="true"/>
            </Policies>
        </RollingRandomAccessFile>

    </Appenders>
</Configuration>