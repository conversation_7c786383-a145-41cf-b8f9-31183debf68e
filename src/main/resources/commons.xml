<?xml version='1.0' encoding='UTF-8'?>
<config>
	<!-- 代码同时支持原来消息系统和游戏数据共用集群的配置方式，若干不需要拆分，则无需修改原有配置方式 -->
	<!--运行模式，TEST：测试模式；LIVE：正式模式；PRESS：压测模式；CHECK：送审模式-->
	<runMode>TEST</runMode>

	<!--环境名字，同一套环境内所有节点一样，不同环境名字不能重复，否则会造成cos、es、nats等数据冲突-->
	<envName>hxTest</envName>
	<!--单机模式-->
	<redis-single>true</redis-single>

	<!-- 消息系统集群，可以和数据集群配置相同，共用集群 -->
	<redis-msg>
		<nodes>
			<node ip="127.0.0.1" port="6399"/>
		</nodes>

		<!-- Redis 集群用户名 -->
		<redisUser>root</redisUser>

		<!-- Redis 集群密码 -->
		<redisPwd>123456</redisPwd>
	</redis-msg>

	<!-- 游戏数据集群 -->
	<redis-data>
		<nodes>
			<node ip="127.0.0.1" port="6399"/>
		</nodes>

		<!-- Redis 集群用户名 -->
		<redisUser>root</redisUser>

		<!-- Redis 集群密码 -->
		<redisPwd>123456</redisPwd>
	</redis-data>

	<!-- NATS服务器连接信息 -->
	<nats>
		<url>nats://tl.shangua.com:4022</url>
		<user>admin</user>
		<password>password123</password>
	</nats>

</config>