/*
Navicat MySQL Data Transfer

Source Server         : localhost
Source Server Version : 50721
Source Host           : localhost:3306
Source Database       : billing-bt

Target Server Type    : MYSQL
Target Server Version : 50721
File Encoding         : 65001

Date: 2019-03-07 11:03:38
*/

SET FOREIGN_KEY_CHECKS=0;

-- ----------------------------
-- Table structure for `account`
-- ----------------------------
DROP TABLE IF EXISTS `account`;
CREATE TABLE `account` (
  `id` varchar(50) NOT NULL,
  `account_id` varchar(50) DEFAULT NULL,
  `password` varchar(50) DEFAULT NULL,
  `mail` varchar(50) DEFAULT NULL,
  `uuid` varchar(50) DEFAULT NULL,
  `create_time` timestamp NULL DEFAULT NULL,
  `update_time` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_accountId` (`account_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Records of account
-- ----------------------------

-- ----------------------------
-- Table structure for `cdkey`
-- ----------------------------
DROP TABLE IF EXISTS `cdkey`;
CREATE TABLE `cdkey`(
    `key_id` varchar(20) NOT NULL DEFAULT '' COMMENT 'CD-KEY',
    `max_use_count` int(11) DEFAULT NULL COMMENT '最大生效次数',
    `used_count` int(11) DEFAULT NULL COMMENT '已生效次数',
    `group_id` int(11) NOT NULL COMMENT '所属组别',
    PRIMARY KEY (`key_id`),
    KEY             `idx_groupId` (`group_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Records of cdkey
-- ----------------------------

-- ----------------------------
-- Table structure for `cdkey_group`
-- ----------------------------
DROP TABLE IF EXISTS `cdkey_group`;
CREATE TABLE `cdkey_group` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `key_desc` varchar(255) DEFAULT NULL COMMENT '备注说明',
  `rewards` varchar(255) DEFAULT NULL COMMENT '掉落道具格式：type|id|count  0|504|1',
  `reward_info` varchar(255) DEFAULT NULL COMMENT '掉落道具中文信息',
  `key_type` int(4) DEFAULT NULL COMMENT '-1表示该类型一个玩家可以领多次，其他类型表示一个玩家只能领取该类型一次',
  `start_time` datetime DEFAULT NULL COMMENT '生效时间',
  `end_time` datetime DEFAULT NULL COMMENT '截止日期',
  `channel` varchar(255) DEFAULT NULL COMMENT '渠道',
  `channel_name` varchar(255) DEFAULT NULL COMMENT '渠道名',
  `level` int(4) DEFAULT NULL COMMENT '等级',
  `register_start_time` datetime DEFAULT NULL COMMENT '角色创建开始时间',
  `register_end_time` datetime DEFAULT NULL COMMENT '角色创建结束时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Records of cdkey_group
-- ----------------------------

-- ----------------------------
-- Table structure for `channel`
-- ----------------------------
DROP TABLE IF EXISTS `channel`;
CREATE TABLE `channel` (
  `channel_id` varchar(50) NOT NULL COMMENT '渠道ID',
  `channel_name` varchar(255) DEFAULT NULL COMMENT '渠道名称',
  PRIMARY KEY (`channel_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Records of channel
-- ----------------------------
TRUNCATE TABLE channel;
INSERT INTO `channel` VALUES ('1234', 'ttlk');
INSERT INTO `channel` VALUES ('2001', '畅游IOS');
INSERT INTO `channel` VALUES ('4001', '畅游安卓');
INSERT INTO `channel` VALUES ('3116', 'b站');
INSERT INTO `channel` VALUES ('3004', '小米');

-- ----------------------------
-- Table structure for `login`
-- ----------------------------
DROP TABLE IF EXISTS `login`;
CREATE TABLE `login` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `account_type` int(11) DEFAULT NULL,
  `account_id` varchar(250) DEFAULT NULL,
  `login_token` varchar(50) DEFAULT NULL,
  `role_info` text,
  `update_time` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_accountId_accountType` (`account_type`,`account_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Records of login
-- ----------------------------

-- ----------------------------
-- Table structure for `receipt`
-- ----------------------------
DROP TABLE IF EXISTS `receipt`;
CREATE TABLE `receipt` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_id` varchar(100) NOT NULL,
  `co_order_id` varchar(100) NOT NULL,
  `player_id` bigint(20) NOT NULL,
  `account_id` varchar(250) NOT NULL,
  `channel` varchar(100) NOT NULL,
  `goods_id` int(11) NOT NULL,
  `co_goods_id` varchar(100) NOT NULL,
  `price` int(11) NOT NULL,
  `status` varchar(50) NOT NULL,
  `gold_count` int(11) NOT NULL,
  `server_num` int(11) NOT NULL,
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `extend_text` text,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_orderId` (`order_id`) USING BTREE,
  UNIQUE KEY `uk_coOrderId_channel` (`co_order_id`,`channel`),
  KEY `idx_status_serverNum` (`status`,`server_num`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Records of receipt
-- ----------------------------

-- ----------------------------
-- Table structure for `token`
-- ----------------------------
DROP TABLE IF EXISTS `token`;
CREATE TABLE `token` (
  `account_id` varchar(250) NOT NULL,
  `token` varchar(50) DEFAULT NULL,
  `create_time` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`account_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Records of token
-- ----------------------------

-- ----------------------------
-- Table structure for `user`
-- ----------------------------
DROP TABLE IF EXISTS `user`;
CREATE TABLE `user` (
  `user_name` varchar(50) NOT NULL,
  `password` varchar(50) DEFAULT NULL,
  `authority` varchar(255) DEFAULT NULL,
  `authority_ids` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`user_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Records of user
-- ----------------------------


-- ----------------------------
-- Table structure for `language_pack`
-- ----------------------------
DROP TABLE IF EXISTS `language_pack`;
CREATE TABLE `language_pack` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `language` int(11) DEFAULT NULL,
  `version` varchar(100) DEFAULT NULL,
  `url` varchar(255) DEFAULT NULL,
  `md5` varchar(255) DEFAULT NULL,
  `size` int(11) DEFAULT NULL,
  `decompress_size` int(11) DEFAULT NULL,
  `platform` varchar(100) DEFAULT NULL,
  `limit_ips` text,
  `time` timestamp NULL DEFAULT NULL,
  `init_version_include` text,
  `init_version_exclude` text,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_language` (`language`,`version`,`platform`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Records of language_pack
-- ----------------------------

-- ----------------------------
-- Table structure for `maintain`
-- ----------------------------
DROP TABLE IF EXISTS `maintain`;
CREATE TABLE `maintain` (
  `maintain_id` int(11) NOT NULL AUTO_INCREMENT,
  `order_id` int(11) DEFAULT NULL,
  `channels` text,
  `languages` varchar(255) DEFAULT NULL,
  `titles` text,
  `contents` text,
  `default_language` varchar(10) DEFAULT NULL,
  `start_time` timestamp NULL DEFAULT NULL,
  `end_time` timestamp NULL DEFAULT NULL,
  `inscription` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`maintain_id`)
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Records of maintain
-- ----------------------------

-- ----------------------------
-- Table structure for `notice`
-- ----------------------------
DROP TABLE IF EXISTS `notice`;
CREATE TABLE `notice` (
  `notice_id` INT(11) NOT NULL AUTO_INCREMENT,
  `order_id` INT(11) DEFAULT NULL,
  `channels` TEXT,
  `platform_channel` text,
  `languages` VARCHAR(255) DEFAULT NULL,
  `titles` TEXT,
  `contents` TEXT,
  `default_language` VARCHAR(10) DEFAULT NULL,
  `tab_names` TEXT,
  `tab_tags` TEXT,
  `start_time` TIMESTAMP NULL DEFAULT NULL,
  `end_time` TIMESTAMP NULL DEFAULT NULL,
  `is_set` VARCHAR(255) DEFAULT NULL,
  `type` VARCHAR(255) DEFAULT NULL,
  PRIMARY KEY (`notice_id`)
) ENGINE=INNODB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Records of notice
-- ----------------------------

-- ----------------------------
-- Table structure for `server`
-- ----------------------------
DROP TABLE IF EXISTS `server`;
CREATE TABLE `server` (
    `server_num`     int(11) NOT NULL,
    `server_id`      int(50) DEFAULT NULL,
    `name`           varchar(50) DEFAULT NULL,
    `ip`             varchar(50) DEFAULT NULL,
    `port`           int(11) DEFAULT NULL,
    `fluency_status` varchar(50) DEFAULT NULL,
    `open_time`      varchar(50) DEFAULT NULL,
    `status`         varchar(50) DEFAULT NULL,
    `keyword`        varchar(50) DEFAULT NULL,
    `limit_ips`      text,
    PRIMARY KEY (`server_num`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Records of server
-- ----------------------------

-- ----------------------------
-- Table structure for `version`
-- ----------------------------
DROP TABLE IF EXISTS `version`;
CREATE TABLE `version` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` int(11) DEFAULT NULL,
  `version` varchar(100) DEFAULT NULL,
  `url` text,
  `md5` varchar(100) DEFAULT NULL,
  `size` int(11) DEFAULT NULL,
  `decompress_size` int(11) DEFAULT NULL,
  `platform` varchar(100) DEFAULT NULL,
  `limit_ips` text,
  `force_update` tinyint(1) DEFAULT NULL,
  `time` timestamp NULL DEFAULT NULL,
  `init_version_include` text,
  `init_version_exclude` text,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_version` (`version`,`platform`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Records of version
-- ----------------------------


-- ----------------------------
-- Table structure for `version`
-- ----------------------------
CREATE TABLE `sql_version` (
   `version` int(11) NOT NULL COMMENT '版本号',
   `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
   `full` bit(1) NOT NULL COMMENT '是否完整版本',
   `pre_version` int(11) DEFAULT NULL COMMENT '前置版本，非完整版本时必填',
   PRIMARY KEY (`version`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;


-- ----------------------------
-- Table structure for `gate`
-- ----------------------------
DROP TABLE IF EXISTS `gate`;
CREATE TABLE `gate` (
                        `server_id` int(50) NOT NULL,
                        `ip` varchar(50) DEFAULT NULL,
                        `port` int(11) DEFAULT NULL,
                        PRIMARY KEY (`server_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- ----------------------------
-- Records of version
-- ----------------------------

-- version
INSERT INTO `sql_version` VALUES (1, now(), 1, 0);


INSERT INTO `user` VALUES ('admin', '90D943A0C989AC93', '用户信息', 'user');