/**
 * 服务器选择列表（单选）
 */
function selectServer(e) {
    var btnEdit = this;
    mini.open({
        url: "web/server/server_list.jsp",
        title: "服务器选择列表（单选）",
        width: 550,
        height: 380,
        allowResize: false,
        ondestroy: function (action) {
            //if (action == "close") return false;
            if (action === "ok") {
                var iframe = this.getIFrameEl();
                var data = iframe.contentWindow.getSelectData();
                data = mini.clone(data);    //必须
                if (data) {
                    btnEdit.setValue(data.id);
                    btnEdit.setText(data.name);
                }
            }
        }
    });
}


/**
 * 服务器选择列表（多选）
 */
function selectServerMulti(e) {
    var btnEdit = this;
    mini.open({
        url: "web/server/server_list_multi.jsp",
        title: "服务器选择列表（多选）",
        width: 550,
        height: 380,
        allowResize: false,
        ondestroy: function (action) {
            //if (action == "close") return false;
            if (action === "ok") {
                var iframe = this.getIFrameEl();
                var data = iframe.contentWindow.getSelectData();
                data = mini.clone(data);    //必须
                if (data) {
                    btnEdit.setValue(data.id);
                    btnEdit.setText(data.name);
                }
            }

        }
    });
}

/**
 * 渠道选择列表（多选）
 */
function selectChannelMulti() {
    var btnEdit = this;
    mini.open({
        url: "web/channel/channel_list.jsp",
        title: "渠道选择列表（多选）",
        width: 550,
        height: 500,
        allowResize: false,
        ondestroy: function (action) {
            if (action === "ok") {
                var iframe = this.getIFrameEl();
                var data = iframe.contentWindow.getSelectData();
                data = mini.clone(data);    //必须
                if (data) {
                    btnEdit.setValue(data.id);
                    btnEdit.setText(data.name);
                }
            }

        }
    });

}

/**
 * 添加物品
 */
function addItem() {
    //清空Div里的内容
    mini.get("itemInfo").setValue("");

    mini.open({
        url : "web/item/item_list.jsp",
        title : "添加物品",
        width : 1370,
        height : 700,
        allowResize : false,
        ondestroy : function(action) {
            if (action === "ok") {
                var iframe = this.getIFrameEl();
                var data = iframe.contentWindow.getItemData();
                data = mini.clone(data); //必须
                if (data) {
                    rewardStr = data.rewardStr;
                    mini.get("itemInfo").setValue(data.currencyStr + data.itemStr);
                }
            }
        }
    });
}

/**
 * 服务器状态
 */
function getServerStatus() {
    return [
        {"id": "close", "text": "关闭"},
        {"id": "newOpen", "text": "新服"},
        {"id": "hot", "text": "火爆"},
        {"id": "maintain", "text": "维护"},
        {"id": "recommend", "text": "推荐"},
        {"id": "limit", "text": "白名单"},
    ];
}
