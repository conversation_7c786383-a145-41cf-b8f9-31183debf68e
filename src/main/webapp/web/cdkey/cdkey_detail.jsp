<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%
    String path = request.getContextPath();
    String basePath = request.getScheme()
            + "://" + request.getServerName()
            + ":" + request.getServerPort()
            + path + "/";
%>
<html>
<head>
    <title>cdkey详细信息</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <base href="<%=basePath%>">

    <script type="text/javascript" src="${pageContext.request.contextPath}/js/import.js"></script>
</head>
<body>
    <div id="keyDetailList" class="mini-datagrid" style="width: 100%; height: 560px;" url="cdkey_detail"  allowResize="false" pageSize="20" allowSortColumn="false"
         allowResizeColumn="false" allowCellEdit="false" allowCellSelect="false" multiSelect="true" allowMoveColumn="false">
        <div property="columns">
            <div name="id" field="id" field="kkey" width="5%" headerAlign="center" align="center">
                CD-KEY</div>
            <div field="maxUseCount" width="5%" headerAlign="center" align="center">
                最大兑换次数</div>
            <div field="usedCount" width="5%" headerAlign="center" align="center">
                已使用次数</div>
        </div>
    </div>

    <script type="text/javascript">
        mini.parse();
        var grid = mini.get("keyDetailList");
        var groupId ;
        function SetData(id) {
            groupId = mini.clone(id);
            grid.load({groupId: id});
        }
    </script>
</body>
</html>