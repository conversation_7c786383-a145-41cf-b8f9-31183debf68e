<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%
    String path = request.getContextPath();
    String basePath = request.getScheme()
            + "://" + request.getServerName()
            + ":" + request.getServerPort()
            + path + "/";
%>
<html>
<head>
    <title>cdkey列表</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <base href="<%=basePath%>">

    <script type="text/javascript" src="${pageContext.request.contextPath}/js/import.js"></script>
</head>
<body>
<div style="width: 100%;">
    <div class="mini-toolbar" style="border-bottom: 0; padding: 0px;">
        <table style="width: 100%;">
            <tr>
                <td style="white-space:nowrap;">
                    <a class="mini-button" iconCls="icon-addnew" onclick="add()">新增</a>
                    <a class="mini-button" iconCls="icon-edit" onclick="edit()">编辑</a>
                    <a class="mini-button" iconCls="icon-remove" onclick="remove()">删除</a>
                </td>

            </tr>
        </table>
    </div>
</div>
<div id="cdkeyList" class="mini-datagrid" style="width:100%;height:95%;" pageSize="20"
     url="cdkey_list" allowCellEdit="true" allowCellSelect="true" sortMode="client" multiSelect="true">
</div>

<div>
    <!--导出Excel相关HTML-->
    <form id="excelForm" action="cdkey_export" method="post" target="excelIFrame">
        <input type="hidden" name="groupId" id="groupId"/>
    </form>
    <iframe id="excelIFrame" name="excelIFrame" style="display:none;"></iframe>
</div>

<script type="text/javascript">

    mini.parse();

    var grid = mini.get("cdkeyList");
    var columns = [
        {
            headerAlign: "center",
            align: "center",
            allowSort: true,
            width: 50,
            dataType: "int",
            field: "type",
            header: "类型"
        },
        {headerAlign: "center", align: "center", allowSort: true, width: 50, field: "rewardInfo", header: "礼包道具"},
        {
            headerAlign: "center",
            align: "center",
            allowSort: true,
            width: 50,
            field: "startTimeStr",
            header: "生效时间",
            dateFormat: "yyyy-MM-dd HH:mm:ss"
        },
        {
            headerAlign: "center",
            align: "center",
            allowSort: true,
            width: 50,
            field: "endTimeStr",
            header: "截止时间",
            dateFormat: "yyyy-MM-dd HH:mm:ss"
        },
        {headerAlign: "center", align: "center", allowSort: true, width: 50, field: "desc", header: "备注"},
        {headerAlign: "center", align: "center", allowSort: true, width: 50, field: "channelName", header: "使用渠道"},
        {headerAlign: "center", align: "center", allowSort: true, width: 50, field: "level", header: "等级限制"},
        {
            headerAlign: "center",
            align: "center",
            allowSort: true,
            width: 50,
            field: "registerStartTimeStr",
            header: "角色创建开始时间",
            dateFormat: "yyyy-MM-dd HH:mm:ss"
        },
        {
            headerAlign: "center",
            align: "center",
            allowSort: true,
            width: 50,
            field: "registerEndTimeStr",
            header: "角色创建结束时间",
            dateFormat: "yyyy-MM-dd HH:mm:ss"
        },
        {
            headerAlign: "center",
            align: "center",
            allowSort: true,
            width: 50,
            dataType: "int",
            field: "keyCount",
            header: "数量"
        },
        {
            headerAlign: "center",
            align: "center",
            allowSort: true,
            width: 50,
            field: "edit",
            header: "操作",
            renderer: "onActionRenderer",
            cellStyle: "padding:0;"
        }
    ];
    grid.set({columns: columns});
    grid.load();

    /**
     * 修改
     */
    function edit() {
        var row = grid.getSelected();
        if (row) {
            mini.open({
                url: "web/cdkey/cdkey_update.jsp",
                title: "修改Cdkey",
                width: 600,
                height: 630,
                allowResize: false,
                onload: function () {
                    var iframe = this.getIFrameEl();
                    var data = {
                        action: "edit",
                        id: row.id,
                        type: row.type,
                        rewardInfo: row.rewardInfo,
                        rewards: row.rewards,
                        startTime: row.startTime,
                        endTime: row.endTime,
                        desc: row.desc,
                        channel: row.channel,
                        level: row.level,
                        channelName: row.channelName,
                        registerStartTime: row.registerStartTime,
                        registerEndTime: row.registerEndTime
                    };
                    iframe.contentWindow.setData(data);
                },
                ondestroy: function (action) {
                    grid.reload();

                }
            });
        } else {
            alert("请选中一条信息");
        }

    }


    /**
     * 新增
     */
    function add() {
        mini.open({
            url: "web/cdkey/cdkey_update.jsp",
            title: "新增服务器",
            width: 600,
            height: 630,
            allowResize: false,
            onload: function () {
                var iframe = this.getIFrameEl();
                var data = {
                    action: "add"
                };
                iframe.contentWindow.setData(data);
            },
            ondestroy: function (action) {
                if (action === "success") {
                    mini.alert("添加成功");
                    grid.reload();
                }
            }
        });
    }

    /**
     * 删除
     */
    function remove() {
        var rows = grid.getSelecteds();
        if (rows) {
            if (!confirm("确定要删除记录吗？")) {
                return;
            }

            var ids = [];
            for (var i = 0; i < rows.length; i++) {
                ids[i] = rows[i].id;
            }

            $.ajax({
                url: "cdkey_remove",
                type: "post",
                data: {
                    ids: ids
                },
                traditional: true,
                success: function (text) {
                    if (text === "success") {
                        mini.alert("删除成功");
                        grid.reload();
                    } else {
                        mini.alert(text);
                    }
                }
            });
        } else {
            mini.alert("请选中一条信息");
        }

    }

    function cdkeyDetail(id) {
        mini.open({
            url: "web/cdkey/cdkey_detail.jsp",
            title: "查看cdkey使用详情",
            width: 600,
            height: 610,
            allowResize: false,
            onload: function () {
                var iframe = this.getIFrameEl();
                iframe.contentWindow.SetData(id);
            },
            ondestroy: function (action) {
                grid.reload();
            }
        });
    }

    function cdkeyExport(id) {
        document.getElementById("groupId").value = id;
        var excelForm = document.getElementById("excelForm");
        excelForm.submit();
    }


    function onActionRenderer(e) {
        var record = e.record;
        var id = record.id;
        var s = '<a class="Edit_Button" href="javascript:cdkeyDetail(\'' + id + '\')" >查看</a>';
        s += '&nbsp; <a class="Edit_Button" href="javascript:cdkeyExport(\'' + id + '\')" >导出</a>';
        return s;
    }

</script>

</body>
</html>