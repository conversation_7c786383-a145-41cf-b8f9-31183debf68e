<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%
    String path = request.getContextPath();
    String basePath = request.getScheme()
            + "://" + request.getServerName()
            + ":" + request.getServerPort()
            + path + "/";
%>
<html>
<head>
    <title>CdKey更新</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <base href="<%=basePath%>">

    <script type="text/javascript" src="${pageContext.request.contextPath}/js/import.js"></script>
</head>
<body>
<div id="sendForm">
    <div id="imessageOK">Thank you! Message Sent!</div>
    <div id="imessageERROR">ERROR: Message Not Sent!</div>
    <input type="hidden" name="id" id="id"/>

    <table style="width: 100%; table-layout: fixed;" border="0">
        <tr>
            <td id="td1" align="right">*类型：</td>
            <td id="td2" colspan="2">
                <input name="type" id="type" class="mini-textbox" allowInput="true" width="70%"
                       emptyText="类型必须为数字" height="100px" required="true"/>
            </td>
        </tr>
        <tr>
            <td id="td3" align="right">*模式：</td>
            <td id="td4" colspan="2">
                <input name="mode" id="mode" class="mini-combobox" allowInput="false" width="70%"
                       textField="text" data="getMode()" value="1" onvaluechanged="loadChange"
                       height="100px" required="true"/>

            </td>
        </tr>
        <tr>
            <td id="td5" align="right">*自定义CdKey(大小写字母和数字最长50)：</td>
            <td id="td6" colspan="2">
                <input name="customKey" id="customKey" class="mini-textbox" allowInput="true" width="70%"
                       height="100px" required="true"/>
            </td>
        </tr>
        <tr>
            <td id="td7" align="right">*最大兑换次数</td>
            <td id="td8" colspan="2">
                <input name="maxUseTime" id="maxUseTime" class="mini-textbox" allowInput="true" width="70%"
                       height="100px" required="true"/>
            </td>
        </tr>
        <tr>
            <td id="td9" align="right">*生成数量(最大50000)</td>
            <td id="td10" colspan="2">
                <input name="count" id="count" class="mini-textbox" allowInput="true" width="70%"
                       height="100px" required="true"/>
            </td>
        </tr>
        <tr>
            <td align="right">*生效日期：</td>
            <td colspan="2">
                <input id="startTime" name="startTime" class="mini-datepicker" style="width:210px;" nullValue="null"
                       format="yyyy-MM-dd HH:mm:ss" timeFormat="HH:mm:ss"
                       showTime="true" showOkButton="true" showClearButton="false" required="true"/>
            </td>
        </tr>
        <tr>
            <td align="right">*截止日期：</td>
            <td colspan="2">
                <input id="endTime" name="endTime" class="mini-datepicker" style="width:210px;" nullValue="null"
                       format="yyyy-MM-dd HH:mm:ss" timeFormat="HH:mm:ss"
                       showTime="true" showOkButton="true" showClearButton="false" required="true"/>
            </td>
        </tr>
        <tr>
            <td align="right">角色创建开始日期</td>
            <td colspan="2">
                <input id="registerStartTime" class="mini-datepicker" style="width:210px;" nullValue="null"
                       format="yyyy-MM-dd HH:mm:ss" timeFormat="HH:mm:ss"
                       showTime="true" showOkButton="true" showClearButton="false"/>
            </td>
        </tr>
        <tr>
            <td align="right">角色创建截止日期</td>
            <td colspan="2">
                <input id="registerEndTime" class="mini-datepicker" style="width:210px;" nullValue="null"
                       format="yyyy-MM-dd HH:mm:ss" timeFormat="HH:mm:ss"
                       showTime="true" showOkButton="true" showClearButton="false"/>
            </td>
        </tr>
        <tr>
            <td align="right">使用渠道</td>
            <td colspan="2">
                <input class="mini-buttonedit" style="width:210px;"
                       name="channel" id="channel" onbuttonclick="selectChannelMulti"
                       allowInput="false"/>
                <input type="hidden" name="channelName" id="channelName"/>
            </td>
        </tr>
        <tr>
            <td align="right">*等级限制：</td>
            <td colspan="2">
                <input name="level" id="level" class="mini-textbox" allowInput="true" width="70%" value="1"
                       height="100px" required="true"/>
            </td>
        </tr>
        <tr>
            <td align="right">备注：</td>
            <td colspan="2">
                <input name="desc" id="desc" class="mini-textbox" allowInput="true" width="70%"
                       height="100px"/>
            </td>
        </tr>
        <tr>
            <td align="right">*礼包内容：</td>
            <td colspan="2">
                <input type="button" class="ibutton" name="submit" value="新增" onclick="addItem()"/>
            </td>
        </tr>
        <tr>
            <td align="right"></td>
            <td colspan="2">
                <input name="itemInfo" id="itemInfo" class="mini-textarea" allowInput="false" style="width:210px;"
                       height="100px"
                       required="true"/>
            </td>
        </tr>
    </table>

    <br>
    <br>
    <button onclick="submitForm()" class="mini-button" style="width:10%;position:fixed;bottom:20px;left: 250px">提交
    </button>
</div>
<%--    <p style="color: red;">使用渠道如果不填则为全部渠道，渠道使用“,”分割，角色创建日期不填则为所有日期</p>--%>


<script type="text/javascript">

    mini.parse();

    //添加物品集合（添加奖励必须添加该集合）
    var rewardStr;
    var action;

    function submitForm() {
        var form = new mini.Form("#sendForm");

        form.validate();
        if (form.isValid() === false) {
            mini.alert("输入有误，请检查");
            return;
        }

        //提交数据
        $.ajax({
            url: "cdkey_update",
            type: "post",
            data: {
                action: action,
                id: $("#id").val(),
                type: mini.get("type").value,
                mode: mini.get("mode").value,
                maxUseTime: mini.get("maxUseTime").value,
                count: mini.get("count").value,
                startTime: mini.get("startTime").getFormValue(),
                endTime: mini.get("endTime").getFormValue(),
                registerStartTime: mini.get("registerStartTime").getFormValue(),
                registerEndTime: mini.get("registerEndTime").getFormValue(),
                rewardStr: rewardStr,
                itemInfo: mini.get("itemInfo").value,
                channel: mini.get("channel").value,
                channelName: mini.get("channel").text,
                level: mini.get("level").value,
                desc: mini.get("desc").value,
                customKey: mini.get("customKey").value

            },
            success: function (text) {
                if (text === "success") {
                    window.CloseOwnerWindow("success");
                } else {
                    mini.alert(text);
                }
            }
        });
    }

    /**
     * 操作类型
     */
    function getMode() {
        return [
            {"id": 1, "text": "生成CdKey"},
            {"id": 2, "text": "自定义CdKey"},
        ];
    }

    function loadChange(e) {
        var mode = mini.get("mode").getValue();

        if (mode === "2") {
            document.getElementById('td5').style.display = '';
            document.getElementById('td6').style.display = '';
            document.getElementById('td9').style.display = 'none';
            document.getElementById('td10').style.display = 'none';
            mini.get("count").setValue(1);
        } else {
            document.getElementById('td5').style.display = 'none';
            document.getElementById('td6').style.display = 'none';
            document.getElementById('td9').style.display = '';
            document.getElementById('td10').style.display = '';
            mini.get("count").setValue("");
            mini.get("customKey").setValue("");
        }
    }

    /**
     * 修改设置数据
     */
    function setData(data) {
        //跨页面传递的数据对象，克隆后才可以安全使用
        var data = mini.clone(data);
        action = data.action;
        if (data.action === "edit") {
            $("#id").val(data.id);
            mini.get("type").setValue(data.type);
            mini.get("mode").setValue(data.mode);
            mini.get("itemInfo").setValue(data.rewardInfo);
            rewardStr = data.rewards;
            mini.get("startTime").setValue(data.startTime);
            mini.get("endTime").setValue(data.endTime);
            mini.get("desc").setValue(data.desc);
            mini.get("channel").setValue(data.channel);
            mini.get("channel").setText(data.channelName);
            mini.get("level").setValue(data.level);
            $("#channelName").val(data.channelName);
            mini.get("registerStartTime").setValue(data.registerStartTime);
            mini.get("registerEndTime").setValue(data.registerEndTime);

            $('#type').hide();
            $('#mode').hide();
            $('#td1').hide();
            $('#td2').hide();
            $('#td3').hide();
            $('#td4').hide();
            $('#td5').hide();
            $('#td6').hide();
            $('#td7').hide();
            $('#td8').hide();
            $('#td9').hide();
            $('#td10').hide();
        } else {
            $('#td5').hide();
            $('#td6').hide();
        }
    }
</script>
</body>
</html>