<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%
    String path = request.getContextPath();
    String basePath = request.getScheme()
            + "://" + request.getServerName()
            + ":" + request.getServerPort()
            + path + "/";
%>
<html>
<head>
    <title>渠道列表</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <base href="<%=basePath%>">

    <script type="text/javascript" src="${pageContext.request.contextPath}/js/import.js"></script>
</head>
<body>
    <div id="channelList" class="mini-datagrid" style="width:100%;height:88%;" pageSize="20"
         url="channel_list" allowCellEdit="true" allowCellSelect="true" sortMode="client" multiSelect="true">
    </div>

    <div class="mini-toolbar" style="text-align:center;padding-top:8px;padding-bottom:8px;" borderStyle="border:0;">
        <a class="mini-button" style="width:15%;" onclick="onOk()">确定</a>
        <span style="display:inline-block;width:25px;"></span>
        <a class="mini-button" style="width:15%;" onclick="onCancel()">取消</a>
    </div>

    <script type="text/javascript">

        mini.parse();

        var grid = mini.get("channelList");
        var columns = [
            { type: "checkcolumn" , width: 10},
            {headerAlign: "center", align: "center", allowSort: true, width: 50, dataType:"int", field: "id", header: "渠道ID"},
            {headerAlign: "center", align: "center", allowSort: true, width: 50, field: "name", header: "渠道名称"}
        ];
        grid.set({columns: columns});
        grid.load();

        function getSelectData() {
            var rows = grid.getSelecteds();
            var ids = [], texts = [];
            for (var i = 0, l = rows.length; i < l; i++) {
                var row = rows[i];
                ids.push(row.id);
                texts.push(row.name);
            }
            var data = {};
            data.id = ids.join(",");
            data.name = texts.join(",");
            return data;
        }

        function CloseWindow(action) {
            if (window.CloseOwnerWindow) return window.CloseOwnerWindow(action);
            else window.close();
        }

        function onOk() {
            CloseWindow("ok");
        }

        function onCancel() {
            CloseWindow("cancel");
        }
    </script>
</body>
</html>