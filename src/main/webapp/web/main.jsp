<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%
    String path = request.getContextPath();
    String basePath = request.getScheme()
            + "://" + request.getServerName()
            + ":" + request.getServerPort()
            + path + "/";
%>
<html>
<head>
    <title>账号中心</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <base href="<%=basePath%>">

    <script type="text/javascript" src="${pageContext.request.contextPath}/js/import.js"></script>

    <style type="text/css">
        body {
            margin: 0;
            padding: 0;
            border: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
        }

    </style>
</head>
<body>

<div id="layout1" class="mini-layout" style="width:100%;height:100%;">
    <div class="header" region="north" height="70" showSplit="false" showHeader="false">
        <h1 style="margin:0;padding:15px;cursor:default;font-family:微软雅黑,黑体,宋体;">账号中心</h1>
        <div style="position:absolute;top:12px;right:10px;">
            <a class="mini-button mini-button-iconTop" iconCls="icon-close" onclick="onQuitClick" plain="true">退出</a>
        </div>

    </div>
    <div title="south" region="south" showSplit="false" showHeader="false" height="30">
        <div style="line-height:28px;text-align:center;cursor:default">Copyright © 提塔利克版权所有</div>
    </div>
    <div title="center" region="center" style="border:0;" bodyStyle="overflow:hidden;">
        <div class="mini-splitter" style="width:100%;height:100%;" borderStyle="border:0;">
            <div size="180" maxSize="250" minSize="100" showCollapseButton="true" style="border:0;">
                <div id="leftTree" class="mini-outlooktree" url="function_list" onnodeclick="onNodeSelect"
                     textField="text" idField="id" parentField="pid">
                </div>

            </div>
            <div showCollapseButton="false" style="border:0;">
                <!--Tabs-->
                <div id="mainTabs" class="mini-tabs" activeIndex="2" style="width:100%;height:100%;"
                     plain="false" onactivechanged="onTabsActiveChanged">
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">

    mini.parse();
    var tree = mini.get("leftTree");

    function onNodeSelect(e) {
        var node = e.node;
        var isLeaf = e.isLeaf;

        if (isLeaf) {
            showTab(node);
        }
    }

    function showTab(node) {
        var tabs = mini.get("mainTabs");

        var id = "tab$" + node.id;
        var tab = tabs.getTab(id);
        if (!tab) {
            tab = {};
            tab._nodeid = node.id;
            tab.name = id;
            tab.title = node.text;
            tab.showCloseButton = true;


            tab.url = "./web/" + node.pagePath;

            tabs.addTab(tab);
        }
        tabs.activeTab(tab);
    }

    function onQuitClick(e) {
        $.ajax({
            url: "user_logout",
            type: "post",
            success: function (text) {
                window.location = "web/login.jsp";
            }
        });
    }

    function onTabsActiveChanged(e) {
        var tabs = e.sender;
        var tab = tabs.getActiveTab();
        if (tab && tab._nodeid) {

            var node = tree.getNode(tab._nodeid);
            if (node && !tree.isSelectedNode(node)) {
                tree.selectNode(node);
            }
        }
    }

</script>


</body>
</html>