<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%
    String path = request.getContextPath();
    String basePath = request.getScheme()
            + "://" + request.getServerName()
            + ":" + request.getServerPort()
            + path + "/";
%>
<html>
<head>
    <title>登录账号中心</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <base href="<%=basePath%>">

    <script type="text/javascript" src="${pageContext.request.contextPath}/js/import.js"></script>
</head>
<body>
    <div id="loginWindow" class="mini-window" title="用户登录" style="width:350px;height:180px;" showModal="true" showCloseButton="false">
        <div id="loginForm" style="padding-left:45px;padding-top:13px;">
            <table>
                <tr>
                    <td style="width:60px;"><label for="username$text">帐号：</label></td>
                    <td>
                        <input id="username" name="username" onvalidation="onUserNameValidation" class="mini-textbox"
                               required="true" style="width:150px;"/>
                    </td>
                </tr>
                <tr>
                    <td style="width:60px;"><label for="password$text">密码：</label></td>
                    <td>
                        <input id="password" name="password" onvalidation="onPwdValidation" class="mini-password"
                               requiredErrorText="密码不能为空" required="true" style="width:150px;" onenter="onLoginClick"/>
                    </td>
                </tr>
                <tr>
                    <td></td>
                    <td style="padding-top:5px;">
                        <a onclick="onLoginClick" class="mini-button" style="width:60px;">登录</a>
                        <a onclick="onResetClick" class="mini-button" style="width:60px;">重置</a>
                    </td>
                </tr>
            </table>
        </div>
    </div>

    <script type="text/javascript">
        mini.parse();

        var loginWindow = mini.get("loginWindow");
        loginWindow.show();

        function onLoginClick(e) {
            var form = new mini.Form("#loginWindow");

            form.validate();
            if (form.isValid() === false) {
                mini.alert("输入有误，请检查");
                return;
            }
            $.ajax({
                url: "user_login",
                type: "post",
                data: {
                    username: mini.get("username").value,
                    password: mini.get("password").value
                },
                complete : function (xhr, text) {
                    if(xhr.responseText === "success") {
                        loginWindow.hide();
                        mini.loading("登录成功，马上转到系统...", "登录成功");
                        setTimeout(function () {
                            window.location = "web/main.jsp";
                        }, 1500);
                    }else if(xhr.responseText === "invalid") {
                        mini.alert("登录失败，账号密码错误");
                    }else if(xhr.responseText === "notexist") {
                        mini.alert("账号不存在，请联系管理员");
                    }else {
                        window.location = "web/login.jsp";
                    }
                }
            });

        }

        function onResetClick(e) {
            var form = new mini.Form("#loginWindow");
            form.clear();
        }

        function onUserNameValidation(e) {
            if (e.isValid) {
                if (e.value.length < 1) {
                    e.errorText = "必须输入账号";
                    e.isValid = false;
                }
            }
        }
        function onPwdValidation(e) {
            if (e.isValid) {
                if (e.value.length < 1) {
                    e.errorText = "必须输入密码";
                    e.isValid = false;
                }
            }
        }
    </script>
</body>
</html>