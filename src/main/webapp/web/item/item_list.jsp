<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%
    String path = request.getContextPath();
    String basePath = request.getScheme()
            + "://" + request.getServerName()
            + ":" + request.getServerPort()
            + path + "/";
%>
<html>
<head>
    <title>奖励列表</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <base href="<%=basePath%>">

    <script type="text/javascript" src="${pageContext.request.contextPath}/js/import.js"></script>
</head>
<body>
<div id="sendForm">
    <fieldset>
        <legend>货币奖励</legend>
        <button onclick="insertCurrency()" class="mini-button" style="width:5%">添加</button>
        <br>
        <div id="currency">
            &nbsp; &nbsp;
            <label class="mini-label" style="width:20%">数值类型:</label>&nbsp;
            <input id="currencyType1" class="mini-combobox" style="width:8%" textField="desc" valueField="param"
                   url="item_list?dropType=currency" allowInput="true" onvalidation="onComboValidation"/>&nbsp;
            <label class="mini-label">数量:</label>&nbsp;
            <input name="currencyCount1" id="currencyCount1" class="mini-textbox" style="width:8%"/>&nbsp;
            <span class="separator"></span>
        </div>
    </fieldset>

    <fieldset>
        <legend>物品奖励</legend>
        <button onclick="insertItem()" class="mini-button" style="width:5%">添加</button>
        <br>
        <div id="item">
            &nbsp;&nbsp;
            <label class="mini-label" style="width:20%">物品类型:</label>&nbsp;
            <input id="itemType1" name="itemType1" class="mini-combobox" style="width:8%" textField="text"
                   valueField="id"
                   url="item_type_list" onvaluechanged="loadItem(1)" allowInput="true"
                   onvalidation="onComboValidation"/>&nbsp;
            <label class="mini-label">物品:</label>&nbsp;
            <input id="itemId1" class="mini-combobox" style="width:18%" textField="desc" valueField="param"
                   allowInput="true" onvalidation="onComboValidation"/>&nbsp;
            <label class="mini-label" id="itemLabel1">数量:</label>&nbsp;
            <input name="itemCount1" id="itemCount1" class="mini-textbox" style="width:8%"/>&nbsp;
            <span class="separator"></span>
        </div>
    </fieldset>

    <br>
    <br>
    <div align="center">
        <button onclick="onOk()" class="mini-button" style="width:5%">提交</button>&nbsp;&nbsp;
        <button onclick="onCancel()" class="mini-button" style="width:5%">关闭</button>
    </div>
</div>

<script type="text/javascript">

    mini.parse();

    var currencyCount = 1;
    var itemCount = 1;

    /**
     * 添加货币
     */
    function insertCurrency() {
        currencyCount++;
        var insertHtml = '            &nbsp; &nbsp;\n' +
            '            <label class="mini-label" style="width:20%">数值类型:</label>&nbsp;\n' +
            '            <input id="currencyType' + currencyCount + '" class="mini-combobox" style="width:8%" textField="desc" valueField="param"\n' +
            '                   url="item_list?dropType=currency" allowInput="true" onvalidation="onComboValidation"/>&nbsp;\n' +
            '            <label class="mini-label">数量:</label>&nbsp;\n' +
            '            <input name="currencyCount' + currencyCount + '" id="currencyCount' + currencyCount + '" class="mini-textbox" style="width:8%"/>&nbsp;\n' +
            '            <span class="separator"></span>';
        if (currencyCount % 3 === 0) {
            insertHtml += "<br><br>";
        }
        $("#currency").append(insertHtml);

        mini.parse();
    }

    /**
     * 添加物品
     */
    function insertItem() {
        itemCount++;
        var insertHtml = '            &nbsp;&nbsp;\n' +
            '            <label class="mini-label" style="width:20%">物品类型:</label>&nbsp;\n' +
            '            <input id="itemType' + itemCount + '" name="itemType' + itemCount + '" class="mini-combobox" style="width:8%" textField="text" valueField="id"\n' +
            '                   url="item_type_list" onvaluechanged="loadItem(' + itemCount + ')" allowInput="true" onvalidation="onComboValidation"/>&nbsp;\n' +
            '            <label class="mini-label">物品:</label>&nbsp;\n' +
            '            <input id="itemId' + itemCount + '" class="mini-combobox" style="width:18%" textField="desc" valueField="param"\n' +
            '                   allowInput="true" onvalidation="onComboValidation"/>&nbsp;\n' +
            '            <label class="mini-label" id="itemLabel' + itemCount + '">数量:</label>&nbsp;\n' +
            '            <input name="itemCount' + itemCount + '" id="itemCount' + itemCount + '" class="mini-textbox" required="true" style="width:8%"/>&nbsp;\n' +
            '            <span class="separator"></span>';
        if (itemCount % 2 === 0) {
            insertHtml += "<br><br>";
        }
        $("#item").append(insertHtml);

        mini.parse();
    }

    /**
     * 动态加载物品数据
     */
    function loadItem(count) {
        var itemType = mini.get("itemType" + count).getValue();
        mini.get("itemId" + count).setUrl("item_list?itemType=" + itemType);
        if (itemType === "starHero") {
            $("#itemLabel" + count).text("星级:");
        } else {
            $("#itemLabel" + count).text("数量:");
        }
        var id = "itemCount" + count;
        var input = mini.get(id);
        if (itemType === "divinesoul" || itemType === "hero" || itemType === "starHero") {
            input.setValue(1);
            input.setAllowInput(false);
        } else {
            input.setValue("");
            input.setAllowInput(true);
        }
    }

    /**
     * 物品类型，需配置
     */
    function getItemType() {
        var type = [
            {"id": "normalItem", "text": "普通物品", "value": "0"},
            {"id": "hero", "text": "英雄", "value": "0"},
            {"id": "starHero", "text": "星级英雄", "value": "10001"},
            {"id": "head", "text": "头像", "value": "10003"},
            {"id": "headFrame", "text": "头像框", "value": "10004"},
            {"id": "fashion", "text": "时装", "value": "10005"},
            {"id": "chip", "text": "碎片", "value": "0"},
            {"id": "divinesoul", "text": "战魂", "value": "10006"},
        ];
        return type;
    }

    /**
     * 根据ID获取物品类型数据
     */
    function getItemTypeValue(id) {
        var arr = getItemType();
        for (var j = 0, len = arr.length; j < len; j++) {
            if (arr[j].id === id) {
                return arr[j];
            }
        }
    }

    /**
     * 获取物品数据
     */
    function getItemData() {
        var data = {};
        var list = [];
        var currencyStr = "";
        var itemStr = "";
        var rewardStr = "";
        getItemType
        var flag = true;

        for (var i = 1; i <= currencyCount; i++) {
            var itemData = {};
            itemData.type = "currency";
            itemData.param = mini.get("currencyType" + i).getValue();
            itemData.itemText = mini.get("currencyType" + i).getText();
            itemData.count = mini.get("currencyCount" + i).getValue();
            if (itemData.count > 0 && itemData.param != "") {
                if (flag) {
                    flag = false;
                    currencyStr = "货币:  ";
                }
                list.add(itemData);
                currencyStr += itemData.itemText + " X " + itemData.count + "  |  ";
                rewardStr += "," + itemData.param + "|-1|" + itemData.count;
            }
        }

        flag = true;

        for (var i = 1; i <= itemCount; i++) {
            var itemData = {};
            itemData.type = "item";
            itemData.param = mini.get("itemId" + i).getValue();
            itemData.itemText = mini.get("itemId" + i).getText();
            itemData.count = mini.get("itemCount" + i).getValue();
            var typeData = getItemTypeValue(mini.get("itemType" + i).getValue());
            if (itemData.count > 0 && typeData.value != "" && itemData.param != "") {
                if (flag) {
                    flag = false;
                    itemStr = "物品:  ";
                }
                list.add(itemData);
                itemStr += typeData.text + "." + itemData.itemText + " X " + itemData.count + "  |  ";
                rewardStr += "," + typeData.value + "|" + itemData.param + "|" + itemData.count;
            }
        }

        data.itemData = list;
        data.currencyStr = currencyStr;
        data.itemStr = itemStr;

        data.rewardStr = rewardStr.substring(1);

        return data;
    }


    function onOk() {
        var form = new mini.Form("#sendForm");

        form.validate();
        if (form.isValid() === false) {
            mini.alert("输入有误，请检查");
            return;
        }
        CloseWindow("ok");
    }

    function onCancel() {
        CloseWindow("cancel");
    }

    function CloseWindow(action) {
        if (window.CloseOwnerWindow) return window.CloseOwnerWindow(action);
        else window.close();
    }

    function onComboValidation(e) {
        if (e.value == "") {
            return;
        }
        var items = this.findItems(e.value);
        if (!items || items.length == 0) {
            e.isValid = false;
            e.errorText = "输入值不在下拉数据中";
        }
    }

</script>
</body>
</html>