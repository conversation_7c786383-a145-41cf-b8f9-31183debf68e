<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%
    String path = request.getContextPath();
    String basePath = request.getScheme()
            + "://" + request.getServerName()
            + ":" + request.getServerPort()
            + path + "/";
%>
<html>
<head>
    <title>用户信息</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <base href="<%=basePath%>">

    <script type="text/javascript" src="${pageContext.request.contextPath}/js/import.js"></script>
</head>
<body>
    <div style="width:100%;">
        <div class="mini-toolbar" style="border-bottom:0;padding:0px;">
            <table style="width:100%;">
                <tr>
                    <td style="white-space:nowrap;">
                        <a class="mini-button" iconCls="icon-addnew" onclick="add()">新增</a>
                        <span class="separator"></span>
                        <a class="mini-button" iconCls="icon-edit" onclick="edit()">编辑</a>
                        <span class="separator"></span>
                        <a class="mini-button" iconCls="icon-remove" onclick="remove()">删除</a>
                        <span class="separator"></span>
                    </td>
                </tr>
            </table>
        </div>
    </div>

    <div id="userList" class="mini-datagrid" style="width:100%;height:96%;"
         url="user_list" allowCellEdit="true" allowCellSelect="true" sortMode="client" multiSelect="false">
        <div property="columns">
            <div name="username"  field="username" headerAlign="center" allowSort="true" width="20" >
                用户名
            </div>
            <div name="password"  field="password" headerAlign="center" allowSort="true">
                密码
            </div>
            <div name="authority" field="authority" headerAlign="center" allowSort="true" width="150" >
                权限
            </div>
            <div name="authorityIds" field="authorityIds" headerAlign="center" allowSort="true" >
                权限ID
            </div>
        </div>
    </div>

    <script type="text/javascript">

        mini.parse();

        var grid = mini.get("userList");
        grid.hideColumn("password");
        grid.hideColumn("authorityIds");
        grid.load();

        /**
         * 删除
         */
        function remove() {
            var rows = grid.getSelecteds();
            if(rows) {
                if (!confirm("确定要删除记录吗？")) {
                    return ;
                }

                var usernames = [];
                for(var i=0;i<rows.length;i++) {
                    usernames[i] = rows[i].username;
                }

                $.ajax({
                    url: "user_remove",
                    type: "post",
                    data: {
                        username: usernames
                    },
                    traditional: true,
                    success: function (text) {
                        if(text === "success"){
                            mini.alert("删除成功");
                            grid.reload();
                        }else {
                            mini.alert(text);
                        }
                    }
                });
            } else {
                mini.alert("请选中一条信息");
            }
        }

        /**
         * 添加用户
         */
        function add() {
            mini.open({
                url: "web/user/user_update.jsp",
                title: "新增用户",
                width : 600,
                height : 480,
                allowResize: false,
                onload: function () {
                    var iframe = this.getIFrameEl();
                    var data = {
                        action: "add"
                    };
                    iframe.contentWindow.setUserData(data);
                },
                ondestroy: function (action) {
                    if (action === "success") {
                        mini.alert("添加成功");
                        grid.reload();
                    }
                }
            });
        }

        /**
         * 修改
         */
        function edit() {
            var row = grid.getSelected();
            if(row) {
                mini.open({
                    url: "web/user/user_update.jsp",
                    title: "修改用户",
                    width : 600,
                    height : 480,
                    allowResize: false,
                    onload: function () {
                        var iframe = this.getIFrameEl();
                        var data = {
                            action: "edit",
                            username: row.username,
                            password: row.password,
                            authority: row.authority,
                            authorityIds: row.authorityIds
                        };
                        iframe.contentWindow.setUserData(data);
                    },
                    ondestroy: function (action) {
                        grid.reload();

                    }
                });
            } else {
                alert("请选中一条信息");
            }

        }

    </script>
</body>
</html>