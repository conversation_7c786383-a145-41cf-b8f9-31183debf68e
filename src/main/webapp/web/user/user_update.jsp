<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%
    String path = request.getContextPath();
    String basePath = request.getScheme()
            + "://" + request.getServerName()
            + ":" + request.getServerPort()
            + path + "/";
%>
<html>
<head>
    <title>用户信息</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <base href="<%=basePath%>">

    <script type="text/javascript" src="${pageContext.request.contextPath}/js/import.js"></script>
</head>
<body>
    <div id="sendForm">
        <br>
        <br>
        <br>
        <table style="width: 100%; table-layout: fixed;" border="0">
            <tr>
                <td align="right">用户名：</td>
                <td colspan="2">
                    <input name="username" id="username" class="mini-textbox" width="70%"
                           height="100px" required="true"/>
                </td>
            </tr>
            <tr>
                <td><br></td>
                <td><br></td>
            </tr>
            <tr>
                <td align="right">密码：</td>
                <td colspan="2">
                    <input name="password" id="password" class="mini-password" emptyText="请输入密码" width="70%"
                           height="100px" required="true"/>
                </td>
            </tr>
            <tr>
                <td><br></td>
                <td><br></td>
            </tr>
            <tr>
                <td align="right">权限：</td>
                <td colspan="2">
                    <input name="authority" id="authority" class="mini-buttonedit" onbuttonclick="selectAuthority"
                           width="70%" allowInput="false"/>
                </td>
            </tr>
        </table>
        <br>
        <br>
        <button onclick="submitForm()" class="mini-button" style="width:10%;position:fixed;bottom:30px;left:49%">提交</button>
    </div>

    <script type="text/javascript">

        mini.parse();
        var oldPassword;

        /**
         * 提交数据
         */
        function submitForm() {
            var form = new mini.Form("#sendForm");

            form.validate();
            if (form.isValid() === false) {
                mini.alert("输入有误，请检查");
                return;
            }

            var sendData = {};
            sendData.username = mini.get("username").value;
            sendData.password = mini.get("password").value;
            sendData.authority = mini.get("authority").text;
            sendData.authorityIds = mini.get("authority").value;
            var json = mini.encode(sendData);

            //提交数据
            $.ajax({
                url: "user_update",
                type: "post",
                data: {
                    data: json,
                    passwordChange: oldPassword !== mini.get("password").value
                },
                success: function (text) {
                    if (text === "success") {
                        window.CloseOwnerWindow("success");
                    } else {
                        mini.alert(text);
                    }
                }
            });
        }

        /**
         * 修改设置数据
         */
        function setUserData(data) {
            //跨页面传递的数据对象，克隆后才可以安全使用
            var data = mini.clone(data);
            optType = data.action;
            if (data.action === "edit") {
                mini.get("username").setAllowInput(false);

                mini.get("username").setValue(data.username);
                mini.get("password").setValue(data.password);
                oldPassword = data.password;
                mini.get("authority").setText(data.authority);
                mini.get("authority").setValue(data.authorityIds);
            } else {
                mini.get("username").setAllowInput(true);
            }
        }

        /**
         * 权限选择树
         */
        function selectAuthority() {
            var btnEdit = this;
            mini.open({
                url: "web/user/authority_tree.jsp",
                showMaxButton: false,
                title: "权限选择树",
                width: 350,
                height: 350,
                onload: function () {
                    var iframe = this.getIFrameEl();
                    var data = {
                        authority: mini.get("authority").value
                    };
                    iframe.contentWindow.setAuthorityTree(data);
                },
                ondestroy: function (action) {
                    if (action === "ok") {
                        var iframe = this.getIFrameEl();
                        var data = iframe.contentWindow.GetData();
                        data = mini.clone(data);
                        if (data) {
                            btnEdit.setValue(data.id);
                            btnEdit.setText(data.text);
                        }
                    }
                }
            });
        }

    </script>
</body>
</html>