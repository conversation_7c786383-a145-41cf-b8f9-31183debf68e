<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%
    String path = request.getContextPath();
    String basePath = request.getScheme()
            + "://" + request.getServerName()
            + ":" + request.getServerPort()
            + path + "/";
%>
<html>
<head>
    <title>权限树</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <base href="<%=basePath%>">

    <script type="text/javascript" src="${pageContext.request.contextPath}/js/import.js"></script>
</head>
<body>
    <div class="mini-fit">

        <ul id="tree1" class="mini-tree" style="width:100%;height:100%;"
            showTreeIcon="true" textField="text" idField="id" parentField="pid" resultAsTree="false"
            showCheckBox="true" checkRecursive="true"
            expandOnLoad="true" allowSelect="false" enableHotTrack="false">
        </ul>

    </div>
    <div class="mini-toolbar" style="text-align:center;padding-top:8px;padding-bottom:8px;"
         borderStyle="border-left:0;border-bottom:0;border-right:0;">
        <a class="mini-button" style="width:60px;" onclick="onOk()">确定</a>
        <span style="display:inline-block;width:25px;"></span>
        <a class="mini-button" style="width:60px;" onclick="onCancel()">取消</a>
    </div>

    <script type="text/javascript">
        mini.parse();

        var tree = mini.get("tree1");


        tree.load("function_list?type=authorityTree");

        function GetCheckedNodes() {
            var nodes = tree.getCheckedNodes();
            return nodes;
        }
        function GetData() {
            var nodes = tree.getCheckedNodes();
            var ids = [], texts = [];
            for (var i = 0, l = nodes.length; i < l; i++) {
                var node = nodes[i];
                if(tree.isLeaf(node) === true){
                    ids.push(node.id);
                    texts.push(node.text);
                }
            }
            var data = {};
            data.id = ids.join(",");
            data.text = texts.join(",");
            return data;
        }
        function search() {
            var key = mini.get("key").getValue();
            //grid.load({ key: key });
        }
        function onKeyEnter(e) {
            search();
        }
        //////////////////////////////////
        function CloseWindow(action) {
            if (window.CloseOwnerWindow) return window.CloseOwnerWindow(action);
            else window.close();
        }
        function onOk() {
            var node = tree.getSelectedNode();
            if (node && tree.isLeaf(node) === false) {
                alert("不能选中父节点");
                return;
            }

            CloseWindow("ok");
        }
        function onCancel() {
            CloseWindow("cancel");
        }


        function setAuthorityTree(data) {
            //跨页面传递的数据对象，克隆后才可以安全使用
            var authorityData = mini.clone(data);
            tree.setValue(authorityData.authority);
        }
    </script>
</body>
</html>