body
{
    font-size:14px;
    line-height:1.4;    
}

/* button
==========================================*/
.mini-button
{
    font-size:13px;
    line-height:16px;
}
.mini-button-text
{   
    padding:5px 12px 7px 12px;
    padding:6px 12px 6px 12px\9;
    line-height:16px;
}
.mini-button-icon
{
    left:8px;    
}
.mini-button .mini-button-icon-text
{
    padding-left:28px;
}
.mini-button .mini-button-icon-only
{
    padding-left:18px;
}
.mini-button .mini-button-icon-only .mini-button-icon
{    
    left:8px;    
}
.mini-button-iconRight .mini-button-icon-text
{
    padding-left:12px;
    padding-right:28px;
}
.mini-button-iconRight .mini-button-icon
{
    left:auto;
    right:8px;
}
.mini-button-iconTop .mini-button-icon-text
{
    padding-left:12px;
    padding-top:28px;
}
.mini-button-iconTop .mini-button-icon
{
    top:6px;
}
.mini-button-allow
{
    margin-top:7px;    
    left:-3px;    
}

/* textbox
==========================================*/
.mini-textbox-input
{
    height:28px;
    line-height:28px;
    font-size:14px;
}
.mini-textbox-border
{
    padding-left:8px;
    padding-right:8px;
    height:28px;
}
.mini-textbox
{
    overflow:visible;
    height:30px;
    width:150px;
}
.mini-textarea .mini-textbox-border
{
    padding-left:8px;
    padding-right:0px;
}
.mini-textarea .mini-textbox-input
{
    line-height:20px;
}

/* buttonedit
==========================================*/
.mini-buttonedit-input
{
    height:28px;
    line-height:28px;
    font-size:14px;
}
.mini-buttonedit-border
{
    padding-left:6px;
    padding-right:25px;
    height:28px;
}
.mini-buttonedit
{
    overflow:visible;
    height:30px;
    width:150px;
}
.mini-buttonedit-button
{
    height:28px;
    width:22px;
}
.mini-buttonedit-close
{
    height:24px;
}
/*.mini-buttonedit-up span, .mini-buttonedit-down span
{
    background-position:50% 50%;
}*/

/* htmlfile
==========================================*/
.mini-htmlfile .mini-buttonedit-button
{
    line-height:24px;
}

/* textboxlist
==========================================*/
.mini-textboxlist
{
    height:30px;
}
.mini-textboxlist ul
{
    padding-top:3px;
    padding-left:8px;
}
.mini-textboxlist .mini-textboxlist-item
{
    font-size:14px;
    line-height:20px;
    height:20px;
}
.mini-textboxlist .mini-errorIcon
{
    margin-top:4px;
}
.mini-textboxlist .mini-textboxlist-input
{
    height:18px;
    line-height:18px;
}

/* checkbox
==========================================*/
.mini-checkbox
{
    font-size:14px;
    line-height:26px;
}
.mini-checkbox-check,
.mini-checkbox-icon
{
    margin-right:10px;
}

/* checkboxlist & radiobuttonlist
==========================================*/
.mini-checkboxlist table label,
.mini-radiobuttonlist table label
{
    padding-left:8px;
    line-height:22px;
    font-size:14px;
}

/* listbox
==========================================*/
.mini-listbox td
{
    line-height:20px;
    font-size:14px;
    padding: 6px 8px 6px 8px;
}

/* layout
==========================================*/
.mini-layout
{
    font-size:14px;
}
.mini-layout-region-header,
.mini-layout-proxy
{
    height:40px;
    line-height:40px;
}
.mini-layout-proxy
{
    width:40px;
}
.mini-layout-region-header .mini-tools,
.mini-layout-proxy .mini-tools
{
    top:12px;
    right:16px;
}
.mini-layout-proxy-text
{
    left:8px;
    font-size:14px;
}

/* calendar
==========================================*/
.mini-calendar,
.mini-calendar-title,
.mini-calendar-menu-month,
.mini-calendar-menu-year
{       
    font-size:14px;
}
.mini-calendar-header
{
    height:34px;
}
.mini-calendar-title
{
    top:4px;
}
.mini-calendar-prev,
.mini-calendar-next
{
    top:10px;
}
body .mini-calendar td.mini-calendar-date
{
    line-height:16px;
    padding:3px 8px;
}
body .mini-calendar .mini-calendar-daysheader td
{
    line-height:18px;
    padding:4px;
}
.mini-calendar-tadayButton, .mini-calendar-clearButton,
.mini-calendar-okButton, .mini-calendar-cancelButton
{
    line-height:16px;
    padding:6px;
    padding-left:0px;
    padding-right:0px;    
}
.mini-calendar-menu-year
{
    padding:2px 2px;
}

/* progressbar
==========================================*/
.mini-progressbar
{
    height:30px;   
}
.mini-progressbar-border
{    
    height:28px;    
    border-radius:4px;
}
.mini-progressbar-text
{
    line-height:28px;
    font-size:14px;
}


/* faary
==========================================*/
.iform {font: 12px/26px Verdana, Geneva, sans-serif; width:400px; margin:30px auto;}
.iform ul { margin:0; padding:0; list-style:none;}
.iform ul ul { overflow:auto}
.iform li { padding-bottom:5px;}
.iform label {
    width:130px; display:block; float:left; line-height:26px;
}
.iform label.ilabel {
    width:auto; display:inline; float:none; line-height:26px; padding:0 5px
}

.iform .itext,.iform .itextarea,.iform .iselect,.iform .ibutton {
    width:200px;
    border:1px solid #999;
    -webkit-border-radius: 3px;-khtml-border-radius:3px;-moz-border-radius:3px;border-radius:3px;
    margin:0;
    padding:5px;
    background: #fff;
    background: -webkit-gradient(linear, left top, left 25, from(#fff), color-stop(6%, #eee), to(#fff));
    background: -moz-linear-gradient(top, #fff, #eee 2px, #fff 25px);
    box-shadow: rgba(0,0,0, 0.1) 0px 0px 8px;
    -moz-box-shadow: rgba(0,0,0, 0.1) 0px 0px 8px;
    -webkit-box-shadow: rgba(0,0,0, 0.1) 0px 0px 8px;

}

.iform .itext:hover,.iform .itextarea:hover,.iform .iselect:hover,.iform .ibutton:hover,
.iform .itext:focus,.iform .itextarea:focus,.iform .iselect:focus,.iform .ibutton:focus{

    border-color: #333;
    background:#fff;

}

.iform .itext {

}


.iform .itextarea{
    height:100px; width: 250px;
}
.iform .ibutton {

    width:auto;
    background: #efefef;
    background: -webkit-gradient(linear, left top, left 25, from(#dadada), color-stop(6%, #efefef), to(#dadada));
    background: -moz-linear-gradient(top, #dadada, #efefef 2px, #dadada 25px);
    box-shadow: rgba(0,0,0, 0.1) 0px 0px 8px;

}
.iform .ibutton:hover,.iform .ibutton:focus { background:#dadada;}
.iform li.iheader { display:block; font-size:18px; border-bottom:1px solid #000; padding:5px; text-indent:10px; margin:5px 0 15px }
.iform li.iseparator { display:block; text-indent:-9999px; height:10px; line-height:10px; border-bottom:1px solid #999;margin:5px 0 15px }

.iform .required { border-color:#F00; }

#imessageOK,#imessageERROR{ border:1px solid #F60; padding:10px; font-size:16px; font-weight:bold; text-align: center; display:none; margin-bottom:20px;

    background: #F90;
    background: -webkit-gradient(linear, left top, left 25, from(#F90), color-stop(4%, #FC0), to(#F90));
    background: -moz-linear-gradient(top, #F90, #FC0 1px, #F90 25px);
    color:#fff;
}
