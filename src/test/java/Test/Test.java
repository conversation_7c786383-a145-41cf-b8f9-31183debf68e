package Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.gy.server.BillingServer;
import com.gy.server.gate.Gate;
import com.gy.server.utils.JsonUtil;

public class Test {
	public static void main(String[] args) {
		try {
//			BillingServer server = new BillingServer();
//			server.init();
//			server.startup();

			Map<String, Object> data = new HashMap<>();
//			List<Gate.JsonGate> gatesRst  = new ArrayList<>();
//			data.put("gates", gatesRst);
			System.out.println(JsonUtil.map2Json(data));
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
}
