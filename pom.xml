<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.gy.server</groupId>
  <artifactId>tl-billing</artifactId>
  <packaging>war</packaging>
  <version>0.0.1-SNAPSHOT</version>
  <name>tl-billing</name>
  <url>http://maven.apache.org</url>
  
  <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.build.encoding>UTF-8</project.build.encoding>
        <project.build.jdk>1.8</project.build.jdk>
        <maven.compiler.target>1.8</maven.compiler.target>
        <maven.compiler.source>1.8</maven.compiler.source>
  </properties>
  
  <dependencies>
      <dependency>
          <groupId>com.gy.server</groupId>
          <artifactId>commons-web</artifactId>
          <version>1.0-SNAPSHOT</version>
          <exclusions>
              <exclusion>
                  <artifactId>redisson-gy</artifactId>
                  <groupId>com.gy.server</groupId>
              </exclusion>
          </exclusions>
      </dependency>
      <dependency>
          <groupId>com.gy.server</groupId>
          <artifactId>tl-baselib</artifactId>
          <version>1.0-SNAPSHOT</version>
      </dependency>
      <dependency>
          <groupId>com.gy.server</groupId>
          <artifactId>utils</artifactId>
          <version>1.0-SNAPSHOT</version>
          <exclusions>
              <exclusion>
                  <artifactId>javassist</artifactId>
                  <groupId>org.javassist</groupId>
              </exclusion>
          </exclusions>
      </dependency>
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<version>3.8.1</version>
			<scope>test</scope>
		</dependency>
		<dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
            <version>3.1.0</version>
            <scope>provided</scope>
        </dependency>
      <dependency>
          <groupId>mysql</groupId>
          <artifactId>mysql-connector-java</artifactId>
          <version>8.0.27</version>
          <exclusions>
              <exclusion>
                  <groupId>com.google.protobuf</groupId>
                  <artifactId>protobuf-java</artifactId>
              </exclusion>
          </exclusions>
      </dependency>
        <dependency>
            <groupId>com.gy.server</groupId>
            <artifactId>tl-packet</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.gy.server</groupId>
		    <artifactId>net3.x</artifactId>
		    <version>1.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>commons-codec</artifactId>
                    <groupId>commons-codec</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-io</artifactId>
                    <groupId>commons-io</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>javassist</artifactId>
                    <groupId>org.javassist</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.auth0</groupId>
		    <artifactId>java-jwt</artifactId>
		    <version>3.10.3</version>
            <exclusions>
                <exclusion>
                    <artifactId>jackson-databind</artifactId>
                    <groupId>com.fasterxml.jackson.core</groupId>
                </exclusion>
            </exclusions>
        </dependency>

      <dependency>
          <groupId>com.qcloud</groupId>
          <artifactId>cos_api</artifactId>
          <version>5.6.24</version>
          <exclusions>
              <exclusion>
                  <artifactId>jackson-databind</artifactId>
                  <groupId>com.fasterxml.jackson.core</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>slf4j-api</artifactId>
                  <groupId>org.slf4j</groupId>
              </exclusion>
          </exclusions>
          <!--<exclusions>
              <exclusion>
                  <artifactId>jackson-databind</artifactId>
                  <groupId>com.fasterxml.jackson.core</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>slf4j-api</artifactId>
                  <groupId>org.slf4j</groupId>
              </exclusion>
          </exclusions>-->
      </dependency>
      <dependency>
          <groupId>com.tencentcloudapi</groupId>
          <artifactId>tencentcloud-sdk-java</artifactId>
          <!-- go to https://search.maven.org/search?q=tencentcloud-sdk-java and get the latest version. -->
          <!-- 请到 https://search.maven.org/search?q=tencentcloud-sdk-java 查询最新版本 -->
          <version>3.1.206</version>
          <!--<exclusions>
              <exclusion>
                  <artifactId>gson</artifactId>
                  <groupId>com.google.code.gson</groupId>
              </exclusion>
              &lt;!&ndash;     <exclusion>
                     <artifactId>okhttp</artifactId>
                     <groupId>com.squareup.okhttp</groupId>
                 </exclusion>
               &ndash;&gt;
          </exclusions>-->
      </dependency>

      <!-- https://mvnrepository.com/artifact/com.maxmind.geoip2/geoip2 -->
      <dependency>
          <groupId>com.maxmind.geoip2</groupId>
          <artifactId>geoip2</artifactId>
          <version>2.12.0</version>
          <exclusions>
              <exclusion>
                  <artifactId>jackson-annotations</artifactId>
                  <groupId>com.fasterxml.jackson.core</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>jackson-core</artifactId>
                  <groupId>com.fasterxml.jackson.core</groupId>
              </exclusion>
              <exclusion>
                  <artifactId>jackson-databind</artifactId>
                  <groupId>com.fasterxml.jackson.core</groupId>
              </exclusion>
          </exclusions>
      </dependency>
	</dependencies>
	 <!-- 仓库与插件仓库 -->
    <repositories>
        <repository>
            <id>company.server.lib.repository</id>
            <url>http://mvn.shangua.com:7888/nexus/repository/maven-public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </snapshots>
        </repository>
    </repositories>
    <pluginRepositories>
        <pluginRepository>
            <id>company.server.lib.pluginRepository</id>
            <url>http://mvn.shangua.com:7888/nexus/repository/maven-public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>


    <build>
        <finalName>tl-billing</finalName>

        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <excludes>
                    <exclude>**/*</exclude>
                </excludes>
            </resource>
        </resources>

        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <version>3.0.0</version>
                <configuration>
                    <archiveClasses>true</archiveClasses>
                    <webResources>
                        <resource>
                            <directory>src/main/resources</directory>
                            <targetPath>WEB-INF/classes</targetPath>
                            <filtering>true</filtering>
                        </resource>
                    </webResources>
                    <nonFilteredFileExtensions>
                        <nonFilteredFileExtension>mmdb</nonFilteredFileExtension>
                    </nonFilteredFileExtensions>
                </configuration>
            </plugin>

        </plugins>

    </build>

</project>
